local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local TeleportService = game:GetService('TeleportService')
local RunService = game:GetService('RunService')

local Client = require(ReplicatedStorage.Client)

local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Packets = require(ReplicatedStorage.Data.Packets)

local EnvironementController = {}


local function setCircleUnderCharacter()
	ClassExtension.Player.CharacterAdded(Players.LocalPlayer, function()
		Players.LocalPlayer.Character:WaitForChild'Humanoid'
		Players.LocalPlayer.Character:WaitForChild'HumanoidRootPart'
		
		local Circle = ReplicatedStorage.Assets.Visuals.CircleUnderCharacter:Clone()
		Circle.Parent = Players.LocalPlayer.Character
		
		Circle.CFrame = Players.LocalPlayer.Character:WaitForChild'HumanoidRootPart'.CFrame * CFrame.new(0, -2.9, 0)

		local weld = Instance.new('WeldConstraint', Circle)
		weld.Part0 = Circle
		weld.Part1 = Players.LocalPlayer.Character.HumanoidRootPart

		Circle.SurfaceGui.Circle.ImageColor3 = Color3.fromRGB(255, 255, 255)
	end)
end


local function setupNPCs()
	local UIController = Client.GetController('UIController')
	local DialogController = Client.GetController('DialogController')

	if Client.Immutable.SERVER_TYPE == 'Lobby' then
		local IdleAnim = Instance.new('Animation')
		IdleAnim.AnimationId = 'rbxassetid://84707419155609'
		
		local NPCFolder = workspace:WaitForChild'Map'.NPCs
		
		for _, v in NPCFolder:GetChildren() do
			local track: AnimationTrack = v.Humanoid.Animator:LoadAnimation(IdleAnim)
			track.Looped = true
			track:Play()
			
			if v:FindFirstChild('ProximityPrompt') then continue end
			
			local NPCPrompt: ProximityPrompt = script.NPCPrompt:Clone()
			NPCPrompt.Parent = v
			
			NPCPrompt.Triggered:Connect(function(p)
				if p == Players.LocalPlayer then
					local Page = Players.LocalPlayer.PlayerGui.LobbyUI.Pages:FindFirstChild(v.Name)
					if Page then
						if Client.Immutable.Dialogs[v.Name] then
							DialogController.SetDialog({
								Header = Client.Immutable.Dialogs[v.Name].Header:upper(),
								Description = Client.Immutable.Dialogs[v.Name].Description,
								Yes = {Text = 'OKAY', Callback = function()
									UIController.Open(Page)
								end},
								No = {Text = 'CANCEL', Callback = math.random}
							})
						else
							UIController.Open(Page)
						end
					elseif v.Name == 'AFK' then
						DialogController.SetDialog({
							Header = Client.Immutable.Dialogs[v.Name].Header:upper(),
							Description = Client.Immutable.Dialogs[v.Name].Description,
							Yes = {Text = 'OKAY', Callback = function()
								local afkPlaceId
								for placeId, placeName in Client.Immutable.Servers.PLACES[game.GameId] do
									if placeName == 'AFK' then
										afkPlaceId = placeId
										break
									end
								end

								TeleportService:Teleport(afkPlaceId)
							end},
							No = {Text = 'CANCEL', Callback = math.random}
						})
					end
				end
			end)
		end
	end
end


function EnvironementController._Start()
	task.defer(setupNPCs)
	task.defer(setCircleUnderCharacter)
end


function EnvironementController._Init()
	Packets.Time.GetPlayerTime.send({t = os.time(os.date('*t')), timeSent = workspace:GetServerTimeNow()})
end


return EnvironementController