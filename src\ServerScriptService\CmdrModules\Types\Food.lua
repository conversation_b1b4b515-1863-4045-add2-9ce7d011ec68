local ReplicatedStorage = game:GetService('ReplicatedStorage')

local Items = require(ReplicatedStorage.Data.Items)

local CombinedFoods = {}

for k, v in Items do
	if v.Type ~= 'Food' then continue end
	table.insert(CombinedFoods, k)
end


return function(cmdr)
	local Util = cmdr.Cmdr.Util

	local foodType = {
		Transform = function(text)
			local findCharacter = Util.MakeFuzzyFinder(CombinedFoods)
			return findCharacter(text)
		end,

		Validate = function(characters)
			return #characters > 0
		end,

		Autocomplete = function(characters)
			return Util.GetNames(characters)
		end,

		Parse = function(characters)
			return characters[1]
		end
	}

	cmdr:RegisterType('food', foodType)
	cmdr:RegisterType('foods', Util.MakeListableType(foodType))
end