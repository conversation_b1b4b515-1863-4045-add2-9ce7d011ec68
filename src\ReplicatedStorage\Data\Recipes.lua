--[=[
	Author: <PERSON><PERSON><PERSON> (Syveric)
	Recipes data
]=]

export type Recipe = {
	Ingredients: {string},
	Maps: {string},
	Image: string
}

return table.freeze{
	['Basic Burger'] = {
		Ingredients = {'Bun', 'Meat'},
		Maps = {'Restaurant'},
		Image = 'rbxassetid://83475613934523'
	},
	
	['Cheese Burger'] = {
		Ingredients = {'Bun', 'Meat', 'Cheese'},
		Maps = {'Restaurant'},
		Image = 'rbxassetid://94140366810485'
	},
	
	['Lettuce Burger'] = {
		Ingredients = {'Bun', 'Meat', 'Lettuce'},
		Maps = {'Restaurant'},
		Image = 'rbxassetid://99986849526868'
	},
	
	['Lettuce Tomato Burger'] = {
		Ingredients = {'Bun', 'Meat', 'Lettuce', 'Tomato'},
		Maps = {'Restaurant'},
		Image = 'rbxassetid://94803002236944'
	},
	
	['Salad'] = {
		Ingredients = {'Lettuce', 'Tomato'},
		Maps = {},
		Image = 'rbxassetid://91755550649010'
	},

	['Plain Salad'] = {
		Ingredients = {'Lettuce'},
		Maps = {},
		Image = 'rbxassetid://132512673195529'
	},
	
	['Tofu'] = {
		Ingredients = {'Tofu'},
		Maps = {'Sky'},
		Image = 'rbxassetid://96009033071999'
	},
	
	['Luffy Meat'] = {
		Ingredients = {'LuffyMeat'},
		Maps = {'Sky'},
		Image = 'rbxassetid://132687833150721'
	},
	
	['Coffee'] = {
		Ingredients = {'Coffee Bean'},
		Maps = {'Ghoul Cafe'},
		Image = 'rbxassetid://119430324030637'
	},
	
	['Rice Onigiri'] = {
		Ingredients = {'Nori', 'Rice'},
		Maps = {'Infinite Castle'},
		Image = 'rbxassetid://102216635570001'
	},
	
	['Soup'] = {
		Ingredients = {'Carrot', 'Potato'},
		Maps = {'Infinite Castle'},
		Image = 'rbxassetid://95550428929565'
	},
	
	['Sandwich'] = {
		Ingredients = {'Bread', 'Ham'},
		Maps = {'Ghoul Cafe'},
		Image = 'rbxassetid://87133438926151'
	}
} :: { [string]: Recipe }