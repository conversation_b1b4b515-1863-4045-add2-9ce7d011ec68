local ReplicatedStorage = game:GetService('ReplicatedStorage')
local UserInputService = game:GetService('UserInputService')
local Players = game:GetService('Players')
local StarterGui = game:GetService('StarterGui')

local Signal = require(ReplicatedStorage.SharedModules.Signal)

local InputController = {
	Inputs = {},
	Binders = {},
	AlternativeInputs = {},

	Preferred = 'Keyboard',
	PreferredChanged = Signal.new()
}


local MOBILE_TOUCH, PC_CLICK = Enum.UserInputType.Touch, Enum.UserInputType.MouseButton1

local mouse = Players.LocalPlayer:GetMouse()

local function bindPreferredDeviceType()
	local function ChangePreferred(newPreferred)
		if InputController.Preferred ~= newPreferred then
			InputController.Preferred = newPreferred
			InputController.PreferredChanged:Fire(newPreferred)

			UserInputService.MouseIconEnabled = newPreferred == 'Mouse' or newPreferred == 'Keyboard'
		end
	end

	local function update()
		if game:GetService'GuiService':IsTenFootInterface() or UserInputService.GamepadEnabled then
			ChangePreferred('Gamepad')
		elseif UserInputService.TouchEnabled and not UserInputService.MouseEnabled then
			ChangePreferred('Touch')
		else
			ChangePreferred('Keyboard')
		end
	end
	
	task.defer(update)
	
	UserInputService.GamepadDisconnected:Connect(update)
	UserInputService.GamepadConnected:Connect(update)

	UserInputService.LastInputTypeChanged:Connect(function(lastInputType: Enum.UserInputType)
		if lastInputType.Name:match'^Mouse' then
			ChangePreferred('Mouse')
		elseif lastInputType == Enum.UserInputType.Keyboard or lastInputType == Enum.UserInputType.TextInput then
			ChangePreferred('Keyboard')
		elseif lastInputType.Name:match'^Gamepad' then
			ChangePreferred('Gamepad')
		elseif lastInputType == Enum.UserInputType.Touch then
			ChangePreferred('Touch')
		end
	end)
end


local function setupAlternativeInputs()
	InputController.AlternativeInputs.TapOrClick = Signal.new()
	
	InputController.Inputs.Mouse.LeftUp:Connect(function()
		InputController.AlternativeInputs.TapOrClick:Fire()
	end)
	
	UserInputService.TouchTapInWorld:Connect(function()
		task.wait() -- so that the mouse position can be updated on mobile
		InputController.AlternativeInputs.TapOrClick:Fire()
	end)
	
	UserInputService.InputBegan:Connect(function(input: InputObject, processed: boolean)
		if input.KeyCode == Enum.KeyCode.ButtonX then
			InputController.AlternativeInputs.TapOrClick:Fire()
		end
	end)
end


function InputController._Start()
	UserInputService.InputBegan:Connect(function(input: InputObjec, processed: boolean)
		if UserInputService:GetFocusedTextBox() then return end
		
		for k, v in InputController.Binders do
			if (v.Key == input.KeyCode or v.GamepadKey == input.KeyCode) and v.KeyDown then
				v.KeyDown()
			end
		end
	end)
	
	UserInputService.InputEnded:Connect(function(input: InputObject, processed: boolean)
		if UserInputService:GetFocusedTextBox() then return end
		
		for k, v in InputController.Binders do
			if (v.Key == input.KeyCode or v.GamepadKey == input.KeyCode) and v.KeyUp then
				v.KeyUp()
			end
		end
	end)
	
	StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.PlayerList, false)
	
	while true do
		if pcall(StarterGui.SetCore, StarterGui, 'ResetButtonCallback', false) then break end
		task.wait(1)
	end
end


function InputController._Init()
	InputController.Inputs.Gamepad = require(script.Inputs.Gamepad)
	InputController.Inputs.Keyboard = require(script.Inputs.Keyboard)
	InputController.Inputs.Mobile = require(script.Inputs.Mobile)
	InputController.Inputs.Mouse = require(script.Inputs.Mouse)

	for k, v in InputController.Inputs do
		v.Init()
		task.defer(v.Start)
	end
	
	for _, v in script.Binders:GetDescendants() do
		if v:IsA'ModuleScript' then
			InputController.Binders[v.Name] = require(v)
			if InputController.Binders[v.Name]._Init then
				InputController.Binders[v.Name]._Init()
			end
		end
	end

	bindPreferredDeviceType()
	setupAlternativeInputs()
end


return InputController