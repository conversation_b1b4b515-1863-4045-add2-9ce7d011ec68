local ReplicatedStorage = game:GetService('ReplicatedStorage')
local RunService = game:GetService('RunService')
local CollectionService = game:GetService('CollectionService')
local Players = game:GetService('Players')
local Debris = game:GetService('Debris')
local SoundService = game:GetService('SoundService')

local Client = require(ReplicatedStorage.Client)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local Math = require(ReplicatedStorage.SharedModules.Math)
local Packets = require(ReplicatedStorage.Data.Packets)

local Dash = {
	Key = Enum.KeyCode.Q,

	GamepadKey = Enum.KeyCode.ButtonL3,
	
	DASH_LEN = .42,
	DASH_POWER = 75,
	DASH_COOLDOWN = 2
}


local function canDoDash(): boolean
	return (
		Players.LocalPlayer.DevComputerMovementMode ~= Enum.DevComputerMovementMode.Scriptable
			or Players.LocalPlayer.DevTouchMovementMode  ~= Enum.DevTouchMovementMode.Scriptable
	)
end


local function dashVisual(character: Model)
	local DashEffectSmoke = ReplicatedStorage.Assets.Visuals.Dash.Dash:Clone()
	DashEffectSmoke.CFrame = CFrame.new(0, -1.2, 0)
	DashEffectSmoke.Parent = character.PrimaryPart

	for _, v in DashEffectSmoke:GetChildren() do
		v:Emit(v:GetAttribute'EmitCount')
	end

	Debris:AddItem(DashEffectSmoke, .7)
end


local LastDash = 0
function Dash.KeyDown()
	if workspace:GetServerTimeNow() - LastDash < Dash.DASH_COOLDOWN or not canDoDash() then return end
	LastDash = workspace:GetServerTimeNow()
	
	local DashJanitor = Janitor.new()
	
	local start = workspace:GetServerTimeNow()
	
	Client.GetController'AbilityController'.SetAbilityOnCooldown({
		Ability = script.Name,
		EndTime = start + Dash.DASH_COOLDOWN,
		StartTime = start
	})
	
	local attachment = DashJanitor:Add(Instance.new('Attachment'))
	attachment.Parent = Players.LocalPlayer.Character.PrimaryPart
	attachment.Name = 'VectorForceAttachment'
	attachment.WorldPosition = Players.LocalPlayer.Character.PrimaryPart.AssemblyCenterOfMass

	local mover = DashJanitor:Add(Instance.new('LinearVelocity'))
	mover.RelativeTo = Enum.ActuatorRelativeTo.World
	mover.VelocityConstraintMode = Enum.VelocityConstraintMode.Plane
	mover.PrimaryTangentAxis = Vector3.new(1, 0, 0)
	mover.SecondaryTangentAxis = Vector3.new(0, 0, 1)

	mover.Attachment0 = attachment
	mover.Parent = Players.LocalPlayer.Character
	mover.MaxForce = 100000
	
	dashVisual(Players.LocalPlayer.Character)
	Packets.Visuals.DashFX.send()
	
	local raycastParams = RaycastParams.new()
	raycastParams.FilterType = Enum.RaycastFilterType.Include
	raycastParams.FilterDescendantsInstances = {CollectionService:GetTagged(Client.Immutable.Tags.MAP_WALLS)}
	
	local PowerModifier = 1
	DashJanitor:Add(RunService.PreSimulation:Connect(function(time, dt)
		if not mover:IsDescendantOf(Players.LocalPlayer.Character) then return end
		local moveDir = Players.LocalPlayer.Character.Humanoid.MoveDirection.Magnitude > 0 and Players.LocalPlayer.Character.Humanoid.MoveDirection or Players.LocalPlayer.Character.PrimaryPart.CFrame.LookVector

		local result = workspace:Raycast(
			Players.LocalPlayer.Character.PrimaryPart.Position,
			(moveDir + Vector3.new(0, .2, 0)) * 50,
			raycastParams
		)
		if result ~= nil and result.Instance ~= nil then
			PowerModifier = result.Distance - 3 < 0 and 0 or 1

			if result.Distance < 20 then
				PowerModifier = Math.MapToRange(result.Distance, 0, 20, .35, 1)
			end
		end
		
		local elapsed = workspace:GetServerTimeNow() - start
		local normalizedElapsed = math.clamp(elapsed / Dash.DASH_LEN, 0, 1)

		mover.PlaneVelocity = Vector2.new(moveDir.X, moveDir.Z)
			* Math.MapToRange(elapsed, 0, Dash.DASH_LEN, 0, Dash.DASH_POWER * PowerModifier)
	end))
	
	DashJanitor:Add(function()
		Players.LocalPlayer.Character.Humanoid:ChangeState(Enum.HumanoidStateType.GettingUp)
	end)
	
	task.wait(Dash.DASH_LEN)
	
	DashJanitor:Destroy()
end


function Dash.KeyUp()
	
end


function Dash.Click()
	Dash.KeyDown()
end


function Dash._Init()
	Packets.Visuals.DashFX.listen(function(playerName: string)
		SoundService.SFX.Dash:Play()
		
		if Players.LocalPlayer.Name == playerName then return end
		
		if Players:FindFirstChild(playerName) and Players[playerName].Character and Players[playerName].Character.PrimaryPart then
			dashVisual(Players[playerName].Character)
		end
	end)
end


return Dash
