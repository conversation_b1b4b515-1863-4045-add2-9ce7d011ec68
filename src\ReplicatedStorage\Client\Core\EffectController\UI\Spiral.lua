local RunService = game:GetService('RunService')

local doublePi = math.pi * 2

return function(gui: GuiObject, intensity: number?, duration: number?, frequency: number?, decay: boolean?, cancelOld: boolean?)
	if gui:GetAttribute('Shaking') then
		if cancelOld then
			gui:SetAttribute('CancelShake', true)
			gui:GetAttributeChangedSignal('CancelShake'):Wait()
		else
			return
		end
	end

	intensity = intensity or 10
	duration = duration or 1
	frequency = frequency or 6

	gui:SetAttribute('Shaking', true)

	local originalPosition = gui.Position
	local startTime = os.clock()

	local connection
	connection = RunService.PreRender:Connect(function()
		if gui:GetAttribute('CancelShake') then
			gui:SetAttribute('CancelShake', nil)
			return connection:Disconnect()
		end

		local elapsedTime = (os.clock() - startTime) / duration
		if elapsedTime > 1 then
			connection:Disconnect()
			gui.Position = originalPosition
			gui:SetAttribute('Shaking', false)
			gui:SetAttribute('CancelShake', nil)
			return
		end

		local decayFactor = decay and 1 - elapsedTime or 1

		local offsetX = math.sin(doublePi * elapsedTime * frequency) * intensity * decayFactor
		local offsetY = math.cos(doublePi * elapsedTime * frequency) * intensity * decayFactor

		gui.Position = UDim2.new(
			originalPosition.X.Scale,
			originalPosition.X.Offset + offsetX,
			originalPosition.Y.Scale,
			originalPosition.Y.Offset + offsetY
		)
	end)
end