local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)
local Packets = require(ReplicatedStorage.Data.Packets)

local InfinityBarrier = {
	Key = Enum.KeyCode.C,

	GamepadKey = Enum.KeyCode.ButtonL2
}


function InfinityBarrier.KeyDown()
	Packets.Ability.UseAbility.send({
		AbilityName = script.Name,
		Args = {}
	})
end


function InfinityBarrier.KeyUp()

end


function InfinityBarrier.Click()
	InfinityBarrier.KeyDown()
end


function InfinityBarrier.IsOnCooldown()
	return Client.GetController'AbilityController'.IsAbilityOnCooldown(script.Name)
end


function InfinityBarrier.CanUseAbilityAsCharacter()
	return Client.GetController'AbilityController'.CanUseAbilityAsCharacter(script.Name)
end


function InfinityBarrier._Init()

end


return InfinityBarrier
