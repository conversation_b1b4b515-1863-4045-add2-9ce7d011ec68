local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local TweenService = game:GetService('TweenService')
local SoundService = game:GetService('SoundService')
local Debris = game:GetService('Debris')
local CollectionService = game:GetService('CollectionService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local Table = require(ReplicatedStorage.SharedModules.TableUtil)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local CookingService: typeof(require(ServerScriptService.Server.GameServices.CookingService)) = Server.GetService('CookingService')

local KingGlare = {
	COOLDOWN = 5,
	RADIUS = 20,
	
	Cooldowns = {} :: { [Player]: number },
	AbilityJanitors = {} :: { [Player]: Janitor.Janitor }
}


function KingGlare.GetCooldownTimeLeft(plr: Player): number
	if KingGlare.Cooldowns[plr] then
		return KingGlare.COOLDOWN - (os.clock() - KingGlare.Cooldowns[plr])
	end
	return 0
end


function KingGlare.CanUse(plr: Player): boolean
	if KingGlare.Cooldowns[plr] then
		return os.clock() - KingGlare.Cooldowns[plr] > KingGlare.COOLDOWN
	end
	
	return true
end


function KingGlare.IsInUse(plr: Player): boolean
	if KingGlare.AbilityJanitors[plr] and KingGlare.AbilityJanitors[plr]:Get('Active') then
		return true
	end

	return false
end


function KingGlare.UseAbility(plr: Player): boolean
	if not KingGlare.CanUse(plr) or KingGlare.IsInUse(plr) then return false end

	KingGlare.Cooldowns[plr] = os.clock()

	task.delay(KingGlare.COOLDOWN, function()
		KingGlare.Cooldowns[plr] = nil
	end)

	if not KingGlare.AbilityJanitors[plr] then
		KingGlare.AbilityJanitors[plr] = Janitor.new()
	end
	
	KingGlare.AbilityJanitors[plr]:Add(math.random, nil, 'Active')
	
	ClassExtension.Player.DisableMovement(plr, true)
	
	local GlareTrack: AnimationTrack = Animations:PlayTrack(plr, 'KingGlare')
	
	SoundService.SFX.Abilities.KingGlare:Play()
	
	local fx = script.VFXV3:Clone()
	fx.CFrame = plr.Character.PrimaryPart.CFrame
	fx.Parent = workspace

	KingGlare.AbilityJanitors[plr]:Add(task.delay(.2, function()
		local op do
			op = OverlapParams.new()
			op.FilterType = Enum.RaycastFilterType.Include
			
			for _, tag in Table.Extend(table.clone(Client.Immutable.BURNABLE_INTERACTABLES), {Client.Immutable.Tags.STOVE}) do
				for _, interactable in CollectionService:GetTagged(tag) do
					op:AddToFilter(interactable)
				end
			end
		end
		
		local res = workspace:GetPartBoundsInRadius(plr.Character.PrimaryPart.Position, KingGlare.RADIUS, op)
		for _, v in res do
			CookingService.Extinguish(v, plr)
		end
		
		VFXFunctions.EnableDescendants(fx)
		VFXFunctions.EmitDescendants(fx)
		task.delay(.5, VFXFunctions.DisableDescendants, fx)
	end))

	KingGlare.AbilityJanitors[plr]:Add(GlareTrack.Ended:Connect(function()
		KingGlare.AbilityJanitors[plr]:Cleanup()
	end))

	KingGlare.AbilityJanitors[plr]:Add(function()
		ClassExtension.Player.DisableMovement(plr, false)
		GlareTrack:Stop()
		
		task.delay(.2, function()
			fx:Destroy()
		end)
	end)
	
	return true
end


function KingGlare.CancelAbility(plr: Player)
	if not KingGlare.IsInUse(plr) then return end

	if KingGlare.AbilityJanitors[plr] then
		KingGlare.AbilityJanitors[plr]:Cleanup()
	end
end


return KingGlare