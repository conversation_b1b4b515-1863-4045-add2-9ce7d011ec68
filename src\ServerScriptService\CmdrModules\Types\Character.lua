local ReplicatedStorage = game:GetService('ReplicatedStorage')

local Characters = require(ReplicatedStorage.Data.Characters)

local CombinedCharacters = {}

for k in Characters do
	table.insert(CombinedCharacters, k)
end


return function(cmdr)
	local Util = cmdr.Cmdr.Util
	
	local CharacterType = {
		Transform = function(text)
			local findCharacter = Util.MakeFuzzyFinder(CombinedCharacters)
			return find<PERSON>haracter(text)
		end,

		Validate = function(characters)
			return #characters > 0
		end,

		Autocomplete = function(characters)
			return Util.GetNames(characters)
		end,

		Parse = function(characters)
			return characters[1]
		end
	}
	
	cmdr:RegisterType('character', CharacterType)
	cmdr:RegisterType('characters', Util.MakeListableType(CharacterType))
end