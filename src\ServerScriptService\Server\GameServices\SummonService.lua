local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local CharactersData = require(ReplicatedStorage.Data.Characters)
local Math = require(ReplicatedStorage.SharedModules.Math)
local Banners = require(ReplicatedStorage.Data.Banners)

local SummonService = {}


local function getHourlyBannerUnits()
	local rand = Random.new(tonumber(ClassExtension.time.CreateHourlyHash(os.time())))
	
	local mythicals = {}
	local legendaries = {}
	
	for k, v in CharactersData do
		if v.Rarity == 'Mythical' then
			table.insert(mythicals, k)
		elseif v.Rarity == 'Legendary' then
			table.insert(legendaries, k)
		end
	end
	
	local result = {mythicals[rand:NextInteger(1, #mythicals)]}
	while #result < 3 do
		local option = legendaries[rand:NextInteger(1, #legendaries)]
		if not table.find(result, option) then
			table.insert(result, option)
		end
	end

	return result
end


function SummonService.Summon(plr: Player)
	local DataService = Server.GetService('DataService')
	local CharacterService = Server.GetService('CharacterService')
	local BoostService = Server.GetService('BoostService')
	
	local BannerUnits = getHourlyBannerUnits()
	
	local characterGot
	
	local MythicalPity = DataService.Get(plr, 'SummonMythicalPity')
	local LegendaryPity = DataService.Get(plr, 'SummonLegendaryPity')
	if MythicalPity > 0 and MythicalPity % Client.Immutable.SUMMON_MYTHICAL_PITY == 0 then
		characterGot = BannerUnits[1]
	elseif LegendaryPity > 0 and LegendaryPity % Client.Immutable.SUMMON_LEGENDARY_PITY == 0 then
		characterGot = BannerUnits[2]
	end
	
	local input = {
		[BannerUnits[1]] = 3/100,
		[BannerUnits[2]] = 8/100,
		[BannerUnits[3]] = 8/100,
	}
	for rarity, odd in Banners['Banner 1'].Odds do
		input[rarity] = odd
	end
	
	local summoned = characterGot or Math.RNG(input, BoostService.GetLuckBoost(plr))
	
	if not CharactersData[summoned] then
		local charactersOfRarity = {}
		for k, v in CharactersData do
			if v.Rarity == summoned then
				table.insert(charactersOfRarity, k)
			end
		end
		
		summoned = charactersOfRarity[Random.new():NextInteger(1, #charactersOfRarity)]
	end
	
	Server.GetService('QuestService').UpdateQuestProgress(plr, 'Summon', 1)
	CharacterService.AddCharacter(plr, summoned)
	
	DataService.Increment(plr, 'SummonMythicalPity', 1)
	DataService.Increment(plr, 'SummonLegendaryPity', 1)
	
	if CharactersData[summoned].Rarity == 'Legendary' then
		DataService.Set(plr, 'SummonLegendaryPity', 0)
	elseif CharactersData[summoned].Rarity == 'Mythical' then
		DataService.Set(plr, 'SummonMythicalPity', 0)
	end
	
	return summoned
end


function SummonService._Start()
	local TimeService = Server.GetService('TimeService')
	local CurrencyService = Server.GetService('CurrencyService')
	local DataService = Server.GetService('DataService')
	
	TimeService.ResetHourly:Connect(function()
		Packets.Summon.GetBannerUnits.sendToAll({
			BannerName = 'Banner 1',
			Units = getHourlyBannerUnits()
		})
	end)
	
	Packets.Summon.GetBannerTimeLeft.listen(function(_, plr: Player)
		local timeLeft = TimeService.GetTimeLeftUntilNextHour(plr)
		Packets.Summon.GetBannerTimeLeft.sendTo(timeLeft, plr)
	end)
	
	Packets.Summon.GetBannerUnits.listen(function(_, plr: Player)
		Packets.Summon.GetBannerUnits.sendTo({
			BannerName = 'Banner 1',
			Units = getHourlyBannerUnits()
		}, plr)
	end)

	local SummonCooldowns = {}
	Packets.Summon.Summon.listen(function(data: { BannerName: string, TenTimes: boolean }, plr: Player)
		if SummonCooldowns[plr] then return end
		
		SummonCooldowns[plr] = true
		task.delay(1, function()
			SummonCooldowns[plr] = nil
		end)
		
		local price = data.TenTimes and 450 or 50
		
		if CurrencyService.GetGems(plr) >= price then
			local summoned = {}
			for i = 1, data.TenTimes and 10 or 1 do
				table.insert(summoned, SummonService.Summon(plr))
			end
			
			CurrencyService.AddGems(plr, -price, 'Summon')
			
			Packets.Summon.SummonedCharacters.sendTo(summoned, plr)
		end
		
		task.wait(.5)
		
		Packets.Inventory.UpdateCharacterInventory.sendTo(DataService.Get(plr, 'Characters'), plr)
	end)
	
	Packets.Summon.TotalSummons.listen(function(_, plr: Player)
		Packets.Summon.TotalSummons.sendTo({
			LegendaryPity = DataService.Get(plr, 'SummonLegendaryPity'),
			MythicalPity = DataService.Get(plr, 'SummonMythicalPity')
		}, plr)
	end)
end


return SummonService