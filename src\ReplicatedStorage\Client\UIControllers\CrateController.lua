local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local RunService = game:GetService('RunService')
local GuiService = game:GetService('GuiService')
local MarketplaceService = game:GetService('MarketplaceService')
local PolicyService = game:GetService('PolicyService')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local CratesData = require(ReplicatedStorage.Data.Crates)
local SimpleMarketplace = require(ReplicatedStorage.Data.SimpleMarketplace)

local CrateController = {
	SelectedCrate = nil
}

local Mouse = Players.LocalPlayer:GetMouse()


local function setupCratesFrame()
	local ItemController = Client.GetController('ItemController')
	
	local CratesFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Crates
	local CrateIndexFrame = CratesFrame.Content.Index
	
	local plrPolicyInfo = PolicyService:GetPolicyInfoForPlayerAsync(Players.LocalPlayer)
	
	local FinalOffset = UDim2.fromOffset(
		CrateIndexFrame.Parent.AbsolutePosition.X,
		CrateIndexFrame.Parent.AbsolutePosition.Y + GuiService.TopbarInset.Height
	)
	
	for crateName, crateInfo in CratesData do
		local ThisCrateDisplay = CratesFrame.Content[`Crate{crateInfo.Order}`]
		
		if crateInfo.Currency == 'Robux' and plrPolicyInfo.ArePaidRandomItemsRestricted  then
			ThisCrateDisplay.Visible = false
			continue
		end
		
		ThisCrateDisplay.Info.ItemName.Text = crateName:upper()
		ThisCrateDisplay.Info.Rarity.Text = crateInfo.Footer
		ThisCrateDisplay.Name = crateName
		
		ThisCrateDisplay.MouseButton1Click:Connect(function()
			CrateController.SetCrateSelection(crateName)
		end)
		
		local function hovered()
			CrateIndexFrame.CrateName.Text = crateName:upper()

			CrateIndexFrame:SetAttribute('Selection', crateName)
			CrateIndexFrame.Visible = true

			local pos = UDim2.fromOffset(
				ThisCrateDisplay.Contents.AbsolutePosition.X,
				ThisCrateDisplay.Contents.AbsolutePosition.Y + ThisCrateDisplay.Contents.AbsoluteSize.Y
			) - FinalOffset
			CrateIndexFrame.Position = pos

			for _, v in CrateIndexFrame.Frame:GetChildren() do
				if v:IsA'GuiObject' then v:Destroy() end
			end

			for itemName, itemInfo in crateInfo.Items do
				local ItemFrame = ItemController.CreateItem({
					ItemName = itemName,
					Parent = CrateIndexFrame.Frame,
					Count = itemInfo.Count
				})

				if CrateIndexFrame:GetAttribute'Selection' ~= crateName then
					ItemFrame:Destroy()
					break
				end
			end
		end
		
		ThisCrateDisplay.Contents.SelectionGained:Connect(hovered)
		ThisCrateDisplay.Contents.MouseEnter:Connect(hovered)
		
		local function unHovered()
			if CrateIndexFrame:GetAttribute'Selection' == crateName then
				CrateIndexFrame.Visible = false
				CrateIndexFrame:SetAttribute('Selection', nil)
			end
		end
		
		ThisCrateDisplay.Contents.MouseLeave:Connect(unHovered)
		ThisCrateDisplay.Contents.SelectionLost:Connect(unHovered)
	end
	
	for crateName, crateInfo in CratesData do
		if crateInfo.Order == 2 then
			CrateController.SetCrateSelection(crateName)
			break
		end
	end
	
	CratesFrame.Content.Buttons.Buy1.MouseButton1Click:Connect(function()
		local selectedCrateName = CrateController.SelectedCrate
		if selectedCrateName then
			local ThisCrateInfo = CratesData[selectedCrateName]
			
			if ThisCrateInfo.Currency == 'Robux' then
				MarketplaceService:PromptProductPurchase(Players.LocalPlayer, ThisCrateInfo.Open1Price)
				return
			end
			
			Packets.Crates.BuyCrate.send({CrateName = selectedCrateName, TenTimes = false})
		end
	end)
	
	CratesFrame.Content.Buttons.Buy10.MouseButton1Click:Connect(function()
		local selectedCrateName = CrateController.SelectedCrate
		if selectedCrateName then
			local ThisCrateInfo = CratesData[selectedCrateName]

			if ThisCrateInfo.Currency == 'Robux' then
				MarketplaceService:PromptProductPurchase(Players.LocalPlayer, ThisCrateInfo.Open10Price)
				return
			end
			
			Packets.Crates.BuyCrate.send({CrateName = selectedCrateName, TenTimes = true})
		end
	end)
	
	CrateIndexFrame.Visible = false
end


function CrateController.SetCrateSelection(selectedCrateName: string)
	local CratesFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Crates
	
	CrateController.SelectedCrate = selectedCrateName
	
	for crateName in CratesData do
		local ThisCrateDisplay = CratesFrame.Content:FindFirstChild(crateName)
		if ThisCrateDisplay then
			ThisCrateDisplay.Info.IsSelected.Visible = selectedCrateName == crateName
			ThisCrateDisplay.Darken.Visible = selectedCrateName ~= crateName
		end
	end
	
	local ThisCrateInfo = CratesData[selectedCrateName]
	
	CratesFrame.Content.Buttons.Buy1.Gems.Txt.Text = `{ThisCrateInfo.Open1Price}x`
	CratesFrame.Content.Buttons.Buy10.Gems.Txt.Text = `{ThisCrateInfo.Open10Price}x`
	
	CratesFrame.Content.Buttons.Buy1.Gems.Txt.GemGradient.Enabled = false
	CratesFrame.Content.Buttons.Buy1.Gems.Txt.CoinGradient.Enabled = false
	CratesFrame.Content.Buttons.Buy1.Gems.Txt.RobuxGradient.Enabled = false
	
	CratesFrame.Content.Buttons.Buy10.Gems.Txt.GemGradient.Enabled = false
	CratesFrame.Content.Buttons.Buy10.Gems.Txt.CoinGradient.Enabled = false
	CratesFrame.Content.Buttons.Buy10.Gems.Txt.RobuxGradient.Enabled = false
	
	if ThisCrateInfo.Currency == 'Coins' then
		CratesFrame.Content.Buttons.Buy1.Gems.Icon.Image = Client.Immutable.Vectors.COINS
		CratesFrame.Content.Buttons.Buy10.Gems.Icon.Image = Client.Immutable.Vectors.COINS
		CratesFrame.Content.Buttons.Buy1.Gems.Txt.CoinGradient.Enabled = true
		CratesFrame.Content.Buttons.Buy10.Gems.Txt.CoinGradient.Enabled = true
	elseif ThisCrateInfo.Currency == 'Gems' then
		CratesFrame.Content.Buttons.Buy1.Gems.Icon.Image = Client.Immutable.Vectors.GEM
		CratesFrame.Content.Buttons.Buy10.Gems.Icon.Image = Client.Immutable.Vectors.GEM
		CratesFrame.Content.Buttons.Buy1.Gems.Txt.GemGradient.Enabled = true
		CratesFrame.Content.Buttons.Buy10.Gems.Txt.GemGradient.Enabled = true
	elseif ThisCrateInfo.Currency == 'Robux' then
		CratesFrame.Content.Buttons.Buy1.Gems.Icon.Image = ''
		CratesFrame.Content.Buttons.Buy10.Gems.Icon.Image = ''
		CratesFrame.Content.Buttons.Buy1.Gems.Txt.RobuxGradient.Enabled = true
		CratesFrame.Content.Buttons.Buy10.Gems.Txt.RobuxGradient.Enabled = true
		
		CratesFrame.Content.Buttons.Buy1.Gems.Txt.Text = 'Loading...'
		CratesFrame.Content.Buttons.Buy10.Gems.Txt.Text = 'Loading...'
		
		local buy1ProdInfo = SimpleMarketplace.GetProductInfo(ThisCrateInfo.Open1Price, Enum.InfoType.Product)
		if buy1ProdInfo and CrateController.SelectedCrate then
			CratesFrame.Content.Buttons.Buy1.Gems.Txt.Text = `{buy1ProdInfo.PriceInRobux}R$`
		end
		
		local buy10ProdInfo = SimpleMarketplace.GetProductInfo(ThisCrateInfo.Open10Price, Enum.InfoType.Product)
		if buy10ProdInfo and CrateController.SelectedCrate then
			CratesFrame.Content.Buttons.Buy10.Gems.Txt.Text = `{buy10ProdInfo.PriceInRobux}R$`
		end
	end
end


function CrateController._Start()
	local CratesFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.Crates
	
	Packets.Crates.UpdateCratesOpened.listen(function(data: { LegendaryPity: number, MythicalPity: number })
		local pityMaxL = Client.Immutable.CRATE_LEGENDARY_PITY
		local pityMaxM = Client.Immutable.CRATE_MYTHICAL_PITY

		local legendaryProgress = data.LegendaryPity % pityMaxL
		local mythicalProgress = data.MythicalPity % pityMaxM

		if legendaryProgress == 0 and data.LegendaryPity > 0 then
			legendaryProgress = pityMaxL
		end
		if mythicalProgress == 0 and data.MythicalPity > 0 then
			mythicalProgress = pityMaxM
		end

		CratesFrame.LegendaryPity.Counter.Text = `{legendaryProgress}/{pityMaxL}`
		CratesFrame.MythicPity.Counter.Text = `{mythicalProgress}/{pityMaxM}`
		
		local progressRatioLegendary = legendaryProgress/Client.Immutable.CRATE_LEGENDARY_PITY
		CratesFrame.LegendaryPity.Holder.Loader.UIGradient.Transparency = NumberSequence.new{
			NumberSequenceKeypoint.new(0, 0),
			NumberSequenceKeypoint.new(math.clamp(progressRatioLegendary, .001, .998), 0),
			NumberSequenceKeypoint.new(math.clamp(progressRatioLegendary + .001, .002, .999), 1),
			NumberSequenceKeypoint.new(1, 1)
		}
		
		local progressRatioMythical = mythicalProgress/Client.Immutable.CRATE_MYTHICAL_PITY
		CratesFrame.MythicPity.Holder.Loader.UIGradient.Transparency = NumberSequence.new{
			NumberSequenceKeypoint.new(0, 0),
			NumberSequenceKeypoint.new(math.clamp(progressRatioMythical, .001, .998), 0),
			NumberSequenceKeypoint.new(math.clamp(progressRatioMythical + .001, .002, .999), 1),
			NumberSequenceKeypoint.new(1, 1)
		}
	end)
	Packets.Crates.UpdateCratesOpened.send()
	
	task.defer(setupCratesFrame)
end


return CrateController