--[=[
	Author: <PERSON><PERSON><PERSON> (Syveric)
	Characters data
]=]

export type Character = {
	Name: string,
	Abilities: {string},
	Passives: {string},
	Rarity: 'Mythical' | 'Legendary' | 'Epic' | 'Rare' | 'Common',
	SellValue: number,
	FuseXP: number,
	
	PassiveDesc: { { Title: string, Desc: string } },
	
	Animations: { Idle: string?, Walk: string? }?,
	Attachments: {string}?,
	
	HeadshotOffset: CFrame?,
	UpperBodyOffset: CFrame?,
	
	Skins: { [string]: { DisplayName: string, SellValue: number } }
}

return table.freeze{
	['Sukuna'] = {
		Name = 'Sukuna',
		Abilities = {'SukunaSlash', 'Fuga'},
		Passives = {},
		Rarity = 'Mythical',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {},
		
		Animations = {Idle = 'rbxassetid://113415224912577', Walk = 'rbxassetid://139305924916743'}
	},
	
	['<PERSON>'] = {
		Name = '<PERSON><PERSON>',
		Abilities = {'ShadowExchange', 'AuthorityHand'},
		Passives = {},
		Rarity = 'Mythical',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {},
		
		Animations = {Idle = 'rbxassetid://106321263385486', Walk = 'rbxassetid://79736554009801'},
		Attachments = {'Weapon[Left]', 'Weapon[Right]'},
		
		HeadshotOffset = CFrame.new(0, .6, -3.55) * CFrame.Angles(math.rad(10), math.rad(180), 0)
	},
	
	['Leorio Paradinight'] = {
		Name = 'Leorio Paradinight',
		Abilities = {},
		Passives = {'MovementSpeed'},
		Rarity = 'Common',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Movement Speed',
				Desc = '+10% Movement Speed'
			}
		},
	},
	
	['Muzan Kibutsuj'] = {
		Name = 'Muzan Kibutsuj',
		Abilities = {'KingGlare', 'VampiricAgility'},
		Passives = {'FireSpreadTime'},
		Rarity = 'Mythical',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Dark Presence',
				Desc = 'Reduces fire spread speed within 1.5 tiles of him by 30%, acting as a soft flame deterrent.'
			}
		},

		Animations = {Idle = 'rbxassetid://127561281726935'},
		
		HeadshotOffset = CFrame.new(.6, 1.05, -2.2) * CFrame.Angles(math.rad(5), math.rad(171), math.rad(0))
	},
	
	['Vash the Stampede'] = {
		Name = 'Vash the Stampede',
		Abilities = {},
		Passives = {'ThrowDistance'},
		Rarity = 'Common',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Throw Distance',
				Desc = '+10% Throw Distance'
			}
		},
	},
	
	['Bulma'] = {
		Name = 'Bulma',
		Abilities = {},
		Passives = {'MovementSpeed'},
		Rarity = 'Common',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Movement Speed',
				Desc = '+10% Movement Speed'
			}
		},
	},
	
	['Kenshiro'] = {
		Name = 'Kenshiro',
		Abilities = {},
		Passives = {'MovementSpeed', 'ThrowDistance'},
		Rarity = 'Rare',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Movement Speed',
				Desc = '+15% Movement Speed'
			},
			{
				Title = 'Throw Distance',
				Desc = '+10% Throw Distance'
			},
		},
	},
	
	['Shoyo Hinata'] = {
		Name = 'Shoyo Hinata',
		Abilities = {},
		Passives = {'MovementSpeed', 'CuttingSpeed'},
		Rarity = 'Rare',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Movement Speed',
				Desc = '+10% Movement Speed'
			},
			{
				Title = 'Cutting Speed',
				Desc = '+15% Faster Cutting Speed'
			},
		},
	},
	
	['Gon Freecss'] = {
		Name = 'Gon Freecss',
		Abilities = {},
		Passives = {'CuttingSpeed', 'ThrowDistance'},
		Rarity = 'Rare',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Cutting Speed',
				Desc = '+10% Faster Cutting Speed'
			},
			{
				Title = 'Throw Distance',
				Desc = '+15% Throw Distance'
			},
		},
	},
	
	['Spike Spiegel'] = {
		Name = 'Spike Spiegel',
		Abilities = {},
		Passives = {'MovementSpeed', 'ThrowDistance'},
		Rarity = 'Rare',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Movement Speed',
				Desc = '+15% Movement Speed'
			},
			{
				Title = 'Throw Distance',
				Desc = '+10% Throw Distance'
			},
		},
	},
	
	['Senku Ishigami'] = {
		Name = 'Senku Ishigami',
		Abilities = {},
		Passives = {'MovementSpeed', 'BurnTime'},
		Rarity = 'Epic',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Movement Speed',
				Desc = '+10% Movement Speed'
			},
			{
				Title = 'Strategic Genius',
				Desc = 'Every submitted order slows meat burn by 10% for 5s'
			},
		},
	},
	
	['Mikasa Ackerman'] = {
		Name = 'Mikasa Ackerman',
		Abilities = {},
		Passives = {'ThrowDistance', 'MovementSpeed'},
		Rarity = 'Epic',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Precision Focus',
				Desc = 'After a throw, Mikasa gains +20% movement speed for 4s'
			},
			{
				Title = 'Throw Distance',
				Desc = '+10% Throw Distance'
			},
		},
	},
	
	['Satoru Gojo'] = {
		Name = 'Satoru Gojo',
		Abilities = {'InfinityBarrier', 'SpatialDistortion'},
		Passives = {'ThrowDistance'},
		Rarity = 'Mythical',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Limitless Momentum',
				Desc = 'After extinguishing fire, all cooldowns are reduced by 25%'
			},
			{
				Title = 'Throw Distance',
				Desc = '+30% Throw Distance'
			},
		},
		
		Animations = {Idle = 'rbxassetid://70564672475452', Walk = 'rbxassetid://86641435619777'},
		
		Skins = {
			['TestSkin'] = {
				DisplayName = 'test skin',
				SellValue = 100
			}
		}
	},
	
	['Kizaru'] = {
		Name = 'Kizaru',
		Abilities = {'LightFlashIntervention', 'Yata no Kagami'},
		Passives = {},
		Rarity = 'Mythical',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Pika Precision',
				Desc = 'Every 4 station interactions (plate, chop, wash), all cooldowns are reduced by 25%.'
			}
		},

		Animations = {Idle = 'rbxassetid://118876975595985', Walk = 'rbxassetid://124772374573108'},
		
		UpperBodyOffset = CFrame.new(-.7, 1.4, -2.9) * CFrame.Angles(math.rad(15), math.rad(195), 0)
	},
	
	['Rengoku Kyojuro'] = {
		Name = 'Rengoku Kyojuro',
		Abilities = {'FlameSurge'},
		Passives = {'BurnTime'},
		Rarity = 'Legendary',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Burning Resolve',
				Desc = 'Every plate submitted by Rengoku Kyojuro reduces the burn speed of all meats on the map by 15% for 8s.'
			}
		},
		
		Animations = {Idle = 'rbxassetid://107776698070751', Walk = 'rbxassetid://90029837170850'},
		Attachments = {'RengokuSword'},
		
		HeadshotOffset = CFrame.new(0, .8, -3.3) * CFrame.Angles(math.rad(5), math.rad(180), 0)
	},
	
	['Levi Ackerman'] = {
		Name = 'Levi Ackerman',
		Abilities = {},
		Passives = {'ThrowDistance', 'BurnTime'},
		Rarity = 'Epic',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Relentless Efficiency',
				Desc = 'Every plate submitted by Levi Ackerman reduces the meat burn speed of all meats on the map by 10% for 6s.'
			},
			{
				Title = 'Throw Distance',
				Desc = '+10% Throw Distance'
			}
		},
	},
	
	['Edward Elric'] = {
		Name = 'Edward Elric',
		Abilities = {'AlchemicalRush'},
		Passives = {},
		Rarity = 'Legendary',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Alchemist’s Focus',
				Desc = 'Every plate submitted by Edward Elric slows nearby meat burn by 15% for 6s.'
			}
		},
		
		Animations = {Walk = 'rbxassetid://129044793911384', Idle = 'rbxassetid://136581678348674'}
	},
	
	['Toge Inumaki'] = {
		Name = 'Toge Inumaki',
		Abilities = {'Stop'},
		Passives = {},
		Rarity = 'Legendary',
		SellValue = 100,
		FuseXP = 100,
		
		PassiveDesc = {
			{
				Title = 'Cursed Calm',
				Desc = 'Throwing an ingredient to a teammate slows the next meat burn speed by 20% for 4s.'
			}
		},
		
		Animations = {Walk = 'rbxassetid://131065774702758', Idle = 'rbxassetid://79854275580997'}
	}
} :: { [string]: Character }