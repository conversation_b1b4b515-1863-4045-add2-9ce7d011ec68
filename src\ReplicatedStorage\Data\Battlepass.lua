--[=[
	Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>ic)
	Battlepass data
]=]

return {
	ID = 'B2',
	
	Tiers = {
		[1] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 100
		},
		
		[2] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 150
		},
		
		[3] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[4] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[5] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[6] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[7] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[8] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[9] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[10] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[11] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[12] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[13] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[14] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[15] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[16] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[17] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[18] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[19] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[20] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[21] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[22] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[23] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[24] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[25] = {
			Free = {
				Name = 'Executive Chef',
				ImageFooter = nil,
				Image = nil,

				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Executive Chef', 1},
				}
			},
			Paid = {
				Name = 'Rengoku Kyojuro',
				ImageFooter = nil,
				Image = nil,

				Reward = {
					Service = 'CharacterService',
					Function = 'AddCharacter',
					Args = {'Rengoku Kyojuro'},
				}
			},
			XPReq = 200
		},
		
		[26] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[27] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[28] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[29] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[30] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[31] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[32] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[33] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[34] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[35] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[36] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[37] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[38] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[39] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[40] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[41] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[42] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[43] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[44] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[45] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[46] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[47] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[48] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[49] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
		
		[50] = {
			Free = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			Paid = {
				Name = '50 GEMS',
				ImageFooter = '50x',
				Image = 'rbxassetid://122220601991100',

				Reward = {
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Battlepass'},
				}
			},
			XPReq = 200
		},
	}
}