--[=[
	Helper to get/set/process data
	
	API:
		DataService.GetProfile(Player) 						-> Returns player profile
		DataService.Get(Player, key) 						-> Returns key from profile
		DataService.Set(Player, key, value, canOverride)	-> Set the value of a key (use canOverride if changing type)
		DataService.Increment(Player, key, delta)			-> Increment the value of a key
		DataService.IsProfileLoaded(Player) 				-> boolean
		DataService.GetTemplate(Player) 					-> Profile template
]=]

local ServerScriptService = game:GetService('ServerScriptService')
local ReplicatedStorage = game:GetService('ReplicatedStorage')
local RunService = game:GetService('RunService')
local Players = game:GetService('Players')
local AnalyticsService = game:GetService('AnalyticsService')

local Server = require(ServerScriptService.Server)

local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local TableUtil = require(ReplicatedStorage.SharedModules.TableUtil)
local Signal = require(ReplicatedStorage.SharedModules.Signal)

local DataService = {
	_ProfileLoaded = Signal.new(),
	DataUpdated = Signal.new(), -- Player, key, newValue, ...
	
	Middlewares = {}
}

local profileStore = require(ServerScriptService.ServerModules.ProfileService).GetProfileStore(Server.Immutable.Data.DATASTORE, Server.Immutable.Data.TEMPLATE)
local profiles = {}


local function loadLeaderstats(plr: Player)
	
end


local function getReleaseHandler(place_id: number, game_job_id: number)
	if game.JobId == game_job_id then
		return 'Steal'
	end

	return 'ForceLoad'
end


function DataService._PlayerAdded(player: Player)
	AnalyticsService:LogOnboardingFunnelStepEvent(player, 1, 'Joined')
	
	if profiles[player.UserId] then task.wait(Server.Immutable.Data.DATA_RELEASE_TIME_AFTER_LEAVING) end

	local profile = profileStore:LoadProfileAsync(`Player_{player.UserId}`, getReleaseHandler)
	
	if profile == nil then
		task.wait(1)
		profile = profileStore:LoadProfileAsync(`Player_{player.UserId}`, 'Steal')
	end
	
	if profile ~= nil then
		profile:AddUserId(player.UserId)
		profile:Reconcile()
		profile:ListenToRelease(function()
			profiles[player.UserId] = nil
			player:Kick('You can only be in 1 server at a time.')
		end)
		if player:IsDescendantOf(Players) then
			profiles[player.UserId] = profile
			DataService._ProfileLoaded:Fire(true, player)
			task.defer(loadLeaderstats, player)
		else
			DataService._ProfileLoaded:Fire(false, player)
			profile:Release()
		end
	else
		DataService._ProfileLoaded:Fire(false, player)
		player:Kick('Your data failed to load. Are you in another server?')
	end
end


function DataService.GetProfile(player: Player)
	--if not player:IsDescendantOf(Players) then return end

	if profiles[player.UserId] then
		return profiles[player.UserId].Data, profiles[player.UserId]
	end

	local yieldSignal = Signal.new()
	local profileLoaded
	profileLoaded = DataService._ProfileLoaded:Connect(function(loaded: boolean, loadedPlayer: Player)
		if loadedPlayer == player then
			profileLoaded:Destroy()
			yieldSignal:Fire()
			yieldSignal:Destroy()
		end

		if not loaded then
			profileLoaded:Destroy()
			yieldSignal:Destroy()
		end
	end)

	yieldSignal:Wait()

	if profiles[player.UserId] then
		return profiles[player.UserId].Data, profiles[player.UserId]
	end
end


do
	local function findKeyParent(key: string, data)
		if data[key] ~= nil then return data end

		for k, v in data do
			if type(v) == 'table' then
				local res = findKeyParent(key, v)
				if res ~= nil then return v end
			end
		end
	end

	function DataService.Get(player: Player, key: string)
		local profile = DataService.GetProfile(player)
		if profile then
			local parentOfKey = findKeyParent(key, profile)
			
			if parentOfKey == nil then
				warn(`Key "{key}" not found in user data`)
			end
			
			return parentOfKey and parentOfKey[key] or nil
		end
	end

	do
		function DataService.Set(player: Player, key: string, value: any, canOverride: boolean?)
			local profile = DataService.GetProfile(player)
			local parentOfKey = findKeyParent(key, profile)

			if type(parentOfKey[key]) == type(value) or canOverride then
				parentOfKey[key] = value
			else
				error(`Attempting to override type {type(parentOfKey[key])} of key {key} with type {type(value)}`)
			end

			DataService.DataUpdated:Fire(player, key, value, 'set', nil)
		end

		function DataService.Increment(player: Player, key: string, amount: number, skipMiddleware: boolean?)
			if not skipMiddleware and DataService.Middlewares[player] and DataService.Middlewares[player][key] then
				for _, v in DataService.Middlewares[player][key] do
					amount = v.Callback(amount)
				end
			end
			
			local profile = DataService.GetProfile(player)
			local parentOfKey = findKeyParent(key, profile)

			parentOfKey[key] += amount

			DataService.DataUpdated:Fire(player, key, parentOfKey[key], 'increment', amount)
		end
	end
	
	function DataService.AddUpdateMiddleware(plr: Player, key: string, cb: (number) -> (number), priority: number)
		if not DataService.Middlewares[plr] then DataService.Middlewares[plr] = {} end
		if not DataService.Middlewares[plr][key] then DataService.Middlewares[plr][key] = {} end
		
		table.insert(DataService.Middlewares[plr][key], {
			Callback = cb,
			Priority = priority
		})
		
		table.sort(DataService.Middlewares[plr][key], function(a, b)
			return a.Priority < b.Priority
		end)
	end
end


function DataService.IsProfileLoaded(player: Player)
	return profiles[player.UserId] ~= nil
end


function DataService.GetTemplate()
	return TableUtil.Copy(Server.Immutable.Data.TEMPLATE, true)
end


function DataService._PlayerRemoving(plr: Player)
	local profile = profiles[plr.UserId]
	if profile ~= nil then
		if not RunService:IsStudio() then
			task.wait(Server.Immutable.Data.DATA_RELEASE_TIME_AFTER_LEAVING)
		end

		profiles[plr.UserId] = nil
		profile:Release()
		
		DataService.Middlewares[plr] = nil
	end
end


function DataService._Start()
	game:BindToClose(function()
		for _, plr in Players:GetPlayers() do
			DataService._PlayerRemoving(plr)
		end
	end)
end


return DataService