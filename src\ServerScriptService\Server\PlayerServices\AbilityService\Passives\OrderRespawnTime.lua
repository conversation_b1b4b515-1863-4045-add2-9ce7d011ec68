local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local PassiveModule = {}


function PassiveModule.Get()
	local TutorialService = Server.GetService('TutorialService')
	local ChallengeService = Server.GetService('ChallengeService')
	local OrderService = Server.GetService('OrderService')
	
	local default = Client.Immutable.Round.WaveOrderSpeeds[OrderService.Wave]
		and Client.Immutable.Round.WaveOrderSpeeds[OrderService.Wave].WAIT_UNTIL_NEXT_ORDER or 20
	
	if ChallengeService.Modifiers[script.Name] then
		default = ChallengeService.Modifiers[script.Name]
	end
	
	if TutorialService.Tutorial then
		default = 9999
	end
	
	return default
end


function PassiveModule.Init()

end


return PassiveModule