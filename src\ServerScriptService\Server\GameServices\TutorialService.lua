local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')
local CollectionService = game:GetService('CollectionService')
local TeleportService = game:GetService('TeleportService')
local AnalyticsService = game:GetService('AnalyticsService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local TableUtil = require(ReplicatedStorage.SharedModules.TableUtil)
local Ingredients = require(ReplicatedStorage.Data.Ingredients)

local TutorialService = {
	Tutorial = false,

	DishWashed = false,
	Complete = false,
	
	FunnelStep = 0
}


local function getNearest(origin: Vector3, objects: {BasePart})
	local nearest: BasePart? = nil
	local nearestDist = math.huge

	for _, obj in objects do
		local dist = (obj.Position - origin).Magnitude
		if dist < nearestDist then
			nearestDist = dist
			nearest = obj
		end
	end

	return nearest
end

function TutorialService.Update()
	if TutorialService.Complete or TutorialService.Tutorial == false then return end

	local CookingService: typeof(require(script.Parent.CookingService)) = Server.GetService('CookingService')
	local OrderService: typeof(require(script.Parent.OrderService)) = Server.GetService('OrderService')
	local RoundService: typeof(require(script.Parent.RoundService)) = Server.GetService('RoundService')

	local interactablesOnFire = {}
	for _, tag in TableUtil.Extend(Client.Immutable.BURNABLE_INTERACTABLES, {Client.Immutable.Tags.STOVE}) do
		for _, v in CollectionService:GetTagged(tag) do
			if CookingService.IsInteractableOnFire(v) then
				table.insert(interactablesOnFire, v)
			end
		end
	end

	for _, plr in Players:GetPlayers() do
		local interactionsToSetForPlayer = {}

		local heldItem = CookingService.GetHeldItem(plr)
		if heldItem then
			if CookingService.IsDirty(heldItem) then
				local possibleInteractables = {}
				for _, interactable in CollectionService:GetTagged(Client.Immutable.Tags.SINK) do
					if interactable:IsDescendantOf(workspace) then
						table.insert(possibleInteractables, interactable)
					end
				end

				local nearest = getNearest(plr.Character.PrimaryPart.Position, possibleInteractables)
				if nearest then
					table.insert(interactionsToSetForPlayer, nearest)
					Packets.HUD.SetActionInfo.sendTo({Text = 'Wash the dirty plate', Priority = 1}, plr)
				end
			elseif heldItem.Name == 'FireExtinguisher' then
				if #interactablesOnFire > 0 then
					for _, v in interactablesOnFire do
						table.insert(interactionsToSetForPlayer, v)
					end

					Packets.HUD.SetActionInfo.sendTo({Text = 'Extinguish the fires', Priority = 1}, plr)
				else
					local possibleInteractables = {}
					for _, interactable in CollectionService:GetTagged(Client.Immutable.Tags.COUNTERS) do
						if CookingService.CanItemBePlacedOnInteractable(heldItem, interactable) then
							table.insert(possibleInteractables, interactable)
						end
					end
					local nearest = getNearest(plr.Character.PrimaryPart.Position, possibleInteractables)
					if nearest then
						table.insert(interactionsToSetForPlayer, nearest)
						Packets.HUD.SetActionInfo.sendTo({Text = '', Priority = 1}, plr)
					end
				end
			elseif CookingService.IsIngredient(heldItem) then
				if heldItem:GetAttribute'Burned' then
					local possibleInteractables = {}
					for _, interactable in CollectionService:GetTagged(Client.Immutable.Tags.GARBAGE) do
						if interactable:IsDescendantOf(workspace) then
							table.insert(possibleInteractables, interactable)
						end
					end
					local nearest = getNearest(plr.Character.PrimaryPart.Position, possibleInteractables)
					if nearest then
						table.insert(interactionsToSetForPlayer, nearest)
						Packets.HUD.SetActionInfo.sendTo({Text = 'Throw away the burned ingredient', Priority = 1}, plr)
					end
				else
					local nextStep = CookingService.GetNextIngredientStep(heldItem)
					if not nextStep then
						local possibleInteractables = {}
						for _, v in workspace.Map:GetDescendants() do
							if CookingService.IsSubmittableFoodSurface(v) then
								local interactable = CookingService.GetInteractableFromItem(v)
								if interactable and not interactable:HasTag(Client.Immutable.Tags.DISHES)
									and CookingService.CanItemBePlacedOnInteractable(heldItem, interactable) then
									table.insert(possibleInteractables, interactable)
									Packets.HUD.SetActionInfo.sendTo({Text = `Place the {heldItem.Name:lower()} on a plate`, Priority = 1}, plr)
								end
							end
						end
						if #possibleInteractables == 0 then
							for _, interactable in CollectionService:GetTagged(Client.Immutable.Tags.COUNTERS) do
								if CookingService.CanItemBePlacedOnInteractable(heldItem, interactable) then
									table.insert(possibleInteractables, interactable)
									Packets.HUD.SetActionInfo.sendTo({Text = '', Priority = 1}, plr)
								end
							end
						end
						local nearest = getNearest(plr.Character.PrimaryPart.Position, possibleInteractables)
						if nearest then
							table.insert(interactionsToSetForPlayer, nearest)
						end
					elseif nextStep == 'CuttingBoard' then
						local possibleInteractables = {}
						for _, interactable in CollectionService:GetTagged(Client.Immutable.Tags.CUTTING_BOARD) do
							if CookingService.CanItemBePlacedOnInteractable(heldItem, interactable.Parent.Parent) then
								table.insert(possibleInteractables, interactable)
							end
						end
						local nearest = getNearest(plr.Character.PrimaryPart.Position, possibleInteractables)
						if nearest then
							table.insert(interactionsToSetForPlayer, nearest)
							Packets.HUD.SetActionInfo.sendTo({Text = `Chop the {heldItem.Name:lower()}`, Priority = 1}, plr)
						end
					elseif nextStep == 'Stove' then
						local Item

						local possibleInteractables = {}
						for _, interactable in CollectionService:GetTagged(Client.Immutable.Tags.STOVE) do
							local ItemOnStove = CookingService.GetItemOnInteractable(interactable)
							if ItemOnStove and CookingService.CanItemBePlacedOnInteractable(heldItem, interactable) then
								local _, stepIdx = CookingService.GetNextIngredientStep(heldItem)
								local stepInfo = Ingredients[heldItem.Name].PrepSteps[stepIdx]
								if ItemOnStove.Name ~= stepInfo.StoveType then continue end

								table.insert(possibleInteractables, interactable)
							end
						end
						local nearest = getNearest(plr.Character.PrimaryPart.Position, possibleInteractables)
						if nearest then
							table.insert(interactionsToSetForPlayer, nearest)
							Packets.HUD.SetActionInfo.sendTo({Text = `Cook the {heldItem.Name:lower()}`, Priority = 1}, plr)
						end
					end
				end
			elseif CookingService.IsSubmittableFoodSurface(heldItem) then
				local surfaceIngredients = CookingService.GetAllIngredientsOnInteractable(heldItem)
				if OrderService.DoesOrderExistWithIngredients(surfaceIngredients) then
					for _, v in CollectionService:GetTagged(Client.Immutable.Tags.SUBMIT_ORDER) do
						if v:IsDescendantOf(workspace) then
							table.insert(interactionsToSetForPlayer, v)
							Packets.HUD.SetActionInfo.sendTo({Text = 'Submit the plate to the customers', Priority = 1}, plr)
						end
					end
				else
					local possibleInteractables = {}
					for _, interactable in CollectionService:GetTagged(Client.Immutable.Tags.COUNTERS) do
						if CookingService.CanItemBePlacedOnInteractable(heldItem, interactable) then
							table.insert(possibleInteractables, interactable)
						end
					end
					local nearest = getNearest(plr.Character.PrimaryPart.Position, possibleInteractables)
					if nearest then
						table.insert(interactionsToSetForPlayer, nearest)
						Packets.HUD.SetActionInfo.sendTo({Text = '', Priority = 1}, plr)
					end
				end
			elseif CookingService.IsItemFoodSurface(heldItem) then
				local hasIngredient = false
				for _, ingredient in heldItem:GetChildren() do
					if CookingService.IsIngredient(ingredient) then
						hasIngredient = true
						break
					end
				end

				if hasIngredient then
					local possibleInteractables = {}
					for _, v in workspace.Map:GetDescendants() do
						if v:IsA'BasePart' and CookingService.IsSubmittableFoodSurface(v) then
							local interactable = CookingService.GetInteractableFromItem(v)
							if interactable and not interactable:HasTag(Client.Immutable.Tags.DISHES)
								and CookingService.CanItemBePlacedOnInteractable(heldItem, interactable) then
								table.insert(possibleInteractables, interactable)
								Packets.HUD.SetActionInfo.sendTo({Text = `Place the cooked meat on a plate`, Priority = 1}, plr)
							end
						end
					end
					if #possibleInteractables == 0 then
						for _, interactable in CollectionService:GetTagged(Client.Immutable.Tags.COUNTERS) do
							if CookingService.CanItemBePlacedOnInteractable(heldItem, interactable) then
								table.insert(possibleInteractables, interactable)
								Packets.HUD.SetActionInfo.sendTo({Text = '', Priority = 1}, plr)
							end
						end
					end
					local nearest = getNearest(plr.Character.PrimaryPart.Position, possibleInteractables)
					if nearest then
						table.insert(interactionsToSetForPlayer, nearest)
					end
				else
					local possibleInteractables = {}
					for _, interactable in CollectionService:GetTagged(Client.Immutable.Tags.STOVE) do
						if CookingService.CanItemBePlacedOnInteractable(heldItem, interactable) then
							table.insert(possibleInteractables, interactable)
						end
					end
					local nearest = getNearest(plr.Character.PrimaryPart.Position, possibleInteractables)
					if nearest then
						table.insert(interactionsToSetForPlayer, nearest)
						Packets.HUD.SetActionInfo.sendTo({Text = `Place the {heldItem.Name:lower()} back to where it was`, Priority = 1}, plr)
					end
				end
			end
		else
			local canSubmit = false
			for _, interactable in CollectionService:GetTagged(Client.Immutable.Tags.COUNTERS) do
				local surface = CookingService.GetItemOnInteractable(interactable)
				if surface and CookingService.IsItemFoodSurface(surface) then
					local surfaceIngredients = CookingService.GetAllIngredientsOnInteractable(interactable)
					if OrderService.DoesOrderExistWithIngredients(surfaceIngredients) then
						canSubmit = true
						table.insert(interactionsToSetForPlayer, interactable)

						Packets.HUD.SetActionInfo.sendTo({Text = 'Grab the plate to give it to customers', Priority = 1}, plr)
					end
				end
			end

			if canSubmit == false and TutorialService.DishWashed == false then
				local gettingPlate = true
				local plateExists = false
				for _, v in workspace.Map:GetDescendants() do
					if v:IsA'BasePart' and CookingService.IsSubmittableFoodSurface(v) then
						local interactable = CookingService.GetInteractableFromItem(v)
						if interactable and not interactable:HasTag(Client.Immutable.Tags.DISHES) then
							plateExists = true

							if not CookingService.GetIngredientOnInteractable(interactable) then
								gettingPlate = false
								break
							end
						end
					end
				end

				if plateExists == false or gettingPlate == true then
					local possibleInteractables = {}
					for _, v in CollectionService:GetTagged(Client.Immutable.Tags.DISHES) do
						if v:IsDescendantOf(workspace) and v:FindFirstChildWhichIsA'BasePart' then
							table.insert(possibleInteractables, v)
						end
					end
					local nearest = getNearest(plr.Character.PrimaryPart.Position, possibleInteractables)
					if nearest then
						gettingPlate = true
						table.insert(interactionsToSetForPlayer, nearest)
						Packets.HUD.SetActionInfo.sendTo({Text = 'Get a plate', Priority = 1}, plr)
					else
						gettingPlate = false
					end
				end

				if not gettingPlate then
					local isGoingForIngredient = false

					-- Take stuff off interactables such ovens and cutting boards
					for _, interactable in CollectionService:GetTagged(Client.Immutable.Tags.CUTTING_BOARD) do
						local thisIngredient = CookingService.GetIngredientOnInteractable(interactable.Parent.Parent)
						if thisIngredient and CookingService.KitchenwareModules['CuttingBoard'].IsActionComplete(thisIngredient) then
							isGoingForIngredient = true
							table.insert(interactionsToSetForPlayer, interactable)
						end
					end

					for _, interactable in CollectionService:GetTagged(Client.Immutable.Tags.STOVE) do
						local thisIngredient = CookingService.GetIngredientOnInteractable(interactable)
						if thisIngredient and CookingService.KitchenwareModules['Stove'].IsActionComplete(thisIngredient) then
							isGoingForIngredient = true
							table.insert(interactionsToSetForPlayer, interactable)
							Packets.HUD.SetActionInfo.sendTo({Text = `Take the {thisIngredient.Name:lower()} off the stove`, Priority = 1}, plr)
						end
					end

					--

					local foundIngredients = {}
					local possibleIngredientsForMap = OrderService.GetUniqueIngredientsInActiveOrders()

					for _, v in CollectionService:GetTagged(Client.Immutable.Tags.INGREDIENT) do
						if v:IsDescendantOf(workspace) then
							foundIngredients[v.Name] = true
						end
					end

					for _, ingredientName in possibleIngredientsForMap do
						if not foundIngredients[ingredientName] then
							isGoingForIngredient = true

							for _, ingredientBox in CollectionService:GetTagged(Client.Immutable.Tags.INGREDIENT_BOX) do
								if ingredientBox:IsDescendantOf(workspace)
									and ingredientBox.Parent:GetAttribute('Ingredient') == ingredientName then
									table.insert(interactionsToSetForPlayer, ingredientBox)
									Packets.HUD.SetActionInfo.sendTo({Text = 'Grab an ingredient', Priority = 1}, plr)
									break
								end
							end

							break
						end
					end
				end
			end
		end

		if #interactablesOnFire > 0 then
			for _, v in CollectionService:GetTagged(Client.Immutable.Tags.FIRE_EXTINGUISHER) do
				if v:IsDescendantOf(workspace.Map) then
					table.insert(interactionsToSetForPlayer, v)
					Packets.HUD.SetActionInfo.sendTo({Text = 'Pickup the fire extinguisher to get rid of the flames', Priority = 1}, plr)
				end
			end
		end

		if #interactionsToSetForPlayer == 0 then
			Packets.HUD.SetActionInfo.sendTo({Text = '', Priority = 2}, plr)
		end

		Packets.Waypoint.StrictSetWaypoints.sendTo(interactionsToSetForPlayer, plr)
	end
end


function TutorialService._PlayerAdded(plr: Player)
	local CookingService = Server.GetService('CookingService')
	local RoundService: typeof(require(ServerScriptService.Server.GameServices.RoundService)) = Server.GetService('RoundService')
	local OrderService = Server.GetService('OrderService')
	local DataService = Server.GetService('DataService')

	AnalyticsService:LogFunnelStepEvent(plr, 'Tutorial', nil, 1, 'Joined')
	TutorialService.FunnelStep = 1

	if Client.Immutable.SERVER_TYPE ~= 'Round' then return end

	local joinData = plr:GetJoinData()
	if (joinData and joinData.TeleportData and joinData.TeleportData.Tutorial) or TutorialService.Tutorial then
		TutorialService.Tutorial = true

		AnalyticsService:LogFunnelStepEvent(plr, 'Tutorial', nil, 2, 'Teleported to tutorial')
		TutorialService.FunnelStep = 2

		local plates = {}
		for _, v in workspace:WaitForChild'Map':GetDescendants() do
			if v:IsA'BasePart' and CookingService.IsSubmittableFoodSurface(v) then
				table.insert(plates, v)
			end
		end

		for idx, v in plates do
			if idx == #plates then break end
			v:Destroy()
		end

		DataService.Increment(plr, 'RoundsPlayed', 1)

		while true do
			task.wait(1)

			if not RoundService.IsRoundActive
				or RoundService.Elapsed() < Client.Immutable.Round.ORDER_INIT
			then
				continue
			end

			OrderService.CreateOrder('Lettuce Tomato Burger')
			break
		end
	end
end


function TutorialService._Start()
	local CookingService: typeof(require(script.Parent.CookingService)) = Server.GetService('CookingService')
	local OrderService: typeof(require(script.Parent.OrderService)) = Server.GetService('OrderService')
	local DataService = Server.GetService('DataService')
	local CurrencyService = Server.GetService('CurrencyService')

	Packets.Tutorial.DoTutorial.listen(function(_, plr: Player)
		local restaurantPlaceID
		for placeId, placeName in Client.Immutable.Servers.PLACES[game.GameId] do
			if placeName == 'Restaurant' then
				restaurantPlaceID = placeId
				break
			end
		end

		local TeleportUI = ReplicatedStorage.Assets.Visuals.TeleportUI:Clone()
		TeleportUI.Frame.Container.Title.Text = `Teleporting To Tutorial...`
		TeleportUI.Frame.Background.Image = Client.Immutable.Servers.PLACE_INFO.Restaurant.MapImage

		local code, privateServerID = TeleportService:ReserveServer(restaurantPlaceID)
		TeleportService:TeleportToPrivateServer(
			restaurantPlaceID, code, {plr}, nil, {
				ExpectedPlayers = 1,
				Tutorial = true
			},
			TeleportUI
		)
	end)

	Packets.Tutorial.GetRoundsCompleted.listen(function(_, plr: Player)
		Packets.Tutorial.GetRoundsCompleted.sendTo(DataService.Get(plr, 'RoundsPlayed'), plr)
	end)

	if Client.Immutable.SERVER_TYPE ~= 'Round' then return end

	Packets.Tutorial.ReturnToLobby.listen(function(_, plr: Player)
		local lobbyPlaceId
		for placeId, placeName in Client.Immutable.Servers.PLACES[game.GameId] do
			if placeName == 'Lobby' then
				lobbyPlaceId = placeId
				break
			end
		end

		TeleportService:Teleport(lobbyPlaceId, plr, nil, ReplicatedStorage.Assets.Visuals.TeleportUI:Clone())
	end)

	workspace:WaitForChild'Map'

	task.defer(TutorialService.Update)

	CookingService.ItemPickedUp:Connect(function(item: BasePart, plr: Player)
		if TutorialService.Tutorial and item.Name == 'Plate' and TutorialService.FunnelStep <= 2 then
			AnalyticsService:LogFunnelStepEvent(plr, 'Tutorial', nil, 3, 'Picked up plate')
			TutorialService.FunnelStep = 3
		end
		
		TutorialService.Update()
	end)

	CookingService.ItemPlaced:Connect(function(item: BasePart | Model, targetInteractable: BasePart, plr: Player?)
		if TutorialService.Tutorial and item.Name == 'Plate' and TutorialService.FunnelStep <= 3 then
			AnalyticsService:LogFunnelStepEvent(plr, 'Tutorial', nil, 4, 'Placed down plate')
			TutorialService.FunnelStep = 4
		end
		
		TutorialService.Update()
	end)

	CookingService.ItemThrown:Connect(function(plr: Player, item: BasePart)
		TutorialService.Update()
	end)

	OrderService.OrderCreated:Connect(function(orderInfo: { Item: string, OrderID: string, TimeCreated: number })
		TutorialService.Update()
	end)

	OrderService.OrderDeleted:Connect(function(orderData, expired: boolean?, plr: Player?)
		if TutorialService.Tutorial and TutorialService.FunnelStep <= 6 then
			AnalyticsService:LogFunnelStepEvent(Players:FindFirstChildOfClass'Player', 'Tutorial', nil, 7, 'Order submitted')
			TutorialService.FunnelStep = 7
		end
		
		TutorialService.Update()
	end)

	CookingService.FireStarted:Connect(function(interactable: BasePart)
		TutorialService.Update()
	end)

	CookingService.FireExtinguished:Connect(function(interactable: BasePart, plr: Player?)
		TutorialService.Update()

		if TutorialService.Tutorial then
			if TutorialService.Tutorial and TutorialService.FunnelStep <= 8 then
				AnalyticsService:LogFunnelStepEvent(Players:FindFirstChildOfClass'Player', 'Tutorial', nil, 9, 'Extinguished fire')
				TutorialService.FunnelStep = 9
			end
			
			local DoesInteractableOnFireExist = false
			for _, tag in TableUtil.Extend(Client.Immutable.BURNABLE_INTERACTABLES, {Client.Immutable.Tags.STOVE}) do
				for _, v in CollectionService:GetTagged(tag) do
					if CookingService.IsInteractableOnFire(v) then
						DoesInteractableOnFireExist = true
						break
					end
				end
			end

			if DoesInteractableOnFireExist == false and TutorialService.DishWashed then
				if plr then
					CurrencyService.AddGems(plr, 50, Enum.AnalyticsEconomyTransactionType.Onboarding)
				end

				Packets.Waypoint.RemoveAllWaypoints.sendToAll(nil)
				Packets.Tutorial.TutorialCompleted.sendToAll()
				
				if TutorialService.Tutorial and TutorialService.FunnelStep <= 9 then
					AnalyticsService:LogFunnelStepEvent(Players:FindFirstChildOfClass'Player', 'Tutorial', nil, 10, 'Tutorial completed')
					TutorialService.FunnelStep = 10
				end
			end
		end
	end)

	CookingService.DishWashed:Connect(function()
		TutorialService.Update()

		if TutorialService.Tutorial then
			if TutorialService.Tutorial and TutorialService.FunnelStep <= 7 then
				AnalyticsService:LogFunnelStepEvent(Players:FindFirstChildOfClass'Player', 'Tutorial', nil, 8, 'Dish washed')
				TutorialService.FunnelStep = 8
			end
			
			TutorialService.DishWashed = true

			task.wait(.3)

			Packets.HUD.SetActionInfo.sendToAll({Text = 'Great job! Now, for your final task...', Priority = 2})

			task.wait(4)

			Packets.HUD.SetActionInfo.sendToAll({Text = 'The kitchen is on fire! Extinguish it quickly!', Priority = 2})

			task.wait(3)

			Packets.HUD.SetActionInfo.sendToAll({Text = '', Priority = 3})

			task.wait(.1)

			local setOnFire = 0
			local counters = TableUtil.Shuffle(CollectionService:GetTagged(Client.Immutable.Tags.COUNTERS))
			for _, v in counters do
				if not v:IsDescendantOf(workspace) then continue end
				
				setOnFire += 1
				CookingService.SetInteractableOnFire(v)
				
				if setOnFire >= 1 then break end
			end

			TutorialService.Update()
		end
	end)


	CookingService.FinishedChopping:Connect(function()
		if TutorialService.Tutorial and TutorialService.FunnelStep <= 4 then
			AnalyticsService:LogFunnelStepEvent(Players:FindFirstChildOfClass'Player', 'Tutorial', nil, 5, 'Chopped ingredient')
			TutorialService.FunnelStep = 5
		end
		
		TutorialService.Update()
	end)

	CookingService.PlateRespawned:Connect(function()
		TutorialService.Update()
	end)

	CookingService.FinishedCooking:Connect(function()
		if TutorialService.Tutorial and TutorialService.FunnelStep <= 5 then
			AnalyticsService:LogFunnelStepEvent(Players:FindFirstChildOfClass'Player', 'Tutorial', nil, 6, 'Finished cooking meat')
			TutorialService.FunnelStep = 6
		end
		
		TutorialService.Update()
	end)
end


return TutorialService