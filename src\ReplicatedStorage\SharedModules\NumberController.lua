--[=[
	Author: Viken (uhi_o)
	Contributors:
	Process numbers into other stuff
]=]

local NumberController = {}


local  Suffixes = {'', 'k', 'M', 'B', 'T', 'q', 'Q', 's', 'S', 'O', 'N', 'd', 'U', 'D', 'td', 'qdD', 'QnD', 'sxD', 'SpD', 'OcD', 'NvD', 'Vgn', 'UVg', 'DVg', 'TVg', 'qtV', 'QnV', 'SeV', 'SPG', 'OVG', 'NVG', 'TGN', 'UTG', 'DTG', 'tsTG', 'qtTG', 'QnTG', 'ssTG', 'SpTG', 'OcTG', 'NoAG', 'UnAG', 'DuAG', 'TeAG', 'QdAG', 'QnAG', 'SxAG', 'SpAG', 'OcAG', 'NvAG', 'CT'}
function NumberController.SuffixNumber(n: number)
	for i = 1, #Suffixes do
		if n < 10^(i * 3) then
			return math.floor(n/((10^((i - 1)*3))/100))/(100) .. Suffixes[i]
		end
	end
end


function NumberController.Comma(n: number): string
	local left, num, decimal = string.match(n, '^([^%d]*%d)(%d*)(.-)$')
	return left .. (num:reverse():gsub('(%d%d%d)', '%1,'):reverse()) .. decimal
end


local RANDOMIZER = Random.new()
local SCALE = 10_000_000_000
function NumberController.RNG(input: {[any]: number}, luck: number?, default: any?)
	local Portions = {}
	local total = 0

	for _, v in input do
		total += v
	end

	local cumulativeNormalized = 0
	for k, v in input do
		local normalized = (v / total) * SCALE
		table.insert(Portions, {
			Lower = cumulativeNormalized,
			Upper = cumulativeNormalized + normalized,
			Result = k
		})
		cumulativeNormalized += normalized
	end
	
	local rand = RANDOMIZER:NextNumber(0, SCALE) / (luck or 1)
	for k, v in Portions do
		if v.Lower <= rand and rand <= v.Upper then
			return v.Result
		end
	end

	if not default then
		for k, v in Portions do
			return v.Result
		end
	else
		return default
	end
end


do -- Time
	function NumberController.ToDHMSorHMS(s: number): string
		return s >= 60*60*24 and string.format('%d:%02d:%02d:%02d', s/86400, (s/(60*60))%24, s/60%60, s%60)
			or string.format('%d:%02d:%02d', (s/(60*60))%24, s/60%60, s%60)
	end

	function NumberController.HMS(x: number): string
		local roundedTime = math.floor(x)
		return string.format("%02i:%02i:%02i", roundedTime/60^2, roundedTime/60%60, roundedTime%60)
	end

	function NumberController.ToMS(x: number): string
		local roundedTime = math.floor(x)
		return string.format("%02i:%02i", roundedTime/60%60, roundedTime%60)
	end
	
	function NumberController.ToHMSorMS(x: number): string
		return x < 60*60 and NumberController.ToMS(x) or NumberController.HMS(x)
	end
	
	function NumberController.ToFormalTime(x: number)
		if x >= 60*60*24 then
			local days = math.floor(x/(60*60*24))
			return `{days} day{days > 1 and 's' or ''}`
		elseif x >= 60*60 then
			local hours = math.floor(x/(60*60))
			return `{hours} hour{hours > 1 and 's' or ''}`
		elseif x >= 60*10 then
			local minutes = math.floor(x/60)
			return `{minutes} minute{minutes > 1 and 's' or ''}`
		elseif x >= 60 then
			local minutes = math.floor(x/60)
			local seconds = math.floor(x%60)
			return `{minutes} minute{minutes > 1 and 's' or ''} and {seconds} second{seconds > 1 and 's' or ''}`
		end
		
		return `{x%60} seconds{x%60 > 1 and 's' or ''}`
	end
	
	function NumberController.ToMSReadable(x: number): string
		local roundedTime = math.floor(x)

		local minutes = math.floor(roundedTime/60%60)
		if minutes >= 1 then
			return minutes .. ' minute' .. (minutes > 1 and 's' or '')
		end

		return (roundedTime%60) .. ' seconds'
	end

	function NumberController.ToMSNerdy(x: number): string
		local roundedTime = math.floor(x)

		local minutes = math.floor(roundedTime/60%60)
		if minutes >= 1 then
			return minutes .. ' minute' .. (minutes == 1 and '' or 's') .. ' and ' .. (roundedTime%60) .. ' seconds'
		end

		return (roundedTime%60) .. ' seconds'
	end
	
	function NumberController.ToMSNerdyCompact(x: number): string
		local roundedTime = math.floor(x)

		local minutes = math.floor(roundedTime/60%60)
		if minutes >= 1 then
			return minutes .. 'm' .. ' and ' .. (roundedTime%60) .. 's'
		end

		return (roundedTime%60) .. 's'
	end

	function NumberController.ToDHorHMNerdy(x: number)
		local days = math.floor(x/86400)
		local hours = math.floor((x/(60*60))%24)
		local minutes = math.floor(x/60%60)

		return (x >= 60*60*24 and math.floor(days) .. ' day' .. (days > 1 and 's' or '') .. (hours > 0 and ' and ' .. hours .. ' hour' .. (hours > 1 and 's' or '') or ''))
			or (x >= 60*60 and hours .. ' hour' .. (hours > 1 and 's' or '') .. (minutes > 0 and ' and ' .. minutes .. ' minute' .. (minutes > 1 and 's' or '') or ''))
			or NumberController.ToMSNerdy(x)
	end
end


do
	local UNITS = {
		[0] = 'Zero',
		'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'
	}

	local TEENS = {
		'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'
	}

	local TENS = {
		[2] = 'Twenty', [3] = 'Thirty', [4] = 'Forty', [5] = 'Fifty', [6] = 'Sixty', [7] = 'Seventy', [8] = 'Eighty', [9] = 'Ninety'
	}

	function NumberController.NumberToText(n: number): string -- MAX 999
		if n <= 10 then
			return n < 10 and UNITS[n] or 'Ten'
		elseif n < 20 then
			return TEENS[n - 10]
		elseif n < 100 then
			local unitDigit = n % 10
			local result = TENS[math.floor(n / 10)]
			if unitDigit > 0 then
				result = result .. '-' .. UNITS[unitDigit]
			end
			return result
		elseif n < 1000 then
			local remainingDigits = n % 100
			local result = UNITS[math.floor(n / 100)] .. ' Hundred'
			if remainingDigits > 0 then
				result = result .. ' and ' .. NumberController.NumberToText(remainingDigits)
			end
			return result
		end

		return 'Number out of range'
	end
end


function NumberController.FixNumber(n: number, default: number?)
	if n ~= n or n == 1/0 then
		-- n is "nan" or "inf"
		-- warn('Some number bugged (as inf or nan)', debug.traceback(nil, 2))
		return default or 0
	end
	
	return math.ceil(n)
end


return NumberController