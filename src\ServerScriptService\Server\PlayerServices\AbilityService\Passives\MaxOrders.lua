local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local PassiveModule = {}


function PassiveModule.Get()
	local TutorialService = Server.GetService('TutorialService')
	local RoundService = Server.GetService('RoundService')

	local default = Client.Immutable.Round.MAX_ORDERS

	if RoundService.MapName and RoundService.Act then
		local res = Client.Immutable.Servers.PLACE_INFO[RoundService.MapName].ActsInfo[RoundService.Act][RoundService.Difficulty].MaxOrders
		default = res or default
	end

	if TutorialService.Tutorial then
		default = 0
	end

	return default
end


function PassiveModule.Init()

end


return PassiveModule