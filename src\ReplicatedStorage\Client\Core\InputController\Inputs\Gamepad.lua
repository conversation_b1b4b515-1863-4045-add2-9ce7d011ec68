-- Gamepad
-- <PERSON>
-- December 28, 2017

--[[
	
	gamepad = Gamepad.new(gamepadUserInputType)
	
	Boolean      gamepad:IsDown(keyCode)
	Boolean      gamepad:IsConnected()
	InputObject  gamepad:GetState(keyCode)
	Void         gamepad:SetMotor(motor, value)
	Void         gamepad:StopMotor(motor)
	Void         gamepad:StopAllMotors()
	Boolean      gamepad:IsMotorSupported(motor)
	Boolean      gamepad:IsVibrationSupported()
	Float        gamepad:GetMotorValue(motor)
	Float        gamepad:ApplyDeadzone(value, deadzoneThreshold)
	
	gamepad.ButtonDown(keyCode)
	gamepad.ButtonUp(keyCode)
	gamepad.Changed(keyCode, input)
	gamepad.Connected()
	gamepad.Disconnected()
	
--]]



local Gamepad = {}
Gamepad.__index = Gamepad

local gamepadsByInputType = {}

local userInput = game:GetService('UserInputService')
local hapticService = game:GetService("HapticService")
local signal = require(game:GetService'ReplicatedStorage'.SharedModules.Signal)


function Gamepad.new(gamepad)

	if (gamepadsByInputType[gamepad]) then
		return gamepadsByInputType[gamepad]
	end

	local self = setmetatable({
		_gamepadInput = gamepad;
		_state = {};
		_isConnected = false;
	}, Gamepad)

	self.ButtonDown   = signal.new()
	self.ButtonUp     = signal.new()
	self.Changed      = signal.new()
	self.Connected    = signal.new()
	self.Disconnected = signal.new()

	self._listeners = self.Shared.ListenerList.new()

	if (userInput:GetGamepadConnected(gamepad)) then
		self._isConnected = true
		self:ConnectAll()
	end

	-- Connected:
	userInput.GamepadConnected:Connect(function(gamepadNum)
		if gamepadNum == gamepad then
			self._isConnected = true
			self:ConnectAll()
			self.Connected:Fire()
		end
	end)

	-- Disconnected:
	userInput.GamepadDisconnected:Connect(function(gamepadNum)
		if gamepadNum == gamepad then
			self._isConnected = false
			self:DisconnectAll()
			self.Disconnected:Fire()
		end
	end)

	-- Map InputObject states to corresponding KeyCodes:
	for _, input in userInput:GetGamepadState(gamepad) do
		self._state[input.KeyCode] = input
	end

	gamepadsByInputType[gamepad] = self

	return self

end


function Gamepad.ConnectAll()
	-- Input Began:
	Gamepad._listeners:Connect(userInput.InputBegan, function(input, processed)
		if (processed) then return end
		if (input.UserInputType == Gamepad._gamepadInput) then
			Gamepad.ButtonDown:Fire(input.KeyCode)
		end
	end)

	-- Input Ended:
	Gamepad._listeners:Connect(userInput.InputEnded, function(input, processed)
		if (input.UserInputType == Gamepad._gamepadInput) then
			Gamepad.ButtonUp:Fire(input.KeyCode)
		end
	end)

	-- Input Changed:
	Gamepad._listeners:Connect(userInput.InputChanged, function(input, processed)
		if (input.UserInputType == Gamepad._gamepadInput) then
			Gamepad.Changed:Fire(input.KeyCode, input)
		end
	end)
end


function Gamepad.DisconnectAll()
	Gamepad._listeners:DisconnectAll()
end


function Gamepad.IsDown(keyCode)
	return userInput:IsGamepadButtonDown(Gamepad._gamepadInput, keyCode)
end


function Gamepad.IsConnected()
	return Gamepad._isConnected
end


function Gamepad.GetState(keyCode)
	return Gamepad._state[keyCode]
end


function Gamepad.SetMotor(motor, value)
	hapticService:SetMotor(Gamepad._gamepadInput, motor, value)
end


function Gamepad.IsMotorSupported(motor)
	return hapticService:IsMotorSupported(Gamepad._gamepadInput, motor)
end


function Gamepad.IsVibrationSupported()
	return hapticService:IsVibrationSupported(Gamepad._gamepadInput)
end


function Gamepad.StopMotor(motor)
	Gamepad.SetMotor(motor, 0)
end


function Gamepad.GetMotorValue(motor)
	return hapticService:GetMotor(Gamepad._gamepadInput, motor)
end


function Gamepad.StopAllMotors()
	for _, motor in Enum.VibrationMotor:GetEnumItems() do
		Gamepad.StopMotor(motor)
	end
end


function Gamepad.ApplyDeadzone(value, deadzoneThreshold)
	if math.abs(value) < deadzoneThreshold then
		return 0
	elseif value > 0 then
		return (value - deadzoneThreshold) / (1 - deadzoneThreshold)
	else
		return (value + deadzoneThreshold) / (1 - deadzoneThreshold)
	end
end


function Gamepad.Start()

end


function Gamepad.Init()

end


return Gamepad