return {
	['All'] = {
		Order = 1,
		Func = function(frame: GuiObject)
			return true
		end
	},
	
	['Common'] = {
		Order = 2,
		Func = function(frame: GuiObject)
			return frame:GetAttribute'Rarity' == 'Common'
		end
	},
	
	['Rare'] = {
		Order = 3,
		Func = function(frame: GuiObject)
			return frame:GetA<PERSON>ribute'Rarity' == 'Rare'
		end
	},
	
	['Epic'] = {
		Order = 4,
		Func = function(frame: GuiObject)
			return frame:GetAttribute'Rarity' == 'Epic'
		end
	},
	
	['Legendary'] = {
		Order = 5,
		Func = function(frame: GuiObject)
			return frame:GetAttribute'Rarity' == 'Legendary'
		end
	},
	
	['Mythical'] = {
		Order = 6,
		Func = function(frame: GuiObject)
			return frame:GetAttribute'Rarity' == 'Mythical'
		end
	},
}