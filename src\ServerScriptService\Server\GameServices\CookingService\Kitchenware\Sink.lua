local ReplicatedStorage = game:GetService('ReplicatedStorage')
local TweenService = game:GetService('TweenService')

local Client = require(ReplicatedStorage.Client)

local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local CookingService = require(script.Parent.Parent)

local Sink = {
	SinkJanitors = {},
	ClaimedSinks = {},
	
	SINK_OFFSET = CFrame.new(0, 0, -4) * CFrame.Angles(0, math.pi, 0),
	PLATE_C0 = CFrame.new(.119552612, -.815523386, -.586326599, -1, 0, 0, 0, 0, -1, 0, -1, 0),
	
	CLEAN_TIME = 4.2
}


function Sink.Interact(plr: Player, interactable: BasePart, ingredient: BasePart?)
	if Sink.ClaimedSinks[interactable] or not Sink.CanInteract(plr, interactable) then return end
	
	local heldItem = CookingService.GetHeldItem(plr)

	Sink.SinkJanitors[plr] = Janitor.new()
	Sink.ClaimedSinks[interactable] = plr
	
	do
		local Plate
		for _, v in Client.Immutable.SUBMITTABLE_FOOD_SURFACES do
			if plr.Character['Right Arm']:FindFirstChild(v) then
				Plate = plr.Character['Right Arm'][v]
				break
			end
		end
		
		local originalC0 = Plate.C0
		Sink.SinkJanitors[plr]:Add(function()
			Plate.C0 = originalC0
		end)
		Plate.C0 = Sink.PLATE_C0
	end
	
	do
		local appliedOffset = interactable.CFrame * Sink.SINK_OFFSET

		plr.Character.PrimaryPart.CFrame = CFrame.new(appliedOffset.X, plr.Character.PrimaryPart.CFrame.Y, appliedOffset.Z)
			* CFrame.fromMatrix(Vector3.new(), appliedOffset.RightVector, appliedOffset.UpVector, appliedOffset.LookVector)

		plr.Character.PrimaryPart.Anchored = true
		plr.Character.Humanoid.AutoRotate = false

		plr.Character.Humanoid:MoveTo(plr.Character.PrimaryPart.Position)
	end
	
	do -- animations
		Animations:StopTrack(plr, 'PlateHold')
		
		Sink.SinkJanitors[plr]:Add(Animations:PlayTrack(plr, 'SinkStart'):GetMarkerReachedSignal'Wash':Connect(function()
			local sinkWashTrack: AnimationTrack = Animations:PlayTrack(plr, 'SinkWashing')
			sinkWashTrack:AdjustSpeed(1.5)
		end))
		
		Sink.SinkJanitors[plr]:Add(function()
			Animations:StopTrack(plr, 'SinkStart', .5)
			Animations:StopTrack(plr, 'SinkWashing', .5)
		end)
	end
	
	local t = TweenService:Create(heldItem.Dirty, TweenInfo.new(Sink.CLEAN_TIME*1.6, Enum.EasingStyle.Linear), {Transparency = 1})
	t:Play()
	
	Sink.SinkJanitors[plr]:Add(task.delay(Sink.CLEAN_TIME, function()
		heldItem.Dirty:Destroy()
		CookingService.DishWashed:Fire()
		Sink.StopInteract(plr)
	end))
	
	Sink.SinkJanitors[plr]:Add(function()
		if heldItem:FindFirstChild'Dirty' then
			t:Cancel()
			heldItem.Dirty.Transparency = 0
		end
		
		plr.Character.Humanoid.AutoRotate = true
		plr.Character.PrimaryPart.Anchored = false
		Sink.ClaimedSinks[interactable] = nil
	end)
end


function Sink.IsActionComplete(ingredient: BasePart)
	return ingredient:FindFirstChild'Dirty' == nil
end


function Sink.CanInteract(plr: Player, interactable: BasePart): boolean
	local heldItem = CookingService.GetHeldItem(plr)
	local itemOnInteractable = CookingService.GetItemOnInteractable(interactable)

	if (heldItem and (heldItem.Name == 'Plate' or heldItem.Name == 'Mug' or heldItem.Name == 'Bowl') and not Sink.IsActionComplete(heldItem)) or
		(itemOnInteractable and (itemOnInteractable.Name == 'Plate' or itemOnInteractable.Name == 'Mug' or itemOnInteractable.Name == 'Bowl')) then
		return true
	end
	
	return false
end


function Sink.StopInteract(plr: Player)
	if not Sink.SinkJanitors[plr] then return end
	Sink.SinkJanitors[plr]:Cleanup()
	
	local heldItem = CookingService.GetHeldItem(plr)
	if heldItem then
		CookingService.PickupItem(plr, heldItem)
	end
end


return Sink