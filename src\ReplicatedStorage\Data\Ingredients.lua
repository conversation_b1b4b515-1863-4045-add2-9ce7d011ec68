--[=[
	Author: <PERSON>ike<PERSON> (Syveric)
	Ingredients data
]=]

export type Ingredient = {
	Image: string,
	StepImages: {string}?,
	PrepSteps: { { Step: 'CuttingBoard' | 'Stove', StoveType: ('FryingPan' | 'Pot')? } },
	ExtraDrop: {string}?,
	PlaceSurfaceType: 'Plate' | 'Mug' | 'Bowl',
	Order: number,
	
	VisualOrder: number?
}


return table.freeze{
	['Lettuce'] = {
		Image = 'rbxassetid://114893932773940',
		PrepSteps = { {Step = 'CuttingBoard'} },
		PlaceSurfaceType = 'Plate',
		Order = 2,
		
		VisualOrder = 3
	},
	
	['Meat'] = {
		Image = 'rbxassetid://108460284768768',
		PrepSteps = { {Step = 'CuttingBoard'}, {Step = 'Stove', StoveType = 'FryingPan'} },
		ExtraDrop = {'rbxassetid://118489907799281'},
		PlaceSurfaceType = 'Plate',
		Order = 4,

		VisualOrder = 1
	},

	['Tomato'] = {
		Image = 'rbxassetid://118435098762059',
		PrepSteps = { {Step = 'CuttingBoard'} },
		PlaceSurfaceType = 'Plate',
		Order = 3,

		VisualOrder = 2
	},

	['Cheese'] = {
		Image = 'rbxassetid://121088064449203',
		PrepSteps = { {Step = 'CuttingBoard'} },
		PlaceSurfaceType = 'Plate',
		Order = 5,

		VisualOrder = 3
	},

	['Onion'] = {
		Image = 'rbxassetid://106388204171746',
		PrepSteps = { {Step = 'CuttingBoard'} },
		PlaceSurfaceType = 'Plate',
		Order = 5,

		VisualOrder = nil
	},
	
	['Bun'] = {
		Image = 'rbxassetid://117053208141972',
		PrepSteps = {},
		PlaceSurfaceType = 'Plate',
		Order = 1,

		VisualOrder = nil
	},
	
	['Tofu'] = {
		Image = 'rbxassetid://130545487408236',
		StepImages = {
			[1] = 'rbxassetid://130545487408236',
			[2] = 'rbxassetid://127082944220489'
		},
		PrepSteps = { {Step = 'Stove', StoveType = 'Pot'}, {Step = 'Stove', StoveType = 'FryingPan'} },
		ExtraDrop = {'rbxassetid://94257110700790', 'rbxassetid://118489907799281'},
		PlaceSurfaceType = 'Plate',
		Order = 1,
		
		VisualOrder = nil
	},
	
	['LuffyMeat'] = {
		Image = 'rbxassetid://132687833150721',
		PrepSteps = { {Step = 'CuttingBoard'}, {Step = 'Stove', StoveType = 'FryingPan'} },
		ExtraDrop = {'rbxassetid://118489907799281'},
		PlaceSurfaceType = 'Plate',
		Order = 4,

		VisualOrder = nil
	},
	
	['Coffee Bean'] = {
		Image = 'rbxassetid://120731300264851',
		PrepSteps = { {Step = 'Stove', StoveType = 'Pot'} },
		ExtraDrop = {'rbxassetid://94257110700790'},
		PlaceSurfaceType = 'Mug',
		Order = 4,

		VisualOrder = nil
	},
	
	['Rice'] = {
		Image = 'rbxassetid://110420846774451',
		PrepSteps = { {Step = 'Stove', StoveType = 'Pot'} },
		ExtraDrop = {'rbxassetid://94257110700790'},
		PlaceSurfaceType = 'Plate',
		Order = 4,

		VisualOrder = nil
	},
	
	['Nori'] = {
		Image = 'rbxassetid://74310053568364',
		PrepSteps = {},
		PlaceSurfaceType = 'Plate',
		Order = 1,

		VisualOrder = nil
	},
	
	['Carrot'] = {
		Image = 'rbxassetid://127497884331687',
		PrepSteps = { {Step = 'CuttingBoard'}, {Step = 'Stove', StoveType = 'Pot'} },
		ExtraDrop = {'rbxassetid://94257110700790'},
		PlaceSurfaceType = 'Bowl',
		Order = 5,

		VisualOrder = nil
	},
	
	['Potato'] = {
		Image = 'rbxassetid://94444847936830',
		PrepSteps = { {Step = 'CuttingBoard'}, {Step = 'Stove', StoveType = 'Pot'} },
		ExtraDrop = {'rbxassetid://94257110700790'},
		PlaceSurfaceType = 'Bowl',
		Order = 5,

		VisualOrder = nil
	},
	
	['Bread'] = {
		Image = 'rbxassetid://112495959618228',
		PrepSteps = {},
		PlaceSurfaceType = 'Plate',
		Order = 1,

		VisualOrder = nil
	},
	
	['Ham'] = {
		Image = 'rbxassetid://134270810696200',
		PrepSteps = { {Step = 'CuttingBoard'}, {Step = 'Stove', StoveType = 'FryingPan'} },
		ExtraDrop = {'rbxassetid://118489907799281'},
		PlaceSurfaceType = 'Plate',
		Order = 4,

		VisualOrder = nil
	}
} :: { [string]: Ingredient }