local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)

local PassiveModule = {}


function PassiveModule.Update(plr: Player)
	if plr.Character and plr.Character:FindFirstChild'Humanoid' then
		plr.Character.Humanoid.WalkSpeed = PassiveModule.Get(plr)
	end
end


function PassiveModule.Get(plr: Player)
	local ChallengeService = Server.GetService('ChallengeService')
	local RoundService = Server.GetService('RoundService')
	local TournamentService = Server.GetService('TournamentService')
	
	local default = Client.Immutable.CharSettings.WALK_SPEED
	
	if ChallengeService.Modifiers[script.Name] then
		default = ChallengeService.Modifiers[script.Name]
	end
	
	if RoundService.IsTournament and TournamentService.ModifierValues[script.Name] then
		default = TournamentService.ModifierValues[script.Name]
	end
	
	local playerCountConfig = Client.Immutable.PLAYER_COUNT_CONFIG[#Players:GetPlayers()]
	if playerCountConfig and playerCountConfig.WalkSpeedMult then
		default *= playerCountConfig.WalkSpeedMult
	end
	
	if plr:GetAttribute('Character') == 'Leorio ParadinightZ' then
		default *= 1.2
	elseif plr:GetAttribute('Character') == 'Bulma' then
		default *= 1.1
	elseif plr:GetAttribute('Character') == 'Kenshiro' then
		default *= 1.15
	elseif plr:GetAttribute('Character') == 'Shoyo Hinata' then
		default *= 1.1
	elseif plr:GetAttribute('Character') == 'Spike Spiegel' then
		default *= 1.15
	elseif plr:GetAttribute('Character') == 'Senku Ishigami' then
		default *= 1.1
	end
	
	if plr:GetAttribute('VampiricAgility') then
		default *= 1.25
	end
	
	if plr:GetAttribute('PrecisionFocus') then
		default *= 1.2
	end
	
	if plr:GetAttribute('AlchemicalRush') then
		default *= 1.2
	end
	
	for _, v in Players:GetPlayers() do
		if v:GetAttribute'FlameSurge' then
			default *= 1.2
		end
	end
	
	return default
end


function PassiveModule.Init()
	local CookingService = Server.GetService('CookingService')
	
	CookingService.ItemThrown:Connect(function(plr: Player)
		if plr:GetAttribute('Character') == 'Mikasa Ackerman' then
			plr:SetAttribute('PrecisionFocus', (plr:GetAttribute'PrecisionFocus' or 0) + 1)
			PassiveModule.Update(plr)
			
			task.delay(5, function()
				plr:SetAttribute('PrecisionFocus', plr:GetAttribute'PrecisionFocus' - 1)
				if plr:GetAttribute'PrecisionFocus' == 0 then plr:SetAttribute('PrecisionFocus', nil) end
				PassiveModule.Update(plr)
			end)
		end
	end)
	
	ClassExtension.Players.PlayerAdded(function(plr: Player)
		ClassExtension.Player.CharacterAdded(plr, function()
			PassiveModule.Update(plr)
		end)
	end)
end


return PassiveModule