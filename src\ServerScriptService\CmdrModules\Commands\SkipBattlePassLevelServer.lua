local ServerScriptService = game:GetService('ServerScriptService')

local Server = require(ServerScriptService.Server)

return function(_, players: Players, amount: number)
	local DataService = Server.GetService('DataService')
	local BattlepassService = Server.GetService('BattlepassService')

	for _, plr in players do
		local profile = DataService.GetProfile(plr)

		profile.Battlepass.Level += amount
		BattlepassService.AddXP(plr, 0)
	end

	return `Levels skipped`
end