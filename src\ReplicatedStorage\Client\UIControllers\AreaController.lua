local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)

local AreaController = {}


local function setupAreasFrame()
	local AreasFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Areas
	
	local Map = workspace:WaitForChild'Map'
	
	for _, v in AreasFrame.Content:GetChildren() do
		if v:IsA'ImageLabel' then v:Destroy() end
	end
	
	for areaName, areaInfo in Client.Immutable.Areas do
		local ThisAreaFrame = ReplicatedStorage.Assets.Templates.AreaContainer:Clone()
		ThisAreaFrame.Description.Text = areaInfo.Description
		ThisAreaFrame.AreaName.Text = areaName
		ThisAreaFrame.Image = areaInfo.Image
		ThisAreaFrame.LayoutOrder = areaInfo.Order
		ThisAreaFrame.Name = areaName
		ThisAreaFrame.Parent = AreasFrame.Content
		
		ThisAreaFrame.Button.MouseButton1Click:Connect(function()
			Players.LocalPlayer.Character.PrimaryPart.CFrame = Map.Areas[areaName].CFrame * CFrame.new(0, 1, 0)
		end)
	end
end


function AreaController.IsArea(area: string): boolean
	workspace:WaitForChild'Map'
	local AreasFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Areas
	return AreasFrame.Content:FindFirstChild(area) ~= nil
end


function AreaController.TeleportToArea(area: string)
	local Map = workspace:WaitForChild'Map'
	Players.LocalPlayer.Character.PrimaryPart.CFrame = Map.Areas[area].CFrame * CFrame.new(0, 1, 0)
end


function AreaController._Start()
	if Client.Immutable.SERVER_TYPE ~= 'Lobby' then return end
	
	local AreasFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.Areas
	
	task.defer(setupAreasFrame)
end


return AreaController