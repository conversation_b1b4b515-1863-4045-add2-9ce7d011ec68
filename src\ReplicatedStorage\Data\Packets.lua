--[=[
	Author: <PERSON>ike<PERSON> (Syveric)
	ByteNet packets
]=]

local ByteNet = require(game:GetService'ReplicatedStorage'.SharedModules.ByteNet)

--[[local REWARD = ByteNet.struct{
	Name = ByteNet.string,
	Count = ByteNet.int32,
	Reward = ByteNet.struct{
		Service = ByteNet.string,
		Function = ByteNet.string,
		Args = ByteNet.array(ByteNet.unknown)
	}
}]]

return {
	Interaction = ByteNet.defineNamespace('Interaction', function(): T
		return {
			Interact = ByteNet.definePacket{
				value = ByteNet.unknown
			},
			
			NearestInteraction = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.unknown)
			},
			
			CancelInteraction = ByteNet.definePacket{
				value = ByteNet.nothing
			},
			
			ThrowFood = ByteNet.definePacket{
				value = ByteNet.vec3
			},
			
			FireExtinguisher = ByteNet.definePacket{
				value = ByteNet.bool
			}
		}
	end),
	
	Round = ByteNet.defineNamespace('Round', function(): T
		return {
			Start = ByteNet.definePacket{
				value = ByteNet.nothing
			},
			
			GetRoundInfo = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.struct{
					RoundStart = ByteNet.uint32,
					OrdersFailed = ByteNet.uint8,
					OrdersCompleted = ByteNet.uint16,
					Act = ByteNet.optional(ByteNet.uint8),
					Difficulty = ByteNet.string,
					FailableOrders = ByteNet.uint32,
					OrdersToComplete = ByteNet.optional(ByteNet.uint16),
					RoundLen = ByteNet.uint32
				})
			},
			
			ScoreUpdated = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.uint16)
			},
			
			RoundEnded = ByteNet.definePacket{
				value = ByteNet.struct{
					Act = ByteNet.optional(ByteNet.uint8),
					MapName = ByteNet.string,
					Difficulty = ByteNet.string,
					OrdersDelivered = ByteNet.uint16,
					OrdersFailed = ByteNet.uint16,
					Tips = ByteNet.uint16,
					Win = ByteNet.bool,
					Elapsed = ByteNet.uint32,
					Score = ByteNet.int32,
					PlayersEndedWith = ByteNet.uint8
				}
			},
			
			ShowTutorial = ByteNet.definePacket{
				value = ByteNet.nothing
			},
			
			VoteReturn = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.uint8)
			},
			
			VoteReplay = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.uint8)
			},
			
			RoundXPGain = ByteNet.definePacket{
				value = ByteNet.struct{
					Level = ByteNet.uint16,
					XP = ByteNet.uint32,
					XPGained = ByteNet.uint16
				}
			}
		}
	end),
	
	Order = ByteNet.defineNamespace('Order', function(): T
		local orderTemplate = ByteNet.struct{
			TimeLeft = ByteNet.uint16,
			Item = ByteNet.string,
			OrderID = ByteNet.string
		}
		
		return {
			CreateOrder = ByteNet.definePacket{
				value = orderTemplate
			},
			
			DeleteOrder = ByteNet.definePacket{
				value = ByteNet.string
			},
			
			GetOrders = ByteNet.definePacket{
				value = ByteNet.nothing
			}
		}
	end),
	
	Ability = ByteNet.defineNamespace('Ability', function(): T
		return {
			UseAbility = ByteNet.definePacket{
				value = ByteNet.struct{
					AbilityName = ByteNet.string,
					Args = ByteNet.array(ByteNet.unknown)
				}
			},
			
			SetCooldown = ByteNet.definePacket{
				value = ByteNet.struct{
					Ability = ByteNet.string,
					StartTime = ByteNet.uint32,
					EndTime = ByteNet.uint32
				}
			}
		}
	end),
	
	EffectController = ByteNet.defineNamespace('EffectController', function(): T
		return {
			Do = ByteNet.definePacket{
				value = ByteNet.struct{
					Effect = ByteNet.string,
					Args = ByteNet.array(ByteNet.unknown)
				}
			}
		}
	end),
	
	Visuals = ByteNet.defineNamespace('Visuals', function(): T
		return {
			DashFX = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.string)
			}
		}
	end),
	
	Loading = ByteNet.defineNamespace('Loading', function(): T
		return {
			PlayersLoaded = ByteNet.definePacket{
				value = ByteNet.nothing
			}
		}
	end),
	
	Matchmaking = ByteNet.defineNamespace('Matchmaking', function(): T
		return {
			CreateLobby = ByteNet.definePacket{
				value = ByteNet.struct{
					Map = ByteNet.string,
					Act = ByteNet.uint8,
					FriendsOnly = ByteNet.bool,
					Difficulty = ByteNet.string
				}
			},
			
			LobbyUpdated = ByteNet.definePacket{
				value = ByteNet.struct{
					Host = ByteNet.string,
					Act = ByteNet.uint8,
					Map = ByteNet.string,
					Players = ByteNet.array(ByteNet.unknown),
					Difficulty = ByteNet.string,
					FriendsOnly = ByteNet.bool,
					UserAdded = ByteNet.optional(ByteNet.string),
					UserRemoved = ByteNet.optional(ByteNet.string)
				}
			},
			
			JoinLobby = ByteNet.definePacket{
				value = ByteNet.string -- Host name
			},
			
			LeaveLobby = ByteNet.definePacket{
				value = ByteNet.nothing
			},
			
			Start = ByteNet.definePacket{
				value = ByteNet.nothing
			},
			
			GetWinrates = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.map(ByteNet.string, ByteNet.array(ByteNet.struct{
					Wins = ByteNet.uint16,
					Losses = ByteNet.uint16,
					MostDelivered = ByteNet.uint16
				})))
			},
			
			PromptLobbyUI = ByteNet.definePacket{
				value = ByteNet.nothing
			},
			
			InvitePlayer = ByteNet.definePacket{
				value = ByteNet.struct{
					PlayerName = ByteNet.string,
					LobbyData = ByteNet.optional(ByteNet.struct{
						MapName = ByteNet.string,
						Act = ByteNet.uint8,
						Difficulty = ByteNet.string,
						Host = ByteNet.string
					})
				},
			}
		}
	end),
	
	Challenge = ByteNet.defineNamespace('Challenge', function(): T
		return {
			UpdateChallengeInterface = ByteNet.definePacket{
				value = ByteNet.struct{
					Map = ByteNet.string,
					ChallengeID = ByteNet.string,
					Players = ByteNet.array(ByteNet.unknown),
					StartingIn = ByteNet.optional(ByteNet.int16),
					Rewards = ByteNet.array(ByteNet.struct{
						Name = ByteNet.string,
						Count = ByteNet.int32,
						Reward = ByteNet.struct{
							Service = ByteNet.string,
							Function = ByteNet.string,
							Args = ByteNet.array(ByteNet.unknown)
						}
					})
				}
			},
			
			LeaveChallenge = ByteNet.definePacket{
				value = ByteNet.nothing
			}
		}
	end),
	
	Time = ByteNet.defineNamespace('Time', function(): T
		return {
			GetPlayerTime = ByteNet.definePacket{
				value = ByteNet.struct{
					t = ByteNet.uint32,
					timeSent = ByteNet.uint32
				}
			},
			
			GetDailyRewardInfo = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.struct{
					Streak = ByteNet.uint16,
					Claimed = ByteNet.array(ByteNet.bool),
					TimeUntilNextDay = ByteNet.uint32
				})
			},
			
			ClaimDailyReward = ByteNet.definePacket{
				value = ByteNet.uint8
			}
		}
	end),
	
	Quest = ByteNet.defineNamespace('Quest', function(): T
		return {
			RequestQuestData = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.struct{
					Category = ByteNet.string,
					QuestID = ByteNet.string,
					Progress = ByteNet.uint32,
					Claimed = ByteNet.bool,
				})
			},
			
			ClaimQuest = ByteNet.definePacket{
				value = ByteNet.struct{
					Category = ByteNet.string,
					QuestID = ByteNet.string
				}
			},
			
			ClaimAllQuests = ByteNet.definePacket{
				value = ByteNet.nothing
			}
		}
	end),
	
	Achievements = ByteNet.defineNamespace('Achievements', function(): T
		return {
			RequestAchievementData = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.struct{
					Name = ByteNet.string,
					
					Achievements = ByteNet.array(ByteNet.struct{
						Name = ByteNet.string,
						Progress = ByteNet.uint32,
						Claimed = ByteNet.bool
					})
				})
			}
		}
	end),
	
	Summon = ByteNet.defineNamespace('Summon', function(): T
		return {
			GetBannerUnits = ByteNet.definePacket{
				value = ByteNet.struct{
					Units = ByteNet.optional(ByteNet.array(ByteNet.string)),
					BannerName = ByteNet.string
				}
			},
			
			GetBannerTimeLeft = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.uint32)
			},
			
			Summon = ByteNet.definePacket{
				value = ByteNet.struct{
					BannerName = ByteNet.string,
					TenTimes = ByteNet.bool
				}
			},
			
			SummonedCharacters = ByteNet.definePacket{
				value = ByteNet.array(ByteNet.string)
			},
			
			TotalSummons = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.struct{
					LegendaryPity = ByteNet.uint16,
					MythicalPity = ByteNet.uint16
				})
			}
		}
	end),
	
	Crates = ByteNet.defineNamespace('Crates', function(): T
		return {
			BuyCrate = ByteNet.definePacket{
				value = ByteNet.struct{
					CrateName = ByteNet.string,
					TenTimes = ByteNet.bool
				}
			},
			
			UpdateCratesOpened = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.struct{
					LegendaryPity = ByteNet.uint16,
					MythicalPity = ByteNet.uint16
				})
			}
		}
	end),
	
	Inventory = ByteNet.defineNamespace('Inventory', function(): T
		return {
			GetEquippedCosmetics = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.array(ByteNet.string))
			},
			
			FeedCharacter = ByteNet.definePacket{
				value = ByteNet.struct{
					Character = ByteNet.string,
					Foods = ByteNet.map(ByteNet.string, ByteNet.uint16)
				}
			},
			
			FuseCharacter = ByteNet.definePacket{
				value = ByteNet.struct{
					BaseCharacter = ByteNet.string,
					FusingCharacters = ByteNet.array(ByteNet.string)
				}
			},
			
			CharacterFed = ByteNet.definePacket{
				value = ByteNet.nothing
			},
			
			EquipCharacter = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.string)
			},
			
			UpdateCharacterInventory = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.map(ByteNet.string, ByteNet.struct{
					Character = ByteNet.string,
					Locked = ByteNet.bool,
					Uses = ByteNet.uint16,
					Deliveries = ByteNet.uint16,
					XP = ByteNet.uint16,
					Level = ByteNet.uint16,
					Equipped = ByteNet.bool,
					Favorite = ByteNet.bool,
					TimeSummoned = ByteNet.uint32
				}))
			},
			
			SellCharacters = ByteNet.definePacket{
				value = ByteNet.array(ByteNet.string)
			},
			
			ToggleLockCharacter = ByteNet.definePacket{
				value = ByteNet.string
			},
			
			ToggleFavoriteCharacter = ByteNet.definePacket{
				value = ByteNet.string
			},
			
			OwnedCharacters = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.array(ByteNet.string))
			},
			
			GetCharacterLevel = ByteNet.definePacket{
				value = ByteNet.struct{
					Level = ByteNet.optional(ByteNet.uint16),
					XP = ByteNet.optional(ByteNet.uint32),
					Character = ByteNet.optional(ByteNet.string)
				}
			},
			
			UpdateItemsInventory = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.map(ByteNet.string, ByteNet.struct{
					TimeCreated = ByteNet.uint32,
					Count = ByteNet.uint32
				}))
			},
			
			UseItem = ByteNet.definePacket{
				value = ByteNet.string
			}
		}
	end),
	
	Codes = ByteNet.defineNamespace('Codes', function(): T
		return {
			RedeemCode = ByteNet.definePacket{
				value = ByteNet.string
			},
			
			CodeSubmitted = ByteNet.definePacket{
				value = ByteNet.struct{
					Color = ByteNet.string,
					Text = ByteNet.string
				}
			}
		}
	end),
	
	HUD = ByteNet.defineNamespace('HUD', function(): T
		return {
			UpdateCoins = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.uint32)
			},
			
			UpdateGems = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.uint32)
			},
			
			UpdateLevel = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.struct{
					XP = ByteNet.uint32,
					Level = ByteNet.uint16
				})
			},
			
			ToggleHUD = ByteNet.definePacket{
				value = ByteNet.struct{
					Enabled = ByteNet.bool,
					Portions = ByteNet.array(ByteNet.string)
				}
			},
			
			SetActionInfo = ByteNet.definePacket{
				value = ByteNet.struct{
					Text = ByteNet.string,
					Priority = ByteNet.uint8
				}
			}
		}
	end),
	
	Battlepass = ByteNet.defineNamespace('Battlepass', function(): T
		return {
			GetPlayerBattlepassInfo = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.struct{
					Level = ByteNet.uint16,
					XP = ByteNet.uint16,
					ClaimedFree = ByteNet.array(ByteNet.bool),
					ClaimedPaid = ByteNet.array(ByteNet.bool)
				})
			},
			
			ClaimTier = ByteNet.definePacket{
				value = ByteNet.struct{
					Tier = ByteNet.uint8,
					Free = ByteNet.bool
				}
			},
			
			ClaimAll = ByteNet.definePacket{
				value = ByteNet.nothing
			}
		}
	end),
	
	AFK = ByteNet.defineNamespace('AFK', function(): T
		return {
			UpdateAFKChamberInfo = ByteNet.definePacket{
				value = ByteNet.struct{
					NextPayout = ByteNet.float32,
					CoinsGained = ByteNet.uint32,
					GemsGained = ByteNet.uint32
				},
				reliabilityType = 'unreliable'
			},
			
			LeaveAFKChamber = ByteNet.definePacket{
				value = ByteNet.bool
			},
			
			AFK = ByteNet.definePacket{
				value = ByteNet.bool
			}
		}
	end),
	
	Tournament = ByteNet.defineNamespace('Tournament', function(): T
		return {
			GetTournamentInfo = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.struct{
					MapName = ByteNet.string,
					Modifiers = ByteNet.array(ByteNet.string),
					TournamentNumber = ByteNet.uint8
				})
			},
			
			GoToTournament = ByteNet.definePacket{
				value = ByteNet.nothing
			},
			
			GetTournamentTimeLeft = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.uint32)
			},
			
			HighScore = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.uint16)
			},
			
			GetPreviousRankings = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.map(ByteNet.string, ByteNet.uint16))
			},
			
			GetTournamentRankings = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.map(ByteNet.string, ByteNet.struct{
					Rank = ByteNet.uint16,
					Value = ByteNet.uint16
				}))
			},
			
			CanClaim = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.bool)
			},
			
			Claim = ByteNet.definePacket{
				value = ByteNet.nothing
			}
		}
	end),
	
	Store = ByteNet.defineNamespace('Store', function(): T
		return {
			OwnedGamepasses = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.array(ByteNet.string))
			},
			
			SetGiftingUser = ByteNet.definePacket{
				value = ByteNet.string
			},
			
			GiftReceived = ByteNet.definePacket{
				value = ByteNet.struct{
					ID = ByteNet.uint32,
					FromUsername = ByteNet.string
				}
			}
		}
	end),
	
	Boosts = ByteNet.defineNamespace('Boosts', function(): T
		return {
			UpdateBoosts = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.array(ByteNet.struct{
					Name = ByteNet.string,
					TimeLeft = ByteNet.uint32
				}))
			}
		}
	end),
	
	Skins = ByteNet.defineNamespace('Skins', function(): T
		return {
			GetSkins = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.map(ByteNet.string, ByteNet.struct{
					
				}))
			},
			
			GetEquipedSkins = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.map(ByteNet.string, ByteNet.string))
			},
			
			SellSkins = ByteNet.definePacket{
				value = ByteNet.array(ByteNet.string)
			}
		}
	end),
	
	Waypoint = ByteNet.defineNamespace('Waypoint', function(): T
		return {
			CreateWaypoint = ByteNet.definePacket{
				value = ByteNet.unknown
			},
			
			RemoveWaypoint = ByteNet.definePacket{
				value = ByteNet.unknown
			},
			
			RemoveAllWaypoints = ByteNet.definePacket{
				value = ByteNet.nothing
			},
			
			StrictSetWaypoints = ByteNet.definePacket{
				value = ByteNet.array(ByteNet.unknown)
			}
		}
	end),

	Tutorial = ByteNet.defineNamespace('Tutorial', function(): T
		return {
			DoTutorial = ByteNet.definePacket{
				value = ByteNet.nothing
			},
			
			GetRoundsCompleted = ByteNet.definePacket{
				value = ByteNet.optional(ByteNet.uint16)
			},
			
			TutorialCompleted = ByteNet.definePacket{
				value = ByteNet.nothing
			},
			
			ReturnToLobby = ByteNet.definePacket{
				value = ByteNet.nothing
			}
		}
	end)
}