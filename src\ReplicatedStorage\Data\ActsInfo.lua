--[=[
	Author: <PERSON><PERSON><PERSON> (S<PERSON>veric)
	Act rewards data
]=]

return table.freeze{
	['Sky'] = {
		Map = 'Sky',
		ServerType = 'Round',
		TutorialImage = 'rbxassetid://104278480200341',
		Order = 2,
		MapImage = 'rbxassetid://138279817971503',

		ActsInfo = {
			[1] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Luffy\'s Meat',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Lu<PERSON>\'s Meat', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Luffy\'s Meat',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Luffy\'s Meat', 1}
							}
						}
					}
				}
			},

			[2] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Luffy\'s Meat',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Luffy\'s Meat', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Luffy\'s Meat',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Luffy\'s Meat', 1}
							}
						}
					}
				}
			},

			[3] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Luffy\'s Meat',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Luffy\'s Meat', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Luffy\'s Meat',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Luffy\'s Meat', 1}
							}
						}
					}
				}
			},

			[4] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Luffy\'s Meat',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Luffy\'s Meat', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Luffy\'s Meat',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Luffy\'s Meat', 1}
							}
						}
					}
				}
			},

			[255] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Luffy\'s Meat',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Luffy\'s Meat', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Luffy\'s Meat',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Luffy\'s Meat', 1}
							}
						}
					}
				}
			},
		}
	},

	['Restaurant'] = {
		Map = 'Restaurant',
		ServerType = 'Round',
		TutorialImage = 'rbxassetid://133240768694048',
		Order = 1,
		MapImage = 'rbxassetid://89597504084156',
		
		ActsInfo = {
			[1] = {
				Easy = {
					MaxOrders = 1,
					FailableOrders = nil,
					OrdersToComplete = 3,
					TimeLimitPerOrder = 999,
					RoundLen = 60*6,
					
					Rewards = {
						{
							Name = 'Gems',
							Count = 10,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Hamburger',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Hamburger', 1}
							}
						}
					}
				},

				Hard = {
					MaxOrders = 1,
					TimeLimitPerOrder = 999,
					RoundLen = 60*6,
					
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Hamburger',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Hamburger', 1}
							}
						}
					}
				}
			},

			[2] = {
				Easy = {
					MaxOrders = 1,
					TimeLimitPerOrder = 999,
					RoundLen = 60*6,
					
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Hamburger',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Hamburger', 1}
							}
						}
					}
				},

				Hard = {
					MaxOrders = 1,
					TimeLimitPerOrder = 999,
					RoundLen = 60*6,
					
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Hamburger',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Hamburger', 1}
							}
						}
					}
				}
			},

			[3] = {
				Easy = {
					MaxOrders = 1,
					TimeLimitPerOrder = 999,
					RoundLen = 60*6,
					
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Hamburger',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Hamburger', 1}
							}
						}
					}
				},

				Hard = {
					MaxOrders = 1,
					TimeLimitPerOrder = 999,
					RoundLen = 60*6,
					
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Hamburger',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Hamburger', 1}
							}
						}
					}
				}
			},

			[4] = {
				Easy = {
					MaxOrders = 1,
					TimeLimitPerOrder = 999,
					RoundLen = 60*6,
					
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Hamburger',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Hamburger', 1}
							}
						}
					}
				},

				Hard = {
					MaxOrders = 1,
					TimeLimitPerOrder = 999,
					RoundLen = 60*6,
					
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Hamburger',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Hamburger', 1}
							}
						}
					}
				}
			},

			[255] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Hamburger',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Hamburger', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Hamburger',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Hamburger', 1}
							}
						}
					}
				}
			},
		}
	},

	['Ghoul Cafe'] = {
		Map = 'Ghoul Cafe',
		ServerType = 'Round',
		TutorialImage = 'rbxassetid://119588620949689',
		Order = 3,
		MapImage = 'rbxassetid://129221114667171',
		
		ActsInfo = {
			[1] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Coffee',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Coffee', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Coffee',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Coffee', 1}
							}
						}
					}
				}
			},

			[2] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Coffee',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Coffee', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Coffee',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Coffee', 1}
							}
						}
					}
				}
			},

			[3] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Coffee',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Coffee', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Coffee',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Coffee', 1}
							}
						}
					}
				}
			},

			[4] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Coffee',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Coffee', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Coffee',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Coffee', 1}
							}
						}
					}
				}
			},

			[255] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Coffee',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Coffee', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Coffee',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Coffee', 1}
							}
						}
					}
				}
			},
		}
	},

	['Infinite Castle'] = {
		Map = 'Infinite Castle',
		ServerType = 'Round',
		TutorialImage = 'rbxassetid://136974684356402',
		Order = 4,
		MapImage = 'rbxassetid://97653551276449',
		
		ActsInfo = {
			[1] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Onigiri',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Onigiri', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Onigiri',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Onigiri', 1}
							}
						}
					}
				}
			},

			[2] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Onigiri',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Onigiri', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Onigiri',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Onigiri', 1}
							}
						}
					}
				}
			},

			[3] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Onigiri',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Onigiri', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Onigiri',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Onigiri', 1}
							}
						}
					}
				}
			},

			[4] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Onigiri',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Onigiri', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Onigiri',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Onigiri', 1}
							}
						}
					}
				}
			},

			[255] = {
				Easy = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Onigiri',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Onigiri', 1}
							}
						}
					}
				},

				Hard = {
					Rewards = {
						{
							Name = 'Gems',
							Count = 2,
							Reward = {
								Service = 'CurrencyService',
								Function = 'AddGems',
								Args = {50, Enum.AnalyticsEconomyTransactionType.Gameplay}
							}
						},
						{
							Name = 'Onigiri',
							Count = 1,
							Reward = {
								Service = 'InventoryService',
								Function = 'AddItem',
								Args = {'Onigiri', 1}
							}
						}
					}
				}
			},
		}
	},
}