local ReplicatedStorage = game:GetService('ReplicatedStorage')
local HttpService = game:GetService('HttpService')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')
local SoundService = game:GetService('SoundService')

local Client = require(ReplicatedStorage.Client)
local Server = require(ServerScriptService.Server)

local ClientImmutable = Client.Immutable
local Signal = require(ReplicatedStorage.SharedModules.Signal)
local Packets = require(ReplicatedStorage.Data.Packets)
local Recipes = require(ReplicatedStorage.Data.Recipes)

local OrderService = {
	Orders = {},
	
	Wave = 1,
	
	OrdersCompleted = 0,
	OrdersFailed = 0,
	
	OrderCreated = Signal.new(),
	OrderDeleted = Signal.new(),
	
	DeliveredDishTracker = {} :: { [Player]: number }
}


local function generateOrders()
	local RoundService: typeof(require(ServerScriptService.Server.GameServices.RoundService)) = Server.GetService('RoundService')
	local AbilityService = Server.GetService('AbilityService')
	
	while true do
		task.wait(1)
		
		local LastOrder = OrderService.GetLastOrder()
		
		local WaitUntilNextOrder = AbilityService.GetPassiveValue(nil, 'OrderRespawnTime')
		local MaxOrders = AbilityService.GetPassiveValue(nil, 'MaxOrders')
		
		if not RoundService.IsRoundActive
			or RoundService.Elapsed() < ClientImmutable.Round.ORDER_INIT
			or (LastOrder and workspace:GetServerTimeNow() - LastOrder.TimeCreated < WaitUntilNextOrder)
			or #OrderService.Orders >= MaxOrders
		then
			continue
		end
		
		OrderService.CreateOrder()
	end
end


function OrderService.GetLastOrder()
	local order = nil
	local t = 0
	
	for _, v in OrderService.Orders do
		if v.TimeCreated > t then
			t = v.TimeCreated
			order = v
		end
	end
	
	return order
end


function OrderService.GetTimeLeftForOrder(order)
	local AbilityService = Server.GetService('AbilityService')
	
	local isSmallOrder = #OrderService.GetIngredientsFromOrder(order.Item) < 3
	local orderLen = AbilityService.GetPassiveValue(isSmallOrder, 'OrderLen')
	
	local elapsed = workspace:GetServerTimeNow() - order.TimeCreated
	
	return orderLen - elapsed
end


function OrderService.GetUniqueIngredientsInActiveOrders()
	local ingredients = {}
	
	for _, order in OrderService.Orders do
		for _, v in OrderService.GetIngredientsFromOrder(order.Item) do
			if not table.find(ingredients, v) then
				table.insert(ingredients, v)
			end
		end
	end
	
	return ingredients
end


function OrderService.GetFoodFromIngredients(ingredients: {string}): string?
	for food, foodInfo in Recipes do
		local foodIngredients = foodInfo.Ingredients

		if #foodIngredients == #ingredients then
			local found = 0
			for _, v in ingredients do
				if table.find(foodIngredients, v) then
					found += 1
				end
			end

			if found == #ingredients then
				return food
			end
		end
	end
end


function OrderService.DoesOrderExistWithIngredients(ingredients: {string}): boolean
	local submittingFood = OrderService.GetFoodFromIngredients(ingredients)
	
	for _, order in OrderService.Orders do
		if order.Item == submittingFood then
			return true
		end
	end
	
	return false
end


function OrderService.GetValidIngredientsForMap(mapName: string?): {string}
	local mapName = mapName or Server.GetService'RoundService'.MapName
	
	if not mapName then return {} end
	
	local res = {}
	for _, v in Recipes do
		if table.find(v.Maps, mapName) then
			for _, ingredientName in v.Ingredients do
				if not table.find(res, ingredientName) then
					table.insert(res, ingredientName)
				end
			end
		end
	end
	
	return res
end


function OrderService.SubmitOrder(ingredients: {string}, plr: Player)
	local submittedFood = OrderService.GetFoodFromIngredients(ingredients)
	
	for _, v in OrderService.Orders do
		if v.Item == submittedFood then
			OrderService.DeleteOrder(v.OrderID, nil, plr)
			return true
		end
	end
	
	return false
end


function OrderService.GetValidRecipesForCurrentMap(): {string}
	local mapName = Server.GetService'RoundService'.MapName
	
	if not mapName then return {} end
	
	local res = {}
	for food, foodInfo in Recipes do
		if table.find(foodInfo.Maps, mapName) then
			table.insert(res, food)
		end
	end
	return res
end


function OrderService.CleanupOrders()
	for _, v in OrderService.Orders do
		task.spawn(OrderService.DeleteOrder, v.OrderID)
	end
end


function OrderService.CreateOrder(recipe: string?)
	local validRecipes = OrderService.GetValidRecipesForCurrentMap()
	
	local thisRecipe = recipe or validRecipes[math.random(1, #validRecipes)]
	
	local orderData = {
		Item = thisRecipe,
		TimeCreated = workspace:GetServerTimeNow(),
		OrderID = HttpService:GenerateGUID(false)
	}
	
	table.insert(OrderService.Orders, orderData)
	
	Packets.Order.CreateOrder.sendToAll({
		Item = orderData.Item,
		TimeLeft = OrderService.GetTimeLeftForOrder(orderData),
		OrderID = orderData.OrderID
	})
	
	task.delay(OrderService.GetTimeLeftForOrder(orderData), OrderService.DeleteOrder, orderData.OrderID, true)
	
	OrderService.OrderCreated:Fire(orderData)
end


function OrderService.DeleteOrder(orderID: string, expired: boolean?, plr: Player)
	for idx, v in OrderService.Orders do
		if v.OrderID == orderID then
			table.remove(OrderService.Orders, idx)
			OrderService.OrderDeleted:Fire(v, expired, plr)
			Packets.Order.DeleteOrder.sendToAll(orderID)
			break
		end
	end
end


function OrderService.GetIngredientsFromOrder(order: string)
	return Recipes[order].Ingredients
end


function OrderService._PlayerAdded(plr: Player)
	OrderService.DeliveredDishTracker[plr] = 0
end


function OrderService._Start()
	local RoundService = Server.GetService('RoundService')
	local DataService = Server.GetService('DataService')
	local CharacterService = Server.GetService('CharacterService')
	local CurrencyService = Server.GetService('CurrencyService')
	
	RoundService.RoundEndedSignal:Connect(OrderService.CleanupOrders)
	
	RoundService.RoundStartedSignal:Connect(function()
		OrderService.OrdersCompleted = 0
		OrderService.OrdersFailed = 0
		OrderService.Wave = 1
	end)
	
	Packets.Order.GetOrders.listen(function(_, plr: Player)
		for _, v in OrderService.Orders do
			Packets.Order.CreateOrder.sendTo({
				Item = v.Item,
				TimeLeft = OrderService.GetTimeLeftForOrder(v),
				OrderID = v.OrderID
			}, plr)
		end
	end)
	
	task.defer(generateOrders)
	
	OrderService.OrderDeleted:Connect(function(orderData, expired: boolean?, plr: Player?)
		if expired then
			OrderService.OrdersFailed += 1
			
			local FailableOrders = RoundService.GetFailableOrders()
			if OrderService.OrdersFailed >= FailableOrders then
				RoundService.EndRound(false, false)
			end
			
			RoundService.SendRoundData()
		elseif plr then
			SoundService.SFX.Ding:Play()

			OrderService.OrdersCompleted += 1

			if OrderService.OrdersCompleted % 5 == 0 then
				OrderService.Wave = math.min(OrderService.Wave + 1, #ClientImmutable.Round.WaveOrderSpeeds)
			end

			for _, v in Players:GetPlayers() do
				CurrencyService.AddXP(v, 1)
				CharacterService.AddXPToCharacter(v, 1)
			end
			
			DataService.Get(plr, 'TutorialStatus')[RoundService.MapName].CompletedAnOrder = true
			Server.GetService'QuestService'.UpdateQuestProgress(plr, 'FinishOrder', 1)
			OrderService.DeliveredDishTracker[plr] += 1
			
			local OrdersToComplete = RoundService.GetOrdersToComplete()
			if OrderService.OrdersCompleted >= OrdersToComplete then
				RoundService.EndRound(false, true)
			end
			
			RoundService.SendRoundData()
		end
	end)
end


return OrderService