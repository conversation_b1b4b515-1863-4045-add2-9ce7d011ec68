local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local RunService = game:GetService('RunService')

local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsClient)

local SummonFolder = workspace.Summon

local OldRig = SummonFolder.CrateAnim.NormalChar
local Rig = SummonFolder.CrateAnim.NormalChar:Clone()
Rig.Name = 'NormalChar'
Rig:PivotTo(SummonFolder.CrateAnim.NormalChar:GetPivot())
Rig.Parent = SummonFolder
OldRig:Destroy()

local OpenAnim = Instance.new('Animation')
OpenAnim.AnimationId = 'rbxassetid://109908426128285'

local initTopCf = SummonFolder.CrateAnim.NormalTable.Top.CFrame

local track = Rig.Humanoid.Animator:LoadAnimation(OpenAnim)

return function(firstCharRarity: string)
	local charDesc = ClassExtension.Humanoid.GetHumanoidDescriptionFromUserId(Players.LocalPlayer.UserId)
	
	Rig.Humanoid:ApplyDescription(charDesc)
	
	for _, v in Rig:GetChildren() do
		if v:IsA'Accessory' then
			v:Destroy()
		end
	end
	
	Rig.Torso.Transparency = 1
	Rig.Head.Transparency = 1
	
	for _, v in SummonFolder.CrateAnim.NormalTable.Bottom:GetChildren() do
		if v:IsA'Attachment' then v:Destroy() end
	end
	
	local camera = workspace.CurrentCamera
	local oldCamCf = camera.CFrame
	
	camera.CameraType = Enum.CameraType.Scriptable
	
	local rsc = RunService.PreRender:Connect(function(dtr: number)
		camera.CFrame = Rig.Head.CFrame
	end)
	
	local weld = Instance.new('WeldConstraint')
	track:GetMarkerReachedSignal'Grab':Once(function()
		local targetObject = SummonFolder.CrateAnim.NormalTable.Top
		
		targetObject.Anchored = false
		weld.Part0 = Rig['Right Arm']
		weld.Part1 = targetObject
		weld.Parent = targetObject
		weld.Name = 'GrabWeld'
		
		for _, v in ReplicatedStorage.Assets.Visuals.SummonVFX[firstCharRarity]:GetChildren() do
			v:Clone().Parent = SummonFolder.CrateAnim.NormalTable.Bottom
		end
	end)
	
	track.TimePosition = 0
	track:Play()
	track:AdjustSpeed(1.3)
	
	track.Stopped:Wait()
	track:Play(0)
	track.TimePosition = track.Length * .99
	track:AdjustSpeed(0)
	
	return function()
		rsc:Disconnect()
		
		weld:Destroy()
		SummonFolder.CrateAnim.NormalTable.Top.Anchored = true
		SummonFolder.CrateAnim.NormalTable.Top.CFrame = initTopCf
		
		camera.CameraType = Enum.CameraType.Custom
		camera.CameraSubject = Players.LocalPlayer.Character.Humanoid
		camera.CFrame = oldCamCf
		
		track:AdjustSpeed(1)
	end
end