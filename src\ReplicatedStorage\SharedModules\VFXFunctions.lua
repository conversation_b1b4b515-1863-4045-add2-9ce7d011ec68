--[=[
	Author: <PERSON><PERSON><PERSON> (Syveric)
	VFXFunctions
]=]

local VFXFunctions = {}


function VFXFunctions.EmitDescendants(object: Instance, count: number?)
	local items = object:GetDescendants()
	table.insert(items, object)
	
	for _,v in items do
		if v:IsA'ParticleEmitter' then
			VFXFunctions.Emit(v, count)
		end
	end
end


function VFXFunctions.Emit(particle: ParticleEmitter, count: number?)
	task.delay(particle:GetAttribute'EmitDelay' or 0,function()
		particle:Emit(count or particle:GetAttribute'EmitCount' or 30)

		if particle:GetAttribute'EmitDuration' then
			particle.Enabled = true
			task.delay(particle:GetAttribute'EmitDuration', function()
				particle.Enabled = false
			end)
		end
	end)
end


function VFXFunctions.DisableDescendants(object: Instance, destroy: boolean?)
	local items = object:GetDescendants()
	table.insert(items, object)
	
	for _,v in items do
		if v:IsA'ParticleEmitter' then
			v.Enabled = false

			if destroy ~= false then
				task.delay(v.Lifetime.Max + 2,function()
					v:Destroy()
				end)
			end
		end
	end
end


function VFXFunctions.EnableDescendants(object: Instance)
	local items = object:GetDescendants()
	table.insert(items, object)
	
	for _,v in items do
		if v:IsA'ParticleEmitter' then
			v.Enabled = true
		end
	end
end


function VFXFunctions.EnableTrails(object: Instance)
	for _,v in object:GetDescendants() do
		if v:IsA'Trail' then
			v.Enabled = true
		end
	end
end


function VFXFunctions.DisableTrails(object: Instance, destroy: boolean?)
	for _,v in object:GetDescendants() do
		if v:IsA'Trail' then
			v.Enabled = false

			if destroy ~= false then
				task.delay(v.Lifetime, function()
					v:Destroy()
				end)
			end
		end
	end
end


function VFXFunctions.GetHighestWaitTime(object: Instance): number
	local highest = 0

	local items = object:GetDescendants()
	table.insert(items, object)
	
	for _,v in items do
		if v:IsA'ParticleEmitter' then
			highest = math.max(highest, v.Lifetime.Max + 1)
		end
	end

	return highest
end


do
	local function scaleNumberSequence(ns: NumberSequence, scale: number): NumberSequence
		local keypoints = ns.Keypoints
		for i = 1, #keypoints do
			keypoints[i] = NumberSequenceKeypoint.new(keypoints[i].Time, keypoints[i].Value * scale, keypoints[i].Envelope * scale)
		end
		return NumberSequence.new(keypoints)
	end

	local function scaleNumberRange(nr: NumberRange, scale: number): NumberRange
		return NumberRange.new(nr.Min * scale, nr.Max * scale)
	end

	function VFXFunctions.ScaleParticle(particle: ParticleEmitter, scale: number)
		particle.Size = scaleNumberSequence(particle.Size, scale)
		--particle.ZOffset = particle.ZOffset * scale
		particle.Acceleration = particle.Acceleration * scale
		particle.Speed = scaleNumberRange(particle.Speed, scale)
	end
	
	function VFXFunctions.ScaleDescendants(object: Instance, scale: number)
		for _,v in object:GetDescendants() do
			if v:IsA'ParticleEmitter' then
				VFXFunctions.ScaleParticle(v, scale)
			end
		end
	end
end


return VFXFunctions