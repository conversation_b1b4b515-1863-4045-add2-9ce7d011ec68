local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local TweenService = game:GetService('TweenService')
local RunService = game:GetService('RunService')
local GuiService = game:GetService('GuiService')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local Recipes = require(ReplicatedStorage.Data.Recipes)
local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local Ingredients = require(ReplicatedStorage.Data.Ingredients)
local Math = require(ReplicatedStorage.SharedModules.Math)

local OrderUIAssets = ReplicatedStorage.Assets.Templates.Orders

local OrderController = {}


local function createOrder(data: { Item: string, TimeLeft: number, OrderID: string })
	if Players.LocalPlayer.PlayerGui.GameplayUI.Orders:FindFirstChild(data.OrderID) then return end

	local EffectController = Client.GetController('EffectController')

	local OrderIngredients = OrderController.GetIngredientsFromOrder(data.Item)

	local OrderContainer = (#OrderIngredients <= 2 and OrderUIAssets.SmallOrder or OrderUIAssets.BigOrder):Clone()
	local OrderContainerJanitor = Janitor.new()

	OrderContainer.Name = data.OrderID

	do -- ingredients
		for _, v in OrderContainer.BaseFrame.SomeIngredients:GetChildren() do
			if v:IsA'GuiObject' then v:Destroy() end
		end
		
		for _, v in OrderIngredients do
			local extraCount = Ingredients[v].ExtraDrop and #Ingredients[v].ExtraDrop
			local thisIngredientFrame = (extraCount and OrderUIAssets['IngredientExtra' .. extraCount] or OrderUIAssets.Ingredient):Clone()
			thisIngredientFrame.Icon.Image = Ingredients[v].Image
			thisIngredientFrame.LayoutOrder = Ingredients[v].Order
			thisIngredientFrame.Parent = OrderContainer.BaseFrame.SomeIngredients
			
			if extraCount then
				for idx, img in Ingredients[v].ExtraDrop do
					thisIngredientFrame['Icon' .. (idx + 1)].Image = img
				end
			end
		end
	end
	
	OrderContainer.BaseFrame.Icon.Image = Recipes[data.Item].Image

	OrderContainer.Parent = Players.LocalPlayer.PlayerGui.GameplayUI.Orders
	OrderContainerJanitor:LinkToInstance(OrderContainer)

	local originalTimeLeft = data.TimeLeft
	OrderContainerJanitor:Add(RunService.RenderStepped:Connect(function(dt: number)
		data.TimeLeft = math.max(0, data.TimeLeft - dt)
		
		local fillRatio = data.TimeLeft/originalTimeLeft
		
		OrderContainer.BaseFrame.Timer.Bar.Size = UDim2.fromScale(fillRatio, 1)
		
		if fillRatio > 2/3 then
			OrderContainer.BaseFrame.Timer.Bar.BackgroundColor3 = Color3.fromRGB(156, 255, 151)
		elseif fillRatio > 1/3 then
			if OrderContainer.BaseFrame.Timer.Bar.BackgroundColor3 ~= Color3.fromRGB(255, 193, 7) then
				OrderContainer.BaseFrame.Timer.Bar.BackgroundColor3 = Color3.fromRGB(255, 193, 7)
				EffectController.Do(
					'Spiral',
					OrderContainer.BaseFrame,
					2.5, .4, 3.5, true, true
				)
			end
			OrderContainer.BaseFrame.Timer.Bar.BackgroundColor3 = Color3.fromRGB(255, 193, 7)
		else
			if OrderContainer.BaseFrame.Timer.Bar.BackgroundColor3 ~= Color3.fromRGB(255, 78, 87) then
				OrderContainer.BaseFrame.Timer.Bar.BackgroundColor3 = Color3.fromRGB(255, 78, 87)
				EffectController.Do(
					'Spiral',
					OrderContainer.BaseFrame,
					1.3, data.TimeLeft, 8 * data.TimeLeft, nil, true
				)
			end
			OrderContainer.BaseFrame.Timer.Bar.BackgroundColor3 = Color3.fromRGB(255, 78, 87)
		end
	end))

	OrderContainerJanitor:Add(task.delay(originalTimeLeft, OrderController.DeleteOrder, data.OrderID))

	do -- animation
		OrderContainer.Visible = true

		OrderContainer.BaseFrame.Position = UDim2.fromScale(.5, -.65)
		OrderContainer.BaseFrame.SomeIngredients.Position = UDim2.fromScale(.5, .5)

		local t = TweenService:Create(
			OrderContainer.BaseFrame,
			TweenInfo.new(.22, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
			{Position = UDim2.fromScale(.5, .5)}
		);t:Play();

		t.Completed:Wait()

		local t = TweenService:Create(
			OrderContainer.BaseFrame.SomeIngredients,
			TweenInfo.new(.3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
			{Position = UDim2.fromScale(.5, .785)}
		);t:Play();
	end
end


local function updateScore(score: number)
	local ScoreDisplayer = Players.LocalPlayer.PlayerGui.GameplayUI.Score.TextLabel
	ScoreDisplayer.Text = Math.Comma(score)
end


function OrderController.GetIngredientsFromOrder(order: string)
	return Recipes[order].Ingredients
end


function OrderController.DeleteOrder(orderID: string)
	local foundOrderItem = Players.LocalPlayer.PlayerGui.GameplayUI.Orders:FindFirstChild(orderID)
	if not foundOrderItem or foundOrderItem:GetAttribute'Deleting' then return end
	foundOrderItem:SetAttribute('Deleting', true)

	local EffectController = Client.GetController('EffectController')
	EffectController.Do(
		'Spiral',
		foundOrderItem.BaseFrame,
		1, .1, 1, true, true
	)

	local t = TweenService:Create(
		foundOrderItem.BaseFrame.SomeIngredients,
		TweenInfo.new(.3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
		{Position = UDim2.fromScale(.5, .45)}
	);t:Play();

	t.Completed:Wait()

	local t = TweenService:Create(
		foundOrderItem.BaseFrame,
		TweenInfo.new(.24, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
		{Position = UDim2.fromScale(.5, -1.05)}
	);t:Play();
	local t = TweenService:Create(
		foundOrderItem,
		TweenInfo.new(.45, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
		{Size = UDim2.fromScale(-Players.LocalPlayer.PlayerGui.GameplayUI.Orders.UIListLayout.Padding.Scale, foundOrderItem.Size.Y.Scale)}
	);t:Play();

	t.Completed:Wait()

	foundOrderItem:Destroy()
end


function OrderController._Start()
	Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'GameplayUI'

	for _, v in Players.LocalPlayer.PlayerGui.GameplayUI.Orders:GetChildren() do
		if v:IsA'GuiObject' then v:Destroy() end
	end
	
	Packets.Round.ScoreUpdated.listen(updateScore)
	Packets.Round.ScoreUpdated.send()
	
	Packets.Order.CreateOrder.listen(createOrder)
	Packets.Order.DeleteOrder.listen(OrderController.DeleteOrder)
	
	Packets.Order.GetOrders.send()
	
	Packets.Round.RoundEnded.listen(function()
		for _, v in Players.LocalPlayer.PlayerGui.GameplayUI.Orders:GetChildren() do
			if v:IsA'GuiObject' then
				task.spawn(OrderController.DeleteOrder, v.Name)
			end
		end
	end)
	
	workspace.Camera:GetPropertyChangedSignal'ViewportSize':Connect(function()
		Players.LocalPlayer.PlayerGui.GameplayUI.Orders.Position = UDim2.fromOffset(GuiService.TopbarInset.Min.X, 0)
	end);GuiService:GetPropertyChangedSignal'TopbarInset':Connect(function()
		Players.LocalPlayer.PlayerGui.GameplayUI.Orders.Position = UDim2.fromOffset(GuiService.TopbarInset.Min.X, 0)
	end);Players.LocalPlayer.PlayerGui.GameplayUI.Orders.Position = UDim2.fromOffset(GuiService.TopbarInset.Min.X, 0);
end


return OrderController