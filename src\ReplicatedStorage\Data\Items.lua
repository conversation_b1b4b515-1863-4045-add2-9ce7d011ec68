--[=[
	Author: <PERSON><PERSON><PERSON> (Syveric)
	Items data
]=]

export type Item = {
	CosmeticType: 'Torso' | 'Hat',
	XPReward: number?,
	MarketplaceKey: string?,
	Duration: string?,
	Boost: number?,
	MapAssociations: {string}?,
	
	Description: string,
	Type: 'Food' | 'Gamepass' | 'Boost' | 'Cosmetic',
	Rarity: 'Mythical' | 'Legendary' | 'Epic' | 'Rare' | 'Common',
	Image: string?,
}

return {
	['Hamburger'] = {
		XPReward = 5,
		MapAssociations = {'Restaurant'},
		
		Description = 'Use this to feed your characters',
		Type = 'Food',
		Rarity = 'Rare',
		
		Image = 'rbxassetid://83475613934523'
	},
	
	['Coffee'] = {
		XPReward = 5,
		MapAssociations = {'Ghoul Cafe'},
		
		Description = 'Use this to feed your characters',
		Type = 'Food',
		Rarity = 'Rare',
		
		Image = 'rbxassetid://119430324030637'
	},
	
	['Onigiri'] = {
		XPReward = 5,
		MapAssociations = {'Infinite Castle'},
		
		Description = 'Use this to feed your characters',
		Type = 'Food',
		Rarity = 'Rare',
		
		Image = 'rbxassetid://102216635570001'
	},
	
	['Luffy\'s Meat'] = {
		XPReward = 5,
		MapAssociations = {'Sky'},
		
		Description = 'Use this to feed your characters',
		Type = 'Food',
		Rarity = 'Rare',
		
		Image = 'rbxassetid://132687833150721'
	},
	
	['Premium Pass'] = {
		MarketplaceKey = 'Premium Pass',
		
		Description = 'Premium Pass',
		Type = 'Gamepass',
		Rarity = 'Mythical'
	},
	
	['VIP'] = {
		MarketplaceKey = 'VIP',

		Description = 'VIP',
		Type = 'Gamepass',
		Rarity = 'Mythical'
	},
	
	['10% Luck Boost Potion'] = {
		Duration = 60*10,
		Boost = 10,
		
		Description = '10% Luck Boost Potion',
		Type = 'Boost',
		Rarity = 'Mythical',
		
		Image = 'rbxassetid://90871310291616'
	},
	
	['20% Luck Boost Potion'] = {
		Duration = 60*30,
		Boost = 20,

		Description = '20% Luck Boost Potion',
		Type = 'Boost',
		Rarity = 'Mythical',

		Image = 'rbxassetid://122759042686729'
	},
	
	['Executive Chef'] = {
		CosmeticType = 'Torso',
		
		Description = 'cosmetic text',
		Type = 'Cosmetic',
		Rarity = 'Rare',
		
		Image = nil
	}
} :: { [string]: Item }