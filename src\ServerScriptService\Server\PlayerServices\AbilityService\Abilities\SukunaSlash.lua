local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local SoundService = game:GetService('SoundService')

local Server = require(ServerScriptService.Server)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local CookingService = Server.GetService('CookingService')

local SukunaSlash = {
	COOLDOWN = 10,
	
	Cooldowns = {} :: { [Player]: number },
	AbilityJanitors = {} :: { [Player]: Janitor.Janitor }
}


function SukunaSlash.GetCooldownTimeLeft(plr: Player): number
	if SukunaSlash.Cooldowns[plr] then
		return SukunaSlash.COOLDOWN - (os.clock() - SukunaSlash.Cooldowns[plr])
	end
	return 0
end


function SukunaSlash.CanUse(plr: Player): boolean
	if SukunaSlash.Cooldowns[plr] then
		return os.clock() - SukunaSlash.Cooldowns[plr] > SukunaSlash.COOLDOWN
	end
	
	return true
end


function SukunaSlash.IsInUse(plr: Player): boolean
	if SukunaSlash.AbilityJanitors[plr] and SukunaSlash.AbilityJanitors[plr]:Get('Active') then
		return true
	end

	return false
end


function SukunaSlash.UseAbility(plr: Player): boolean
	if not SukunaSlash.CanUse(plr) or SukunaSlash.IsInUse(plr) then return false end

	SukunaSlash.Cooldowns[plr] = os.clock()

	task.delay(SukunaSlash.COOLDOWN, function()
		SukunaSlash.Cooldowns[plr] = nil
	end)

	if SukunaSlash.AbilityJanitors[plr] then
		SukunaSlash.AbilityJanitors[plr]:Destroy()
	end

	SukunaSlash.AbilityJanitors[plr] = Janitor.new()

	SukunaSlash.AbilityJanitors[plr]:Add(math.random, nil, 'Active')
	
	ClassExtension.Player.DisableMovement(plr, true)
	
	local SlashTrack: AnimationTrack = Animations:PlayTrack(plr, 'SukunaSlash')
	SlashTrack:AdjustSpeed(2.85)

	local SlashFX = SukunaSlash.AbilityJanitors[plr]:Add(script.Slashes:Clone())
	SukunaSlash.AbilityJanitors[plr]:Add(SlashTrack:GetMarkerReachedSignal'Start':Once(function()
		SoundService.SFX.Abilities.SukunaSlash:Play()
		
		SlashFX.CFrame = plr.Character.HumanoidRootPart.CFrame
		SlashFX.Floor.CFrame = plr.Character.HumanoidRootPart.CFrame * CFrame.new(0, -2.6, 0)
		SlashFX.Parent = workspace
		
		VFXFunctions.EnableDescendants(SlashFX)
	end))
	
	SukunaSlash.AbilityJanitors[plr]:Add(SlashTrack.Ended:Connect(function()
		ClassExtension.Player.DisableMovement(plr, false)
		VFXFunctions.DisableDescendants(SlashFX)
		
		local interactablesInRegion = CookingService.GetInteractablesInRegion(plr.Character.PrimaryPart.Position, 10)
		for _, interactable in interactablesInRegion do
			local ingredient = CookingService.GetIngredientOnInteractable(interactable)
			if ingredient then
				CookingService.KitchenwareModules.CuttingBoard.SetIngredientAsCut(ingredient)
			end
		end
		
		task.wait(VFXFunctions.GetHighestWaitTime(SlashFX))
		
		SukunaSlash.AbilityJanitors[plr]:Cleanup()
	end))
	
	SukunaSlash.AbilityJanitors[plr]:Add(function()
		ClassExtension.Player.DisableMovement(plr, false)
		Animations:StopTrack(plr, 'SukunaSlash')
	end)
	
	return true
end


function SukunaSlash.CancelAbility(plr: Player)
	if not SukunaSlash.IsInUse(plr) then return end

	if SukunaSlash.AbilityJanitors[plr] then
		SukunaSlash.AbilityJanitors[plr]:Cleanup()
	end
end


return SukunaSlash