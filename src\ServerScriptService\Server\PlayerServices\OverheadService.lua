local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local ServerScriptService = game:GetService('ServerScriptService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)

local OverheadService = {}

local OverheadObj = ReplicatedStorage.Assets.Templates.Overhead
local NametagAdornees: { [Player]: BillboardGui } = {}


function OverheadService._PlayerAdded(plr: Player)
	local DataService = Server.GetService('DataService')
	local RankService = Server.GetService('RankService')
	
	local nametag = OverheadObj:Clone()
	nametag.Frame.PlayerName.Text = plr.DisplayName
	nametag.Frame.Rank.Visible = false
	nametag.Name = plr.Name
	nametag.Parent = workspace.Overheads

	NametagAdornees[plr] = nametag

	local function charAdded(character: Model)
		repeat task.wait() until character.Humanoid['HumanoidDescription']
		character:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>('HumanoidRootPart')
		
		character.Humanoid.DisplayDistanceType = Enum.HumanoidDisplayDistanceType.None
		character.Humanoid.NameOcclusion = Enum.NameOcclusion.NoOcclusion

		if NametagAdornees[plr] then
			NametagAdornees[plr].Adornee = character.Head
		end

		--[[local c: RBXScriptConnection
		c = character.Humanoid.HumanoidDescription.Changed:Connect(function()
			c:Disconnect()
			if nametagAdornees[plr] and not nametagAdornees[plr].Adornee then
				nametagAdornees[plr].Adornee = character:WaitForChild('HumanoidRootPart')
			end
		end)]]
	end

	if plr.Character then
		coroutine.wrap(charAdded)(plr.Character)
	end;plr.CharacterAdded:Connect(charAdded);
	
	NametagAdornees[plr].Frame.Level.Text = `Lvl {DataService.Get(plr, 'Level')}`
	
	if RankService.GetRank(plr) > 1 then
		local roleName = RankService.GetRole(plr)
		nametag.Frame.Rank.Text = roleName
		nametag.Frame.Rank.Visible = true
	end
end


function OverheadService._PlayerRemoving(plr: Player)
	if NametagAdornees[plr] then
		NametagAdornees[plr]:Destroy()
		NametagAdornees[plr] = nil
	end
end


function OverheadService._Start()
	local DataService = Server.GetService('DataService')
	
	DataService.DataUpdated:Connect(function(plr: Player, key: string, newValue: any)
		if key == 'Level' and NametagAdornees[plr] then
			NametagAdornees[plr].Frame.Level.Text = `Lvl {newValue}`
		end
	end)
end


function OverheadService._Init()
	if not workspace:FindFirstChild'Overheads' then
		local folder = Instance.new('Folder')
		folder.Name = 'Overheads'
		folder.Parent = workspace
	end
end


return OverheadService