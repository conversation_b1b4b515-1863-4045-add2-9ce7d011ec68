local ReplicatedStorage = game:GetService('ReplicatedStorage')

local Items = require(ReplicatedStorage.Data.Items)

local CombinedCosmetics = {}

for k, v in Items do
	if v.Type ~= 'Cosmetic' then continue end
	table.insert(CombinedCosmetics, k)
end


return function(cmdr)
	local Util = cmdr.Cmdr.Util

	local cosmeticType = {
		Transform = function(text)
			local findCharacter = Util.MakeFuzzyFinder(CombinedCosmetics)
			return findCharacter(text)
		end,

		Validate = function(characters)
			return #characters > 0
		end,

		Autocomplete = function(characters)
			return Util.GetNames(characters)
		end,

		Parse = function(characters)
			return characters[1]
		end
	}

	cmdr:RegisterType('cosmetic', cosmeticType)
	cmdr:RegisterType('cosmetics', Util.MakeListableType(cosmeticType))
end