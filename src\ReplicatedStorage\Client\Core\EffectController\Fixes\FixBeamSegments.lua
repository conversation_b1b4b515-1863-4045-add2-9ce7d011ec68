local ret: ((ParticleEmitter) -> ())?


workspace.DescendantAdded:Connect(function(c: Instance)
	if c:IsA'Beam' then
		ret(c)
	end
end)


ret = function(beamObject: Beam)
	local Attachment0 = beamObject.Attachment0
	local Attachment1 = beamObject.Attachment1

	if not Attachment0 or not Attachment1 then return end

	if not beamObject:GetAttribute'DesiredSegments' then
		local c c = UserSettings().GameSettings:GetPropertyChangedSignal'SavedQualityLevel':Connect(function()
			ret(beamObject)
			c:Disconnect()
		end)

		beamObject.AncestryChanged:Connect(function()
			if not beamObject:IsDescendantOf(game) then
				c:Disconnect()
			end
		end)
	end

	local SegmentCount = beamObject:GetAttribute('DesiredSegments')
	if not SegmentCount then
		SegmentCount = beamObject.Segments
		beamObject:SetAttribute('DesiredSegments', beamObject.Segments)
	end

	local CameraLocation = workspace.CurrentCamera.CFrame
	local Distance = math.max((CameraLocation.Position - Attachment0.WorldPosition).Magnitude, (CameraLocation.Position - Attachment1.WorldPosition).Magnitude)

	local QualityFactor = math.max(0, math.min(1, UserSettings().GameSettings.SavedQualityLevel.Value / 10))
	local QualityDistanceScalar = math.clamp((1 - (Distance - 200) / 800) * QualityFactor, .1, 1)

	beamObject.Segments = math.ceil(SegmentCount / QualityDistanceScalar)
end


return ret