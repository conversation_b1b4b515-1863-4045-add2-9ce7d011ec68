local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Packets = require(ReplicatedStorage.Data.Packets)

local FoodThrowing = {
	Key = Enum.KeyCode.Space,

	GamepadKey = Enum.KeyCode.ButtonY
}


function FoodThrowing.KeyDown()
	local Character = Players.LocalPlayer.Character
	local movDir = Character.Humanoid.MoveDirection
	Packets.Interaction.ThrowFood.send(movDir.Magnitude > 0 and movDir or Character.PrimaryPart.CFrame.LookVector)
end


function FoodThrowing.KeyUp()

end


function FoodThrowing._Init()

end


return FoodThrowing
