local ReplicatedStorage = game:GetService('ReplicatedStorage')

local Packets = require(ReplicatedStorage.Data.Packets)

local FireExtinguisher = {
	Key = Enum.KeyCode.Space,

	GamepadKey = Enum.KeyCode.ButtonY
}


function FireExtinguisher.KeyDown()
	Packets.Interaction.FireExtinguisher.send(true)
end


function FireExtinguisher.KeyUp()
	Packets.Interaction.FireExtinguisher.send(false)
end


return FireExtinguisher
