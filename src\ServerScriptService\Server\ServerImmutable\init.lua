local ReplicatedStorage = game:GetService('ReplicatedStorage')

local RET = {
	Data = {
		DATASTORE = 'Viken._AnimeC_Data.*#$89&.^$t7J',
		DATASTORE_HASHED = '',
		DATA_RELEASE_TIME_AFTER_LEAVING = .05,

		TEMPLATE = require(script.ProfileTemplate)
	},
	
	Letters = {
		CONSONANTS_CLUSTER = {
			'b', 'bl', 'c', 'cl', 'd', 'f', 'fr', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p',
			'r', 's', 'sh', 't', 'tr', 'v', 'w', 'x', 'y', 'z'
		},
		
		VOWELS_CLUSTER = {
			'a', 'ai', 'e', 'i', 'o', 'oa', 'oo', 'ou', 'u'
		}
	},
	
	Topics = {
		SERVER_INDEXING = 'SERVER_INDEXING'
	},
	
	MemoryStores = {
		
	},
	
	Matchmaking = {
		MAX_LOAD_TIME = 10
	},
	
	THROW_FORCE = 1.5,
	CUTTING_SPEED = 2.8,
	
	COOK_TIME = 5,
	STOVE_BURN_TIME = 11,
	FIRE_SPREAD_TIME = 11,
	BURN_AGAIN_AFTER_X_SEC = 6, -- fire spreading
	
	FALL_HEIGHT_MIN = -200,
	
	Codes = {
		['test'] = {
			Expired = false,
			Rewards = {
				{
					Service = 'CurrencyService',
					Function = 'AddCoins',
					Args = {10, 'Codes'}
				}
			}
		},
		
		['gems'] = {
			Expired = false,
			Rewards = {
				{
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {2_000, 'Codes'}
				}
			}
		}
	}
}

RET.Data.DATASTORE_HASHED = require(ReplicatedStorage.SharedModules.HashLib).sha1(RET.Data.DATASTORE):sub(12)

print(
	string.format(
		'Server running on datastore `%s` with hash `%s`',
		RET.Data.DATASTORE,
		RET.Data.DATASTORE_HASHED
	)
)

return table.freeze(RET)