local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local TweenService = game:GetService('TweenService')
local Debris = game:GetService('Debris')
local SoundService = game:GetService('SoundService')
local CollectionService = game:GetService('CollectionService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local CookingService: typeof(require(ServerScriptService.Server.GameServices.CookingService)) = Server.GetService('CookingService')

local SpatialDistortion = {
	COOLDOWN = 60,
	EFFECT_TIME = 5,
	
	Cooldowns = {} :: { [Player]: number },
	AbilityJanitors = {} :: { [Player]: Janitor.Janitor }
}


function SpatialDistortion.GetCooldownTimeLeft(plr: Player): number
	if SpatialDistortion.Cooldowns[plr] then
		return SpatialDistortion.COOLDOWN - (os.clock() - SpatialDistortion.Cooldowns[plr])
	end
	return 0
end


function SpatialDistortion.CanUse(plr: Player): boolean
	if SpatialDistortion.Cooldowns[plr] then
		return os.clock() - SpatialDistortion.Cooldowns[plr] > SpatialDistortion.COOLDOWN
	end
	
	return true
end


function SpatialDistortion.IsInUse(plr: Player): boolean
	if SpatialDistortion.AbilityJanitors[plr] and SpatialDistortion.AbilityJanitors[plr]:Get('Active') then
		return true
	end

	return false
end


function SpatialDistortion.UseAbility(plr: Player): boolean
	if not SpatialDistortion.CanUse(plr) or SpatialDistortion.IsInUse(plr) then return false end

	SpatialDistortion.Cooldowns[plr] = os.clock()

	task.delay(SpatialDistortion.COOLDOWN, function()
		SpatialDistortion.Cooldowns[plr] = nil
	end)

	if not SpatialDistortion.AbilityJanitors[plr] then
		SpatialDistortion.AbilityJanitors[plr] = Janitor.new()
	end
	
	SpatialDistortion.AbilityJanitors[plr]:Add(math.random, nil, 'Active')
	
	ClassExtension.Player.DisableMovement(plr, true)
	plr:SetAttribute(script.Name, true)
	
	local SpatialDistortionTrack: AnimationTrack = Animations:PlayTrack(plr, 'SpatialDistortion')

	SoundService.SFX.Abilities.SpatialDistortion:Play()

	do
		local CharFX = {} :: { ParticleEmitter }
		for _, bodyPart in script.CharFX:GetChildren() do
			for _, v in bodyPart:GetChildren() do
				local ThisFX = v:Clone()
				ThisFX.Parent = plr.Character[bodyPart.Name]
				table.insert(CharFX, ThisFX)
			end
		end

		SpatialDistortion.AbilityJanitors[plr]:Add(function()
			for _, v in CharFX do
				VFXFunctions.DisableDescendants(v)
				Debris:AddItem(v, VFXFunctions.GetHighestWaitTime(v))
			end
		end)
	end
	
	SpatialDistortion.AbilityJanitors[plr]:Add(task.delay(SpatialDistortion.EFFECT_TIME, function()
		SpatialDistortion.AbilityJanitors[plr]:Cleanup()
	end))
	
	SpatialDistortion.AbilityJanitors[plr]:Add(SpatialDistortionTrack.Ended:Connect(function()
		ClassExtension.Player.DisableMovement(plr, false)
		SpatialDistortion.AbilityJanitors[plr]:Remove('Active')
	end))

	SpatialDistortion.AbilityJanitors[plr]:Add(function()
		plr:SetAttribute(script.Name, nil)
		Animations:StopTrack(plr, 'SpatialDistortion')
		ClassExtension.Player.DisableMovement(plr, false)

		Server.GetService('AbilityService').UpdatePassives(plr)
	end)
	
	return true
end


function SpatialDistortion.CancelAbility(plr: Player)
	if not SpatialDistortion.IsInUse(plr) then return end

	if SpatialDistortion.AbilityJanitors[plr] then
		SpatialDistortion.AbilityJanitors[plr]:Cleanup()
	end
end


return SpatialDistortion