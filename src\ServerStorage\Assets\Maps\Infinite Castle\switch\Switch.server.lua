--[=[
	Author: <PERSON><PERSON><PERSON> (Syveric)
	Infinity castle room switch
]=]

if not script:IsDescendantOf(workspace) then return end

local TableUtil = require(game:GetService'ReplicatedStorage'.SharedModules.TableUtil)
local TweenService = game:GetService('TweenService')
local ServerScriptService = game:GetService('ServerScriptService')

local Server = require(ServerScriptService.Server)

local Switchables = {}
local OriginPositions = {
	script.Parent['1']:GetPivot(),
	script.Parent['2']:GetPivot(),
	script.Parent['3']:GetPivot(),
	script.Parent['4']:GetPivot()
}

local outCf = script.Parent.Model:GetPivot()

for _, v in script.Parent:GetChildren() do
	if v:IsA'Model' then
		table.insert(Switchables, v)
	end
end


local ColorCorrection = game:GetService'Lighting':FindFirstChildOfClass('ColorCorrectionEffect')
if not ColorCorrection then
	ColorCorrection = Instance.new('ColorCorrectionEffect')
	ColorCorrection.Parent = game:GetService'Lighting'
end

local originalTint = ColorCorrection.TintColor


local SwapSound = game:GetService'SoundService'.SFX.RoomSwap
local function swap()
	Switchables = TableUtil.Shuffle(Switchables)
	
	SwapSound:Play()
	
	local t = TweenService:Create(ColorCorrection, TweenInfo.new(.3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
		{ TintColor = Color3.fromRGB(15, 15, 15) }
	);t:Play();
	t.Completed:Wait()
	task.wait(.2)
	TweenService:Create(ColorCorrection, TweenInfo.new(.2), {TintColor = originalTint}):Play()
	
	for idx, v in Switchables do
		local final = idx <= 4 and OriginPositions[idx] or outCf
		v:PivotTo(final)
	end
end

task.wait(3)

while script:IsDescendantOf(workspace) do
	local AbilityService = Server.GetService('AbilityService')
	
	local t = 30
	if AbilityService and AbilityService.GetPassiveValue then
		t = AbilityService.GetPassiveValue(nil, 'InfiniteCastleSwitchTime')
	end
	
	task.wait(t)
	
	swap()
end
