local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Debris = game:GetService('Debris')
local SoundService = game:GetService('SoundService')
local TweenService = game:GetService('TweenService')

local Server = require(ServerScriptService.Server)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)

local ShadowExchange = {
	COOLDOWN = 5,
	
	Cooldowns = {} :: { [Player]: number },
	AbilityJanitors = {} :: { [Player]: Janitor.Janitor },
	
	SavedTeleportPoints = {} :: { [Player]: CFrame }
}


function ShadowExchange.GetCooldownTimeLeft(plr: Player): number
	if ShadowExchange.Cooldowns[plr] then
		return ShadowExchange.COOLDOWN - (os.clock() - ShadowExchange.Cooldowns[plr])
	end
	return 0
end


function ShadowExchange.CanUse(plr: Player): boolean
	if ShadowExchange.Cooldowns[plr] then
		return os.clock() - ShadowExchange.Cooldowns[plr] > ShadowExchange.COOLDOWN
	end
	
	return true
end


function ShadowExchange.IsInUse(plr: Player): boolean
	return false
end


function ShadowExchange.UseAbility(plr: Player): boolean
	if not ShadowExchange.CanUse(plr) or ShadowExchange.IsInUse(plr) then return false end

	if not ShadowExchange.AbilityJanitors[plr] then
		ShadowExchange.AbilityJanitors[plr] = Janitor.new()
	end
	
	local rayParams do
		rayParams = RaycastParams.new()
		rayParams.FilterType = Enum.RaycastFilterType.Exclude
		rayParams.FilterDescendantsInstances = ClassExtension.Players.GetCharacters()
	end
	
	local res = workspace:Raycast(plr.Character.PrimaryPart.Position, Vector3.new(0, -100, 0), rayParams)
	
	if ShadowExchange.AbilityJanitors[plr]:Get('Marker') then
		ShadowExchange.Cooldowns[plr] = os.clock()

		task.delay(ShadowExchange.COOLDOWN, function()
			ShadowExchange.Cooldowns[plr] = nil
		end)
		
		ShadowExchange.AbilityJanitors[plr]:Remove('Marker')
		
		ClassExtension.Player.DisableMovement(plr, true)
		
		local ShadowExchangeTrack: AnimationTrack = Animations:PlayTrack(plr, 'ShadowExchange', .1)
		
		SoundService.SFX.Abilities.ShadowExchangeEnd:Play()
		
		local ShadowEffect = script.ShadowEffect:Clone()
		ShadowEffect.Parent = workspace
		
		if res then
			ShadowEffect.Position = res.Position + Vector3.new(0, ShadowEffect.Size.Y / 2, 0)
		else
			ShadowEffect.Position = plr.Character.PrimaryPart.Position
		end
		
		do
			local CharFX = {} :: { ParticleEmitter }
			for _, bodyPart in script.CharFX:GetChildren() do
				for _, v in bodyPart:GetChildren() do
					local ThisFX = v:Clone()
					ThisFX.Parent = plr.Character[bodyPart.Name]
					table.insert(CharFX, ThisFX)
				end
			end
			
			ShadowExchange.AbilityJanitors[plr]:Add(function()
				for _, v in CharFX do
					VFXFunctions.DisableDescendants(v)
					Debris:AddItem(v, VFXFunctions.GetHighestWaitTime(v))
				end
			end)
		end
		
		ShadowExchange.AbilityJanitors[plr]:Add(task.delay(.3, function()
			plr.Character.PrimaryPart.CFrame = ShadowExchange.SavedTeleportPoints[plr]
			
			VFXFunctions.DisableDescendants(ShadowEffect)
			Debris:AddItem(ShadowEffect, VFXFunctions.GetHighestWaitTime(ShadowEffect))
			
			task.wait(.5)
			
			ShadowExchange.AbilityJanitors[plr]:Cleanup()
		end))
		
		ShadowExchange.AbilityJanitors[plr]:Add(function()
			if ShadowEffect:IsDescendantOf(workspace) then
				VFXFunctions.DisableDescendants(ShadowEffect)
				Debris:AddItem(ShadowEffect, VFXFunctions.GetHighestWaitTime(ShadowEffect))
			end
			
			ClassExtension.Player.DisableMovement(plr, false)
			Animations:StopTrack(plr, 'ShadowExchange')
		end)
	else
		ShadowExchange.SavedTeleportPoints[plr] = plr.Character.PrimaryPart.CFrame
		
		local ShadowClone = script.ShadowClone:Clone()
		ShadowClone.Parent = workspace
		
		VFXFunctions.EnableDescendants(ShadowClone)
		ShadowClone:PivotTo(plr.Character.PrimaryPart.CFrame)
		
		SoundService.SFX.Abilities.ShadowExchangeStart:Play()
		
		ShadowExchange.AbilityJanitors[plr]:Add(function()
			for _, v in ShadowClone:GetDescendants() do
				if v:IsA'BasePart' then
					TweenService:Create(v, TweenInfo.new(.5), {Transparency = 1}):Play()
				end
			end
			
			TweenService:Create(ShadowClone.Highlight, TweenInfo.new(.4), {FillTransparency = 1}):Play()
			
			TweenService:Create(ShadowClone.HumanoidRootPart.Bottom.t, TweenInfo.new(1), {TimeScale = .2}):Play()
			task.wait(.5)
			VFXFunctions.DisableDescendants(ShadowClone)
			Debris:AddItem(ShadowClone, VFXFunctions.GetHighestWaitTime(ShadowClone))
		end, nil, 'Marker')
	end

	return true
end


function ShadowExchange.CancelAbility(plr: Player)
	if not ShadowExchange.IsInUse(plr) then return end

	if ShadowExchange.AbilityJanitors[plr] then
		ShadowExchange.AbilityJanitors[plr]:Cleanup()
	end
end


return ShadowExchange