--[[
	
	Mobile.TouchStarted(position)
	Mobile.TouchEnded(position)
	Mobile.TouchMoved(position, delta)
	Mobile.TouchTapInWorld(position)
	Mobile.TouchPinch(scale, state)
	
--]]



local Mobile = {}

local RAY = Ray.new
local workspace = workspace

local userInput = game:GetService('UserInputService')
local cam = workspace.CurrentCamera

local signal = require(game:GetService'ReplicatedStorage'.SharedModules.Signal)


function Mobile.GetRay(position)
	local viewportMouseRay = cam:ViewportPointToRay(position.X, position.Y)
	return RAY(viewportMouseRay.Origin, viewportMouseRay.Direction * 999)
end


function Mobile.Cast(position, ignoreDescendantsInstance, terrainCellsAreCubes, ignoreWater)
	return workspace:FindPartOnRay(Mobile.GetRay(position), ignoreDescendantsInstance, terrainCellsAreCubes, ignoreWater)
end


function Mobile.CastWithIgnoreList(position, ignoreDescendantsTable, terrainCellsAreCubes, ignoreWater)
	return workspace:FindPartOnRayWithIgnoreList(Mobile.GetRay(position), ignoreDescendantsTable, terrainCellsAreCubes, ignoreWater)
end


function Mobile.CastWithWhitelist(position, whitelistDescendantsTable, ignoreWater)
	return workspace:FindPartOnRayWithWhitelist(Mobile.GetRay(position), whitelistDescendantsTable, ignoreWater)
end


function Mobile.Start()

end


function Mobile.Init()
	Mobile.TouchStarted    = signal.new()
	Mobile.TouchEnded      = signal.new()
	Mobile.TouchMoved      = signal.new()
	Mobile.TouchTapInWorld = signal.new()
	Mobile.TouchPinch      = signal.new()

	userInput.TouchStarted:Connect(function(input, processed)
		if processed then return end
		Mobile.TouchStarted:Fire(input.Position)
	end)

	userInput.TouchEnded:Connect(function(input, processed)
		Mobile.TouchEnded:Fire(input.Position)
	end)

	userInput.TouchMoved:Connect(function(input, processed)
		if processed then return end
		Mobile.TouchMoved:Fire(input.Position, input.Delta)
	end)

	userInput.TouchTapInWorld:Connect(function(position, processed)
		if processed then return end
		Mobile.TouchTapInWorld:Fire(position)
	end)

	userInput.TouchPinch:Connect(function(touchPositions, scale, velocity, state, processed)
		if processed then return end
		Mobile.TouchPinch:Fire(scale, state)
	end)
end


return Mobile