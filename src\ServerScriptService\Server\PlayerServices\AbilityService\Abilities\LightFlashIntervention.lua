local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local TweenService = game:GetService('TweenService')
local Debris = game:GetService('Debris')
local CollectionService = game:GetService('CollectionService')
local SoundService = game:GetService('SoundService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local Table = require(ReplicatedStorage.SharedModules.TableUtil)
local CookingService: typeof(require(ServerScriptService.Server.GameServices.CookingService)) = Server.GetService('CookingService')

local LightFlashIntervention = {
	COOLDOWN = 5,
	
	Cooldowns = {} :: { [Player]: number },
	AbilityJanitors = {} :: { [Player]: Janitor.Janitor }
}


function LightFlashIntervention.GetCooldownTimeLeft(plr: Player): number
	if LightFlashIntervention.Cooldowns[plr] then
		return LightFlashIntervention.COOLDOWN - (os.clock() - LightFlashIntervention.Cooldowns[plr])
	end
	return 0
end


function LightFlashIntervention.CanUse(plr: Player): boolean
	if LightFlashIntervention.Cooldowns[plr] then
		return os.clock() - LightFlashIntervention.Cooldowns[plr] > LightFlashIntervention.COOLDOWN
	end
	
	return true
end


function LightFlashIntervention.IsInUse(plr: Player): boolean
	if LightFlashIntervention.AbilityJanitors[plr] and LightFlashIntervention.AbilityJanitors[plr]:Get('Active') then
		return true
	end

	return false
end


function LightFlashIntervention.UseAbility(plr: Player): boolean
	if not LightFlashIntervention.CanUse(plr) or LightFlashIntervention.IsInUse(plr) then return false end

	LightFlashIntervention.Cooldowns[plr] = os.clock()

	task.delay(LightFlashIntervention.COOLDOWN, function()
		LightFlashIntervention.Cooldowns[plr] = nil
	end)

	if not LightFlashIntervention.AbilityJanitors[plr] then
		LightFlashIntervention.AbilityJanitors[plr] = Janitor.new()
	end
	
	LightFlashIntervention.AbilityJanitors[plr]:Add(math.random, nil, 'Active')
	
	ClassExtension.Player.DisableMovement(plr, true)
	
	local FlashTrack: AnimationTrack = Animations:PlayTrack(plr, 'LightFlashIntervention')
	FlashTrack:AdjustSpeed(3.4)
	
	do
		local CharFX = {} :: { ParticleEmitter }
		for _, bodyPart in script.CharFX:GetChildren() do
			for _, v in bodyPart:GetChildren() do
				local ThisFX = v:Clone()
				ThisFX.Parent = plr.Character[bodyPart.Name]
				table.insert(CharFX, ThisFX)
			end
		end

		LightFlashIntervention.AbilityJanitors[plr]:Add(function()
			for _, v in CharFX do
				VFXFunctions.DisableDescendants(v)
				Debris:AddItem(v, VFXFunctions.GetHighestWaitTime(v))
			end
		end)
	end
	
	LightFlashIntervention.AbilityJanitors[plr]:Add(task.delay(.4, function()
		for _, tag in Table.Extend(table.clone(Client.Immutable.BURNABLE_INTERACTABLES), {Client.Immutable.Tags.STOVE}) do
			for _, interactable in CollectionService:GetTagged(tag) do
				if CookingService.IsInteractableOnFire(interactable) then
					local offset = CFrame.new(0, 0, -(interactable.Size.Z/2 + plr.Character.PrimaryPart.Size.Z/2)) * CFrame.Angles(0, math.pi, 0)
					local appliedOffset = interactable.CFrame * offset
					plr.Character.PrimaryPart.CFrame = CFrame.new(appliedOffset.X, plr.Character.PrimaryPart.CFrame.Y, appliedOffset.Z)
						* CFrame.fromMatrix(Vector3.new(), appliedOffset.RightVector, appliedOffset.UpVector, appliedOffset.LookVector)
					SoundService.SFX.Abilities.LightFlashIntervention:Play()
					break
				end
			end
		end
	end))
	
	LightFlashIntervention.AbilityJanitors[plr]:Add(FlashTrack.Ended:Connect(function()
		LightFlashIntervention.AbilityJanitors[plr]:Cleanup()
	end))
	
	LightFlashIntervention.AbilityJanitors[plr]:Add(function()
		Animations:StopTrack(plr, 'LightFlashIntervention')
		ClassExtension.Player.DisableMovement(plr, false)
		
		Server.GetService('AbilityService').UpdatePassives(plr)
	end)
	
	return true
end


function LightFlashIntervention.CancelAbility(plr: Player)
	if not LightFlashIntervention.IsInUse(plr) then return end

	if LightFlashIntervention.AbilityJanitors[plr] then
		LightFlashIntervention.AbilityJanitors[plr]:Cleanup()
	end
end


return LightFlashIntervention