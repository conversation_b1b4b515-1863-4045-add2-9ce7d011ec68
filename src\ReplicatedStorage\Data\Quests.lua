--[=[
	Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>)
	Quests data
]=]

export type Quest = {
	Name: string,
	Description: string,
	
	ID: string, -- for internal processing
	
	Requirement: number?,
	Rewards: {{
		Service: string,
		Function: string,
		Args: {string | number},

		Display: string | Model,
		Amount: number?
	}}?
}

local RET = {
	Daily = {
		{
			Name = 'Play 1 Round',
			Description = '-',
			
			ID = 'PlayRound',
			
			Requirement = 1,
			Rewards = {
				{
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {10, 'Quest'},

					Display = 'rbxassetid://122220601991100',
					Amount = 10
				}
			}
		},
		
		{
			Name = 'Finish 5 Orders',
			Description = '-',

			ID = 'FinishOrder',

			Requirement = 5,
			Rewards = {
				{
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {10, 'Quest'},

					Display = 'rbxassetid://122220601991100',
					Amount = 10
				}
			}
		},
		
		{
			Name = 'Summon 1',
			Description = '-',

			ID = 'Summon',

			Requirement = 1,
			Rewards = {
				{
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {10, 'Quest'},

					Display = 'rbxassetid://122220601991100',
					Amount = 10
				}
			}
		},
	},
	
	Weekly = {
		{
			Name = 'Finish 10 Orders',
			Description = '-',

			ID = 'FinishOrder',

			Requirement = 10,
			Rewards = {
				{
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {10, 'Quest'},

					Display = 'rbxassetid://122220601991100',
					Amount = 10
				}
			}
		},
		
		{
			Name = 'Play 5 Rounds',
			Description = '-',

			ID = 'PlayRound',

			Requirement = 5,
			Rewards = {
				{
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {10, 'Quest'},

					Display = 'rbxassetid://122220601991100',
					Amount = 10
				}
			}
		},
		
		{
			Name = 'Summon 5',
			Description = '-',

			ID = 'Summon',

			Requirement = 5,
			Rewards = {
				{
					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {10, 'Quest'},

					Display = 'rbxassetid://122220601991100',
					Amount = 10
				}
			}
		},
	}
}


function RET.FindQuestFromCategoryAndID(category: string, id: string)
	for _, v in RET[category] do
		if v.ID == id then
			return v
		end
	end
end


return table.freeze(RET)