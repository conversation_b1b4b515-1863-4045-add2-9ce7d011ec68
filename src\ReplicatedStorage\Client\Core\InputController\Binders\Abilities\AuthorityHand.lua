local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local CollectionService = game:GetService('CollectionService')
local GamepadService = game:GetService('GamepadService')
local UserInputService = game:GetService('UserInputService')

local Client = require(ReplicatedStorage.Client)
local Janitor = require(ReplicatedStorage.SharedModules.Janitor)

local Packets = require(ReplicatedStorage.Data.Packets)
local InputController: typeof(require(script.Parent.Parent.Parent)) = Client.GetController('InputController')

local AuthorityHand = {
	Key = Enum.KeyCode.V,
	
	GamepadKey = Enum.KeyCode.ButtonR2
}


function AuthorityHand.KeyDown(second: true?)
	if InputController.Preferred == 'Gamepad' and second == nil then
		AuthorityHand.Click()
		return
	end
	
	AuthorityHand.Cleanup()
	
	if AuthorityHand.IsOnCooldown() or not AuthorityHand.CanUseAbilityAsCharacter() then return end
	
	local whitelist = {}
	for _, tag in Client.Immutable.VALID_INTERACTABLES do
		for _, v in CollectionService:GetTagged(tag) do
			table.insert(whitelist, v)
		end
	end
	
	local interactableRp do
		interactableRp = RaycastParams.new()
		interactableRp.FilterType = Enum.RaycastFilterType.Include
		interactableRp.FilterDescendantsInstances = whitelist
	end
	
	local hitInteractable = InputController.Inputs.Mouse.Hit(interactableRp)

	if hitInteractable then
		local hitRp do
			hitRp = RaycastParams.new()
			hitRp.FilterType = Enum.RaycastFilterType.Exclude
			hitRp.FilterDescendantsInstances = {workspace.Map.Collision_Parts}
		end
		
		local hitRes = InputController.Inputs.Mouse.Hit(hitRp)
		
		Packets.Ability.UseAbility.send({
			AbilityName = script.Name,
			Args = {hitInteractable.Instance, hitRes and hitRes.Position}
		})
	end
end


function AuthorityHand.KeyUp()
	
end


local ClickJanitor = Janitor.new()
function AuthorityHand.Click()
	if ClickJanitor:Get('Click') then
		return AuthorityHand.Cleanup()
	end
	
	if AuthorityHand.IsOnCooldown() or not AuthorityHand.CanUseAbilityAsCharacter() then return end
	
	ClickJanitor:Add(Players.LocalPlayer:GetMouse().Button1Down:Connect(function()
		task.wait()
		AuthorityHand.KeyDown()
		AuthorityHand.Cleanup()
	end), nil, 'Click')
	
	GamepadService:EnableGamepadCursor(nil)
	ClickJanitor:Add(UserInputService.InputBegan:Connect(function(input: InputObject, processed: boolean)
		if input.KeyCode == Enum.KeyCode.ButtonA then
			AuthorityHand.KeyDown(true)
			AuthorityHand.Cleanup()
		elseif input.KeyCode == Enum.KeyCode.ButtonB then
			AuthorityHand.Cleanup()
		end
	end))

	ClickJanitor:Add(function()
		GamepadService:DisableGamepadCursor()
	end)
end


function AuthorityHand.Cleanup()
	ClickJanitor:Cleanup()
end


function AuthorityHand.IsOnCooldown()
	return Client.GetController'AbilityController'.IsAbilityOnCooldown(script.Name)
end


function AuthorityHand.CanUseAbilityAsCharacter()
	return Client.GetController'AbilityController'.CanUseAbilityAsCharacter(script.Name)
end


function AuthorityHand._Init()

end


return AuthorityHand
