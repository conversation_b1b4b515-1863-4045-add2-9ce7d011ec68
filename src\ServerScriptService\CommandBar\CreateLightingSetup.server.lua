--[=[
	Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>ver<PERSON>)
	Create a setup for lighting to insert into a map folder
]=]

local LightingSetup = Instance.new('Folder')
LightingSetup.Name = 'Lighting'

local Properties = Instance.new('Folder')
Properties.Name = 'Properties'
Properties.Parent = LightingSetup

local Instances = Instance.new('Folder')
Instances.Name = 'Instances'
Instances.Parent = LightingSetup


local TypeValueMap = {
	boolean = 'BoolValue',
	string = 'StringValue',
	number = 'NumberValue',
	Color3 = 'Color3Value'
}

for _, v in {
	'Ambient', 'Brightness', 'ClockTime', 'ColorShift_Bottom', 'ColorShift_Top', 'EnvironmentDiffuseScale',
	'EnvironmentSpecularScale', 'ExposureCompensation', 'FogColor', 'FogEnd', 'FogStart', 'GeographicLatitude',
	'GlobalShadows', 'OutdoorAmbient', 'ShadowSoftness', 'TimeOfDay'
	} do
	local inst = Instance.new(TypeValueMap[typeof(game.Lighting[v])])
	inst.Value = game.Lighting[v]
	inst.Name = v
	inst.Parent = Properties
end

for _, v in game.Lighting:GetChildren() do
	v:Clone().Parent = Instances
end

LightingSetup.Parent = game.ReplicatedStorage


