local ReplicatedStorage = game:GetService('ReplicatedStorage')
local CollectionService = game:GetService('CollectionService')
local RunService = game:GetService('RunService')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)

local CharacterController = {}


local function followTaggedMovingSurface()
	local lastCfWhenOn: CFrame?
	local lastObjOn: BasePart?
	
	RunService.Heartbeat:Connect(function(dt: number)
		if not Players.LocalPlayer.Character or not Players.LocalPlayer.Character.PrimaryPart then return end
		
		local RootPart = Players.LocalPlayer.Character.PrimaryPart
		
		local rayParams do
			rayParams = RaycastParams.new()
			rayParams.FilterType = Enum.RaycastFilterType.Include
			rayParams.FilterDescendantsInstances = { CollectionService:GetTagged(Client.Immutable.Tags.MOVING_SURFACE) }
		end
		
		local res = workspace:Raycast(RootPart.Position, Vector3.new(0, -9, 0), rayParams)
		if res and (lastObjOn == nil or res.Instance == lastObjOn) then
			local instCf = res.Instance:GetPivot()
			
			lastObjOn = res.Instance
			
			if not lastCfWhenOn then
				lastCfWhenOn = instCf
			end
			
			local rel = instCf * lastCfWhenOn:Inverse()
			RootPart.CFrame = rel * RootPart.CFrame
			
			lastCfWhenOn = instCf
		else
			if lastObjOn and lastCfWhenOn then
				local rel = lastObjOn.CFrame * lastCfWhenOn:Inverse()
				RootPart.CFrame = rel * RootPart.CFrame
			end
			
			lastObjOn = nil
			lastCfWhenOn = nil
		end
	end)
end


function CharacterController.GetEquippedCharacter(waitForCharacter: true?): string?
	if not Players.LocalPlayer:GetAttribute'Character' then
		Players.LocalPlayer:GetAttributeChangedSignal'Character':Wait()
	end
	
	return Players.LocalPlayer:GetAttribute'Character'
end


function CharacterController._Start()
	task.defer(followTaggedMovingSurface)
end


return CharacterController