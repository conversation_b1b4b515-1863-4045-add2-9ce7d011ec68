local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local SoundService = game:GetService('SoundService')
local TweenService = game:GetService('TweenService')
local Debris = game:GetService('Debris')

local Server = require(ServerScriptService.Server)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local CookingService: typeof(require(ServerScriptService.Server.GameServices.CookingService)) = Server.GetService('CookingService')

local AnimProps = require(script.AnimProps)

local MOVE_SPEED = 95

local AuthorityHand = {
	COOLDOWN = 5,
	
	Cooldowns = {} :: { [Player]: number },
	AbilityJanitors = {} :: { [Player]: Janitor.Janitor }
}


function AuthorityHand.GetCooldownTimeLeft(plr: Player): number
	if AuthorityHand.Cooldowns[plr] then
		return AuthorityHand.COOLDOWN - (os.clock() - AuthorityHand.Cooldowns[plr])
	end
	return 0
end


function AuthorityHand.CanUse(plr: Player): boolean
	if AuthorityHand.Cooldowns[plr] then
		return os.clock() - AuthorityHand.Cooldowns[plr] > AuthorityHand.COOLDOWN
	end
	
	if CookingService.GetHeldItem(plr) then return false end
	
	return true
end


function AuthorityHand.IsInUse(plr: Player): boolean
	if AuthorityHand.AbilityJanitors[plr] and AuthorityHand.AbilityJanitors[plr]:Get('Active') then
		return true
	end

	return false
end


local OriginalAttributeName = 'InitialHiddenState'
local function toggleTransparency(object: Instance, hide: boolean)
	if object:IsA'BasePart' then
		if hide and not object:GetAttribute(OriginalAttributeName) then
			object:SetAttribute(OriginalAttributeName, object.Transparency)
			object.Transparency = 1
		elseif not hide and object:GetAttribute(OriginalAttributeName) then
			object.Transparency = object:GetAttribute(OriginalAttributeName)
			object:SetAttribute(OriginalAttributeName, nil)
		end
		
		for _, v in object:GetChildren() do
			toggleTransparency(v, hide)
		end
	elseif object:IsA'SurfaceGui' or object:IsA'BillboardGui' then
		if hide and not object:GetAttribute(OriginalAttributeName) then
			object:SetAttribute(OriginalAttributeName, object.Enabled)
			object.Enabled = false
		elseif not hide and object:GetAttribute(OriginalAttributeName) then
			object.Enabled = object:GetAttribute(OriginalAttributeName)
			object:SetAttribute(OriginalAttributeName, nil)
		end
	end
end


function AuthorityHand.UseAbility(plr: Player, hitInteractable: BasePart, hitPosition: Vector3): boolean
	if not AuthorityHand.CanUse(plr) or AuthorityHand.IsInUse(plr) then return false end

	if not AuthorityHand.AbilityJanitors[plr] then
		AuthorityHand.AbilityJanitors[plr] = Janitor.new()
	end
	
	local Item = CookingService.GetItemOnInteractable(hitInteractable)
	if Item then
		if CookingService.IsItemFoodSurface(Item) and not CookingService.IsSubmittableFoodSurface(Item) then
			return false
		end
		
		local itemOrigin = Item.CFrame
		
		local pickedUp = CookingService.PickupItem(plr, Item)
		if pickedUp then
			AuthorityHand.Cooldowns[plr] = os.clock()

			task.delay(AuthorityHand.COOLDOWN, function()
				AuthorityHand.Cooldowns[plr] = nil
			end)
			
			AuthorityHand.AbilityJanitors[plr]:Add(math.random, nil, 'Active')
			
			Animations:StopPlayingTracks(plr)
			
			local AuthHandTrack: AnimationTrack = Animations:PlayTrack(plr, 'RulerAuthority', .1)
			AuthorityHand.AbilityJanitors[plr]:Add(AnimProps(plr, AuthHandTrack), 'Destroy')
			
			ClassExtension.Player.DisableMovement(plr, true)
			
			SoundService.SFX.Abilities.AuthorityHand:Play()
			
			local lookAt = Vector3.new(itemOrigin.Position.X, plr.Character.PrimaryPart.CFrame.Y, itemOrigin.Position.Z)
			plr.Character.PrimaryPart.CFrame = CFrame.lookAt(plr.Character.PrimaryPart.Position, lookAt)
			
			local FinalCf = plr.Character.PrimaryPart.CFrame * CFrame.new(-1, 0, 0)
			
			local Dupe = AuthorityHand.AbilityJanitors[plr]:Add(CookingService.GetHeldItem(plr):Clone())
			Dupe.CFrame = itemOrigin
			Dupe.Parent = workspace
			
			local DupeAura = AuthorityHand.AbilityJanitors[plr]:Add(script.FoodVFX.Attachment:Clone())
			DupeAura.Parent = Dupe
			
			local dist = (itemOrigin.Position - FinalCf.Position).Magnitude
			local t = AuthorityHand.AbilityJanitors[plr]:Add(TweenService:Create(
				Dupe,
				TweenInfo.new(.75, Enum.EasingStyle.Quint, Enum.EasingDirection.In),
				{CFrame = FinalCf}
			));t:Play();
			
			toggleTransparency(Item, true)
			
			AuthorityHand.AbilityJanitors[plr]:Add(t.Completed:Once(function()
				Dupe:Destroy()
				
				if Item:IsDescendantOf(workspace) then
					toggleTransparency(Item, false)
				end
			end))
			
			AuthorityHand.AbilityJanitors[plr]:Add(AuthHandTrack.Ended:Once(function()
				AuthorityHand.AbilityJanitors[plr]:Cleanup()
			end))
			
			AuthorityHand.AbilityJanitors[plr]:Add(function()
				if Item:IsDescendantOf(workspace) then
					toggleTransparency(Item, false)
				end
				
				if plr.Character then
					CookingService.PickupItem(plr, Item, true)
					ClassExtension.Player.DisableMovement(plr, false)
					Animations:StopTrack(plr, 'RulerAuthority')
				end
			end)
		end
	end

	return true
end


function AuthorityHand.CancelAbility(plr: Player)
	if not AuthorityHand.IsInUse(plr) then return end

	if AuthorityHand.AbilityJanitors[plr] then
		AuthorityHand.AbilityJanitors[plr]:Cleanup()
	end
end


return AuthorityHand