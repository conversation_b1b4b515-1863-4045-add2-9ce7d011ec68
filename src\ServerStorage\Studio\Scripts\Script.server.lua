local function calculateFOV2 ()
	local playerPositions = {}

	for _, RootPart:BasePart in pairs(self.ClientHandler.RootParts) do
		table.insert(playerPositions, RootPart.Position)
	end

	if #playerPositions < 1 then return end

	local center = Vector3.zero
	for _, pos in ipairs(playerPositions) do
		center += pos
	end
	center /= #playerPositions

	local minX, maxX = math.huge, -math.huge
	local minZ, maxZ = math.huge, -math.huge

	for _, pos in pairs(playerPositions) do
		minX = math.min(minX, pos.X)
		maxX = math.max(maxX, pos.X)
		minZ = math.min(minZ, pos.Z)
		maxZ = math.max(maxZ, pos.Z)
	end

	local width = (maxX - minX) + 5
	local depth = (maxZ - minZ) + 5
	local spread = math.max(width, depth)
	local distanceFromCamera = (CurrentCamera.CFrame.Position - center).Magnitude
	local requiredFOV = 2 * math.deg(math.atan((spread / 2) / distanceFromCamera))
	local targetFOV = math.clamp(requiredFOV, self.minFOV, self.maxFOV)

	CurrentCamera.FieldOfView += (targetFOV - CurrentCamera.FieldOfView) * 0.01

	self:adjustFOV(CurrentCamera.FieldOfView)
end