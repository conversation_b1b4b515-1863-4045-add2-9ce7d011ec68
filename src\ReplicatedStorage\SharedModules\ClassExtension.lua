--[=[
	Author: <PERSON><PERSON><PERSON> (Syver<PERSON>)
	Extends a Roblox class
]=]

local Players = game:GetService('Players')
local RunService = game:GetService('RunService')
local ContentProvider = game:GetService('ContentProvider')
local ReplicatedStorage = game:GetService('ReplicatedStorage')


local ClassExtension = setmetatable({}, {
	__index = function<T, U>(t: T, k: U): index<T, U>
		if not rawget(t, k) then
			t[k] = {}
		end

		return t[k]
	end
})


do -- Players
	function ClassExtension.Players.PlayerAdded(cb: (Player) -> ()): RBXScriptConnection
		for _, v in Players:GetPlayers() do
			task.spawn(cb, v)
		end

		return Players.PlayerAdded:Connect(cb)
	end

	function ClassExtension.Players.GetCharacters(): {Model}
		local chars = {}

		for _, v in Players:GetPlayers() do
			if v.Character and v.Character.PrimaryPart and v.Character:FindFirstChild'Humanoid' then
				table.insert(chars, v.Character)
			end
		end

		return chars
	end

	local UsernameCache = {}
	function ClassExtension.Players.GetUsernameFromUserId(userId: number, retries: number?): string?
		local user, s

		local tries = 0
		while not s do
			s = pcall(function()
				user = UsernameCache[userId] or Players:GetNameFromUserIdAsync(userId)
			end)

			if s then
				UsernameCache[userId] = user
			end

			return s and user or 'UNKNOWN'
		end
	end
	
	local THUMBNAIL_FORMAT = 'rbxthumb://type=AvatarHeadShot&w=%d&h=%d&id=%d+'
	local THUMB_TYPES = {
		[Enum.ThumbnailType.HeadShot] = 'AvatarHeadShot',
		[Enum.ThumbnailType.AvatarThumbnail] = 'Avatar',
		[Enum.ThumbnailType.AvatarBust] = 'AvatarBust',
	}
	function ClassExtension.Players.GetUserThumbnailAsync(userId: number, thumbType: Enum.ThumbnailType, thumbSize: Enum.ThumbnailSize): (string, boolean?)
		local size = thumbSize.Name:match'%d+'
		
		local thumbnail, isReady = select(2, xpcall(function()
			return Players:GetUserThumbnailAsync(userId, thumbType, thumbSize)
		end, function()
			return THUMBNAIL_FORMAT:format(THUMB_TYPES[thumbType], size, size), nil
		end))
		
		return thumbnail, isReady
	end
end


do -- Player
	function ClassExtension.Player.CharacterAdded(plr: Player, cb: (Model) -> ()): RBXScriptConnection
		if plr.Character then
			task.spawn(cb, plr.Character)
		end

		return plr.CharacterAdded:Connect(cb)
	end

	function ClassExtension.Player.GetTools(plr: Player)
		local ToolInCharacter = plr.Character:FindFirstChildOfClass('Tool')
		local ToolsInBackpack = plr.Backpack:GetChildren()

		if ToolInCharacter then
			table.insert(ToolsInBackpack, ToolInCharacter)
		end

		return ToolsInBackpack
	end
	
	function ClassExtension.Player.DisableMovement(plr: Player, value: boolean)
		local enumVal = value and 'Scriptable' or 'UserChoice'

		plr.DevTouchMovementMode = Enum.DevTouchMovementMode[enumVal]
		plr.DevComputerMovementMode = Enum.DevComputerMovementMode[enumVal]

		if value and plr.Character and plr.Character.PrimaryPart then
			plr.Character.Humanoid:MoveTo(plr.Character.PrimaryPart.Position)
		end
	end
end


do -- workspace
	local CachedHumanoids = {}
	local Connected = false
	function ClassExtension.workspace.GetCharactersWithHumanoid(): {Model}
		if Connected then return CachedHumanoids end

		Connected = true

		local function checkHumanoid(humanoid: Humanoid)
			if humanoid:IsA'Humanoid' then
				table.insert(CachedHumanoids, humanoid.Parent)
			end
		end

		workspace.DescendantAdded:Connect(checkHumanoid)

		for _, v in workspace:GetDescendants() do
			if v:IsA'Humanoid' then
				checkHumanoid(v)
			end
		end

		return CachedHumanoids
	end
end


do -- game
	if RunService:IsServer() then
		workspace:SetAttribute('PrivateServerId', game.PrivateServerId)
	end

	function ClassExtension.game.IsReservedServer()
		return workspace:GetAttribute'PrivateServerId' ~= '' and game.PrivateServerOwnerId == 0
	end


	function ClassExtension.game.IsPublicServer()
		return workspace:GetAttribute'PrivateServerId' == ''
	end


	function ClassExtension.game.IsPrivateServer()
		return workspace:GetAttribute'PrivateServerId' ~= '' and game.PrivateServerOwnerId ~= 0
	end


	function ClassExtension.game.GetServerType()
		if ClassExtension.game.IsReservedServer() then
			return 'Reserved'
		elseif ClassExtension.game.IsPrivateServer() then
			return 'Private'
		end

		return 'Public'
	end
end


do -- Instance
	function ClassExtension.Instance.GetChildrenOfClass(parent: Instance, class: string): {Instance}
		assert(parent:IsA'Instance' and type(class) == 'string')

		local children = {}

		for _, v in parent:GetChildren() do
			if v:IsA(class) then
				table.insert(children, v)
			end
		end

		return children
	end

	function ClassExtension.Instance.FindFirstChildOfClass(parent: Instance, class: string, recursive: boolean?)
		for _, v in parent:GetChildren() do
			if v.ClassName == class then
				return v
			elseif recursive then
				local found = ClassExtension.Instance.FindFirstChildOfClass(v, class, recursive)
				if found then
					return found
				end
			end
		end
	end

	function ClassExtension.Instance.WaitForChildOfClass(parent: Instance, class: string, yield: number?)
		local res

		local yieldTask: thread?
		if yield then
			yieldTask = task.delay(yield, function()
				local i = Instance.new(class)
				i.Parent = parent
				task.wait()
				i:Destroy()
			end)
		end

		repeat
			res = parent:FindFirstChildOfClass(class)

			if not res then
				parent.ChildAdded:Wait()
			end
		until res

		if yieldTask then
			pcall(task.cancel, yieldTask)
			res = nil
		end

		return res
	end
	
	
	function ClassExtension.Instance.Weld(...): () -> ()
		local itemsToWeld = {...}
		
		local createdWelds = {}
		
		local first
		local function weld(item: BasePart)
			if not item:IsA'BasePart' then return end
			
			if not first then
				first = item
				return
			end

			local weld = Instance.new('WeldConstraint')
			weld.Part0 = first
			weld.Part1 = item
			weld.Parent = first

			table.insert(itemsToWeld, weld)
		end
		
		for _, item in itemsToWeld do
			weld(item)
			
			for _, v in item:GetDescendants() do
				weld(v)
			end
		end
		
		return function()
			for _, v in createdWelds do v:Destroy() end
		end
	end
	
	
	function ClassExtension.Instance.DeleteWelds(...)
		for _, item in {...} do
			for _, v in item:GetDescendants() do
				if v:IsA'WeldConstraint' or v:IsA'Weld' or v:IsA'Motor6D' or v:IsA'Motor' then
					print('del', v.Name) v:Destroy()
				end
			end
		end
	end
end


do -- Sound
	function ClassExtension.Sound.IsIdValid(songId: string)
		local valid = false

		local fakeSound = Instance.new('Sound', script)
		fakeSound.SoundId = songId

		ContentProvider:PreloadAsync({fakeSound}, function(_, status: Enum.AssetFetchStatus)
			valid = status == Enum.AssetFetchStatus.Success and fakeSound.TimeLength > 1
		end)

		fakeSound:Destroy()

		return valid
	end
end


do -- BasePart
	function ClassExtension.BasePart.GetAllConnectedParts(basePart: BasePart)
		local Parts = {}
		
		local function GetConnectedParts(Object)
			for _, v in Object:GetConnectedParts() do
				local Ignore = false
				for _, vv in Parts do
					if v == vv then
						Ignore = true
					end
				end
				
				if not Ignore then
					table.insert(Parts, v)
					GetConnectedParts(v)
				end
			end
		end
		
		GetConnectedParts(basePart)
		
		return Parts
	end
end


do -- time
	local TIME_OFFSET_FOR_EST = -60 * 60 * (os.date('*t').isdst and 4 or 5)
	
	function ClassExtension.time.CreateDailyHash(t: number): string
		return os.date('%j%Y', t)
	end

	function ClassExtension.time.CreateWeeklyHash(t: number): string
		return os.date('%W%Y', t)
	end

	function ClassExtension.time.CreateHourlyHash(t: number): string
		return os.date('%H', t) .. ClassExtension.time.CreateDailyHash(t)
	end
	
	
	function ClassExtension.time.CreateMonthlyHash(t: number): string
		return os.date('%m%Y', t)
	end
	
	function ClassExtension.time.GetESTUnix()
		return os.time() + TIME_OFFSET_FOR_EST
	end
end


do -- Humanoid
	local localPlayerDescription: HumanoidDescription?
	local Binded = false
	function ClassExtension.Humanoid.GetHumanoidDescriptionFromUserId(userId: number, retries: number?, default: HumanoidDescription?)
		for i = 1, retries or 3 do
			local success, ret = pcall(function()
				if RunService:IsClient() and userId == Players.LocalPlayer.UserId and localPlayerDescription then
					return localPlayerDescription
				end
				
				return Players:GetHumanoidDescriptionFromUserId(userId)
			end)

			if success then
				if RunService:IsClient() and userId == Players.LocalPlayer.UserId and Binded == false then
					Binded = true
					localPlayerDescription = ret
					
					Players.LocalPlayer.CharacterAppearanceLoaded:Connect(function()
						localPlayerDescription = ClassExtension.Humanoid.GetHumanoidDescriptionFromUserId(Players.LocalPlayer.UserId, nil, localPlayerDescription)
					end)
				end
				
				return ret
			else
				task.wait(3)
			end
		end

		return default or Instance.new('HumanoidDescription')
	end
end


do -- Misc
	local Dummies = {}
	local Binded = false
	local lastDesc = nil
	function ClassExtension.Misc.CreateLocalPlayerDummy(): Model
		local playerDescription = ClassExtension.Humanoid.GetHumanoidDescriptionFromUserId(Players.LocalPlayer.UserId)
		local Dummy = ReplicatedStorage.Assets.Dummy:Clone()
		
		Dummy.Parent = game -- :ApplyDescription() will error if the humanoid is parented to nil. This is to circumvent it
		Dummy.Humanoid:ApplyDescriptionReset(playerDescription)
		
		Dummies[Dummy] = true
		
		if Binded == false then
			Binded = true
			
			Players.LocalPlayer.CharacterAppearanceLoaded:Connect(function()
				local playerDescription = ClassExtension.Humanoid.GetHumanoidDescriptionFromUserId(Players.LocalPlayer.UserId)
				
				for k in Dummies do
					local humanoid = k:FindFirstChildOfClass('Humanoid')
					if not humanoid then continue end
					humanoid:ApplyDescriptionReset(playerDescription)
				end
			end)
		end
		
		return Dummy
	end
end


return table.freeze(ClassExtension)