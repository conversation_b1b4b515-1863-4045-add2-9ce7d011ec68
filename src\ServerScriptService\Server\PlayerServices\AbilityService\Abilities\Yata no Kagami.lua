local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local TweenService = game:GetService('TweenService')
local Debris = game:GetService('Debris')
local SoundService = game:GetService('SoundService')

local Server = require(ServerScriptService.Server)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local Math = require(ReplicatedStorage.SharedModules.Math)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local CookingService = Server.GetService('CookingService')

local BEAM_SPEED = 63

local YatanoKagami = {
	COOLDOWN = 10,

	Cooldowns = {} :: { [Player]: number },
	AbilityJanitors = {} :: { [Player]: Janitor.Janitor }
}


function YatanoKagami.GetCooldownTimeLeft(plr: Player): number
	if YatanoKagami.Cooldowns[plr] then
		return YatanoKagami.COOLDOWN - (os.clock() - YatanoKagami.Cooldowns[plr])
	end
	return 0
end


function YatanoKagami.CanUse(plr: Player): boolean
	if YatanoKagami.Cooldowns[plr] then
		return os.clock() - YatanoKagami.Cooldowns[plr] > YatanoKagami.COOLDOWN
	end

	return true
end


function YatanoKagami.IsInUse(plr: Player): boolean
	if YatanoKagami.AbilityJanitors[plr] and YatanoKagami.AbilityJanitors[plr]:Get('Active') then
		return true
	end

	return false
end


function YatanoKagami.UseAbility(plr: Player, mouseHit: Vector3): boolean
	if not YatanoKagami.CanUse(plr) or YatanoKagami.IsInUse(plr) then return false end

	YatanoKagami.Cooldowns[plr] = os.clock()

	task.delay(YatanoKagami.COOLDOWN, function()
		YatanoKagami.Cooldowns[plr] = nil
	end)

	if YatanoKagami.AbilityJanitors[plr] then
		YatanoKagami.AbilityJanitors[plr]:Destroy()
	end

	YatanoKagami.AbilityJanitors[plr] = Janitor.new()

	YatanoKagami.AbilityJanitors[plr]:Add(math.random, nil, 'Active')

	ClassExtension.Player.DisableMovement(plr, true)

	mouseHit += Vector3.new(0, 3, 0)

	local YatanoKagamiStartTrack: AnimationTrack = Animations:PlayTrack(plr, 'YatanoKagamiStart')
	YatanoKagamiStartTrack:AdjustSpeed(1.35)

	YatanoKagami.AbilityJanitors[plr]:Add(YatanoKagamiStartTrack.Ended:Connect(function()
		SoundService.SFX.Abilities.YatanoKagami:Play()
		
		local Beam = YatanoKagami.AbilityJanitors[plr]:Add(script.Beam:Clone())
		Beam.Parent = workspace

		Beam.CFrame = plr.Character.PrimaryPart.CFrame

		for _, v in plr.Character:GetDescendants() do
			if v:IsA'BasePart' then
				TweenService:Create(v, TweenInfo.new(.2), {Transparency = 1}):Play()
			end
		end

		local dist = (Beam.Position - mouseHit).Magnitude

		local beamCount = math.ceil(math.sqrt(dist))
		local ZigzagPath = Math.GenerateZigZagPath(plr.Character.PrimaryPart.Position, mouseHit, beamCount, NumberRange.new(2, 4.5))

		local TimeLen = dist/BEAM_SPEED
		local timePerBeam = TimeLen/beamCount

		plr.Character.PrimaryPart.Anchored = true

		for _, v in ZigzagPath do
			TweenService:Create(
				plr.Character.PrimaryPart,
				TweenInfo.new(timePerBeam, Enum.EasingStyle.Linear, Enum.EasingDirection.In),
				{Position = v}
			):Play()

			local t = TweenService:Create(
				Beam,
				TweenInfo.new(timePerBeam, Enum.EasingStyle.Linear, Enum.EasingDirection.In),
				{Position = v}
			);t:Play();t.Completed:Wait();
		end

		plr.Character.PrimaryPart.Anchored = true

		Animations:PlayTrack(plr, 'YatanoKagamiEnd', 0)

		YatanoKagami.AbilityJanitors[plr]:Cleanup()
	end))

	YatanoKagami.AbilityJanitors[plr]:Add(function()
		plr.Character.PrimaryPart.Anchored = false

		for _, v in plr.Character:GetDescendants() do
			if v:IsA'BasePart' and v.Name ~= 'HumanoidRootPart' then
				TweenService:Create(v, TweenInfo.new(.4), {Transparency = 0}):Play()
			end
		end

		ClassExtension.Player.DisableMovement(plr, false)
		Animations:StopTrack(plr, 'YatanoKagamiStart')
	end)

	return true
end


function YatanoKagami.CancelAbility(plr: Player)
	if not YatanoKagami.IsInUse(plr) then return end

	if YatanoKagami.AbilityJanitors[plr] then
		YatanoKagami.AbilityJanitors[plr]:Cleanup()
	end
end


return YatanoKagami