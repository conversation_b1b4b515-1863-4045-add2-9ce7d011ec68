local ReplicatedStorage = game:GetService('ReplicatedStorage')
local RunService = game:GetService('RunService')

local CookingService = require(script.Parent)
local Janitor = require(ReplicatedStorage.SharedModules.Janitor)

local FireExtinguisher = {
	ExtinguisherJanitors = {},
	
	EXTINGUISH_TIME = 2
}


function FireExtinguisher.Enable(plr: Player)
	local extinguisher = FireExtinguisher.GetExtinguisher(plr)
	if extinguisher then
		if not FireExtinguisher.ExtinguisherJanitors[extinguisher] then
			FireExtinguisher.ExtinguisherJanitors[extinguisher] = Janitor.new()
		end

		FireExtinguisher.ExtinguisherJanitors[extinguisher]:Add(RunService.Stepped:Connect(function(t: number, dt: number)
			local nearestInteraction: BasePart? = CookingService.NearestInteraction[plr]
			if nearestInteraction then
				local BurnWarning = nearestInteraction:FindFirstChild('BurnWarning', true)
				if BurnWarning then
					BurnWarning:Destroy()
				end
				
				local fire = nearestInteraction:FindFirstChild('Fire', true)
				if fire then
					local ProgressBar: BillboardGui = fire.ProgressBar
					
					ProgressBar.Enabled = true
					
					if not ProgressBar:GetAttribute'ExtinguishTimeLeft' then
						ProgressBar:SetAttribute('ExtinguishTimeLeft', FireExtinguisher.EXTINGUISH_TIME)
					end
					
					ProgressBar:SetAttribute('ExtinguishTimeLeft', ProgressBar:GetAttribute'ExtinguishTimeLeft' - dt)
					
					local progress = ProgressBar:GetAttribute'ExtinguishTimeLeft' / FireExtinguisher.EXTINGUISH_TIME
					ProgressBar.Bar.Progress.UIGradient.Transparency = NumberSequence.new{
						NumberSequenceKeypoint.new(0, 0),
						NumberSequenceKeypoint.new(math.clamp(progress, .001, .998), 0),
						NumberSequenceKeypoint.new(math.clamp(progress + .001, .002, .999), 1),
						NumberSequenceKeypoint.new(1, 1)
					}
					
					if ProgressBar:GetAttribute'ExtinguishTimeLeft' <= 0 then
						CookingService.Extinguish(nearestInteraction, plr)
					end
				end
			end
		end))

		for _, v in extinguisher.Attachment:GetChildren() do
			v.Enabled = true
		end

		FireExtinguisher.ExtinguisherJanitors[extinguisher]:Add(function()
			for _, v in extinguisher.Attachment:GetChildren() do
				v.Enabled = false
			end
		end)
		
		FireExtinguisher.ExtinguisherJanitors[extinguisher]:Add(extinguisher.AncestryChanged:Connect(function(child: Instance, parent: Instance)
			FireExtinguisher.ExtinguisherJanitors[extinguisher]:Cleanup()
		end))
	end
end


function FireExtinguisher.Disable(plr: Player)
	local extinguisher = FireExtinguisher.GetExtinguisher(plr)
	if extinguisher then
		if not FireExtinguisher.ExtinguisherJanitors[extinguisher] then return end
		FireExtinguisher.ExtinguisherJanitors[extinguisher]:Cleanup()
	end
end


function FireExtinguisher.GetExtinguisher(plr: Player): BasePart?
	local extinguisher = CookingService.GetHeldItem(plr)
	if extinguisher and extinguisher.Name == 'FireExtinguisher' then
		return extinguisher
	end
end


return FireExtinguisher