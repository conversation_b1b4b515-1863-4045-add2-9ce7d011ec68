--!native

local Players = game:GetService('Players')
local RunService = game:GetService('RunService')
local ReplicatedStorage = game:GetService('ReplicatedStorage')
local CollectionService = game:GetService('CollectionService')

local Client = require(ReplicatedStorage.Client)
local ClientImmutable = Client.Immutable

local CameraController = {
	MOVEMENT_SMOOTHING = .12,
	FOV_Smoothing = .025,
	X_FOV_Padding = 5,
	Z_FOV_Padding = 5,
	minFOV = 40,
	maxFOV = 51,
	baseAspectRatio = 16/9,

	portraitFOVScaleFactor = 0.5,
	landscapeFOVScaleFactor = 0.3,

	CameraShaker = require(script.CameraShaker).new(Enum.RenderPriority.Camera.Value + 1),
	RegionPart = nil,
	
	MAX_PLANED_X = 0,
	
	MIN_PLANED_Z = 0,
	MAX_PLANED_Z = 0,
}


local CurrentCamera: Camera = workspace.CurrentCamera
local CameraPart: BasePart?


function CameraController.EnableGameplayCamera()
	local function calculateFOV(deltaTime)
		local x_Size_Aspect = (1 / CameraController.RegionPart.Size.X)
		local z_Size_Aspect = (1 - x_Size_Aspect)
		local x_FOV_Ratio = ((CameraController.maxFOV + CameraController.X_FOV_Padding) * x_Size_Aspect)
		local z_FOV_Ratio = ((CameraController.maxFOV + CameraController.Z_FOV_Padding) * z_Size_Aspect)

		---

		local farthestDist = 0
		local savedRoot1Pos, savedRoot2Pos = Vector3.new(), Vector3.new()

		for _, plr in Players:GetPlayers() do
			if not plr.Character or not plr.Character.PrimaryPart then continue end

			local RootPart1 = plr.Character.PrimaryPart
			local RootPart1Pos = RootPart1.Position

			for _, plr2 in Players:GetPlayers() do
				if not plr2.Character or not plr2.Character.PrimaryPart then continue end

				local RootPart2 = plr2.Character.PrimaryPart
				if RootPart1 ~= RootPart2 then
					local RootPart2Pos = RootPart2.Position
					local distance = (RootPart2Pos - RootPart1Pos).Magnitude

					if distance > farthestDist then
						farthestDist = distance
						savedRoot1Pos = RootPart1Pos
						savedRoot2Pos = RootPart2Pos
					end
				end
			end
		end

		local ToRoot1Space = CameraController.RegionPart.CFrame:ToObjectSpace(CFrame.new(savedRoot1Pos)).Position * Vector3.new(1, 1, 0)
		local ToRoot2Space = CameraController.RegionPart.CFrame:ToObjectSpace(CFrame.new(savedRoot2Pos)).Position * Vector3.new(1, 1, 0)

		local xFOV = ((math.abs(ToRoot1Space.Y - ToRoot2Space.Y)/CameraController.RegionPart.Size.Y) * x_FOV_Ratio)
		local zFOV = ((math.abs(ToRoot1Space.X - ToRoot2Space.X)/CameraController.RegionPart.Size.X) * z_FOV_Ratio)

		local baseFOV = math.clamp((xFOV + zFOV), CameraController.minFOV, CameraController.maxFOV)
		local adjustedFOV = CameraController.AdjustFOVForAspectRatio(baseFOV)

		CurrentCamera.FieldOfView += ((adjustedFOV - CurrentCamera.FieldOfView) * 
			(CameraController.FOV_Smoothing + (CameraController.FOV_Smoothing * deltaTime)))
	end

	RunService.RenderStepped:Connect(function(dt: number)
		if not CameraPart or not CameraController.RegionPart or Players.LocalPlayer:GetAttribute('Freecam') then return end

		local CameraShakeCF = CameraController.CameraShaker:Update(dt)
		local Character = Players.LocalPlayer.Character
		local CameraCF

		calculateFOV(dt)

		CameraCF = (CurrentCamera.CFrame:Lerp(CameraPart.CFrame, (CameraController.MOVEMENT_SMOOTHING + (CameraController.MOVEMENT_SMOOTHING * dt))))
		if Character and Character.PrimaryPart then
			local PlanedCF = CameraPart.CFrame:ToObjectSpace(Character.HumanoidRootPart.CFrame)
			local distFromCameraPart = (CurrentCamera.CFrame.Position - CameraPart.Position).Magnitude

			local clampedZPlane = math.clamp(PlanedCF.Z, CameraController.MIN_PLANED_Z, CameraController.MAX_PLANED_Z)
			if math.abs(PlanedCF.X) < CameraController.MAX_PLANED_X or distFromCameraPart > 30 then
				local targetCf = CameraPart.CFrame * CFrame.new(PlanedCF.X, -clampedZPlane, 0)
				CameraCF = CameraPart.CFrame:Lerp(targetCf, .2)
			else
				CameraCF = CurrentCamera.CFrame
			end
		end

		CurrentCamera.CFrame = CurrentCamera.CFrame:Lerp(CameraCF, CameraController.MOVEMENT_SMOOTHING) * CameraShakeCF
	end)
end


function CameraController.AdjustFOVForAspectRatio(baseFOV)
	local viewportSize = CurrentCamera.ViewportSize
	local currentAspectRatio = viewportSize.X / viewportSize.Y
	local aspectRatioFactor = currentAspectRatio / CameraController.baseAspectRatio

	local adjustedFOV = baseFOV
	if currentAspectRatio < CameraController.baseAspectRatio then
		adjustedFOV = baseFOV * (1 + (1 - aspectRatioFactor) * CameraController.portraitFOVScaleFactor)
	elseif currentAspectRatio > CameraController.baseAspectRatio then
		adjustedFOV = baseFOV * (1 + (aspectRatioFactor - 1) * CameraController.landscapeFOVScaleFactor)
	end

	return math.clamp(adjustedFOV, CameraController.minFOV, CameraController.maxFOV)
end


function CameraController._Start()
	CameraController.CameraShaker:Start()
	
	if ClientImmutable.SERVER_TYPE ~= 'Round' then return end
	
	CurrentCamera.FieldOfView = 40
	
	do
		local function updateCameraAndRegionParts()
			CameraPart = workspace:WaitForChild'Map':WaitForChild'CameraPart'
			CameraController.RegionPart = workspace.Map:WaitForChild'RegionPart'
			
			CameraController.MAX_PLANED_X = -math.huge
			CameraController.MAX_PLANED_Z = -math.huge
			CameraController.MIN_PLANED_Z = math.huge
			
			for _, tag in ClientImmutable.VALID_INTERACTABLES do
				for _, v in CollectionService:GetTagged(tag) do
					local PlanedCF = CameraPart.CFrame:ToObjectSpace(v:GetPivot())
					CameraController.MIN_PLANED_Z = math.min(CameraController.MIN_PLANED_Z, PlanedCF.Z)
					CameraController.MAX_PLANED_Z = math.max(CameraController.MAX_PLANED_Z, PlanedCF.Z)
					CameraController.MAX_PLANED_X = math.max(CameraController.MAX_PLANED_X, math.abs(PlanedCF.X))
				end
			end
			
			CameraController.MAX_PLANED_X += math.sqrt(CameraController.MAX_PLANED_X)
			CameraController.MAX_PLANED_Z += math.sqrt(math.abs(CameraController.MAX_PLANED_X)) * math.sign(CameraController.MAX_PLANED_Z)
			CameraController.MIN_PLANED_Z += math.sqrt(math.abs(CameraController.MIN_PLANED_Z)) * math.sign(CameraController.MIN_PLANED_Z)
			
			CameraController.MAX_PLANED_Z = math.max(CameraController.MIN_PLANED_Z, math.min(CameraController.MAX_PLANED_X, 100))
		end
		
		task.defer(updateCameraAndRegionParts)
		workspace.ChildAdded:Connect(function(c)
			if c.Name == 'Map' then updateCameraAndRegionParts() end
		end)
		
		if workspace:FindFirstChild'Map' then
			updateCameraAndRegionParts()
		end
		
		CurrentCamera:GetPropertyChangedSignal'ViewportSize':Connect(updateCameraAndRegionParts)
	end
end


function CameraController._Init()
	if ClientImmutable.SERVER_TYPE ~= 'Round' then return end
	
	CurrentCamera.CameraType = Enum.CameraType.Scriptable
	CurrentCamera:GetPropertyChangedSignal'CameraType':Once(function()
		CurrentCamera.CameraType = Enum.CameraType.Scriptable
	end)
	
	CameraController.EnableGameplayCamera(true)
end


return CameraController