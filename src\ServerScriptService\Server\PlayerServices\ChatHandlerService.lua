local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local TextService = game:GetService('TextService')

local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)

local ChatHandlerService = {}

local ChatCallbacks: { [Player]: {{ Command: string, Callback: (Player, string, string) -> () }} } = {}


local function findPlayerFromPartialString(str)
	if not str then return end

	str = str:lower()
	for _, v in Players:GetPlayers() do
		if v.Name:lower():find('^' .. str) then
			return v
		end
	end
end


function ChatHandlerService.BindChatCommand(plr: Player, command: string, callback: (Player, string, string) -> ())
	if not ChatCallbacks[plr] then
		ChatCallbacks[plr] = {}
	end

	-- If command is already there, then don't add it.
	for _, v in ChatCallbacks[plr] do
		if v.Command == command then return end
	end

	table.insert(ChatCallbacks[plr], {
		Command = command,
		Callback = callback
	})
end


function ChatHandlerService.FilterString(str: string)
	local s, ret = pcall(function()
		return TextService:FilterStringAsync(str, ClassExtension.Instance.WaitForChildOfClass(Players, 'Player').UserId, Enum.TextFilterContext.PublicChat):GetNonChatStringForBroadcastAsync()
	end)

	return ret
end


function ChatHandlerService._PlayerAdded(plr: Player)
	plr.Chatted:Connect(function(msg)
		local arrangedMessage = msg:lower():gsub('^%s*(.-)%s*$', '%1'):gsub('%s+', ' ') -- bad white spices

		if not ChatCallbacks[plr] then return end

		for _, v in ChatCallbacks[plr] do
			if arrangedMessage:find('^' .. v.Command) then
				v.Callback(plr, msg, arrangedMessage)
				break
			end
		end
	end)
	
	ChatHandlerService.BindChatCommand(plr, '/sukuna', function(plr, msg, arrangedMsg)
		plr:SetAttribute('Character', 'Sukuna')
	end)
	
	ChatHandlerService.BindChatCommand(plr, '/sjw', function(plr, msg, arrangedMsg)
		plr:SetAttribute('Character', 'Sung Jinwoo')
	end)
	
	ChatHandlerService.BindChatCommand(plr, '/muzan', function(plr, msg, arrangedMsg)
		plr:SetAttribute('Character', 'Muzan Kibutsuj')
	end)
	
	ChatHandlerService.BindChatCommand(plr, '/gojo', function(plr, msg, arrangedMsg)
		plr:SetAttribute('Character', 'Satoru Gojo')
	end)
	
	ChatHandlerService.BindChatCommand(plr, '/kizaru', function(plr, msg, arrangedMsg)
		plr:SetAttribute('Character', 'Kizaru')
	end)
	
	ChatHandlerService.BindChatCommand(plr, '/rengoku', function(plr, msg, arrangedMsg)
		plr:SetAttribute('Character', 'Rengoku Kyojuro')
	end)
	
	ChatHandlerService.BindChatCommand(plr, '/edward', function(plr, msg, arrangedMsg)
		plr:SetAttribute('Character', 'Edward Elric')
	end)
	
	ChatHandlerService.BindChatCommand(plr, '/toge', function(plr, msg, arrangedMsg)
		plr:SetAttribute('Character', 'Toge Inumaki')
	end)
end


function ChatHandlerService._PlayerRemoving(plr: Player)
	ChatCallbacks[plr] = nil
end


function ChatHandlerService._Start()
	
end


return ChatHandlerService