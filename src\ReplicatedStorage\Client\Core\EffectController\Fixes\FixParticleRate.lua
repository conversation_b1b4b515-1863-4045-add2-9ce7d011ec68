local ret: ((ParticleEmitter) -> ())?


workspace.DescendantAdded:Connect(function(c: Instance)
	if c:IsA'ParticleEmitter' and (not c:GetAttribute'EmitCount' or c:GetAttribute'EmitCount' == 0) then
		ret(c)
	end
end)


ret = function(particle: ParticleEmitter)
	--if true then return end
	
	if not particle:GetAttribute'OriginalRate' then
		local c c = UserSettings().GameSettings:GetPropertyChangedSignal'SavedQualityLevel':Connect(function()
			ret(particle)
			c:Disconnect()
		end)

		particle.AncestryChanged:Connect(function()
			if not particle:IsDescendantOf(game) then
				c:Disconnect()
			end
		end)
	end

	local quality = UserSettings().GameSettings.SavedQualityLevel.Value
	local normalizedQuality = quality / 10
	
	local rate = particle:GetAttribute'OriginalRate' or particle.Rate

	particle:SetAttribute('OriginalRate', rate)

	local finalRate = rate / normalizedQuality
	particle.Rate = finalRate
end


return ret