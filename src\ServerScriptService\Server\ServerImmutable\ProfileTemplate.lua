local ReplicatedStorage = game:GetService("ReplicatedStorage")

local ClientImmutable = require(ReplicatedStorage.Client).Immutable

local AchievementsData = require(ReplicatedStorage.Data.Achievements)


local RET = {
	Analytics = {
		Wins = 0,
		Losses = 0,
		PlatesSubmitted = 0,
		
		MapStats = {} :: { [string]: { Wins: number, Losses: number, MostDelivered: number } }
	},
	
	Skins = {},
	
	Inventory = {},
	
	EquippedCosmetics = {},
	
	TutorialStatus = {},
	RoundsPlayed = 0,
	
	Characters = {},
	EquippedCharacter = nil,
	
	Gems = 0,
	Coins = 0,
	
	Level = 1,
	XP = 0,
	
	SummonMythicalPity = 0,
	SummonLegendaryPity = 0,
	
	CrateMythicalPity = 0,
	CrateLegendaryPity = 0,
	
	LastDailyReset = 0,
	LastWeeklyReset = 0,
	
	DailyStreak = 1,
	DailyRewardsClaimed = {},
	
	Boosts = {},
	
	Tournament = {
		Claimed = false,
		CurrentTournament = '',
		PreviousRankings = {}
	},
	
	Quests = {
		Infinite = {},
		Daily = {},
		Weekly = {},
		Special = {},

		Claimed = {}
	},
	
	Achievements = {
		Claimed = {}
	},
	
	Battlepass = {
		Level = 1,
		XP = 0,
		CurrentBattlepass = '',

		ClaimedFree = table.create(50, false),
		ClaimedPaid = table.create(50, false)
	},
	
	CodesRedeemed = {},
	
	UTCOffset = 0,
	
	OtherData = {
		FirstJoin = 0,
		LastJoin = 0,
		
		RobuxSpent = 0,
		RobuxPurchases = 0,
		
		GameVisits = 0,
		
		TimePlayed = 0,

		PurchasedPasses = {},
		PurchasedProducts = {}
	}
}


for placeName, placeInfo in ClientImmutable.Servers.PLACE_INFO do
	if placeInfo.ServerType == 'Round' then
		RET.TutorialStatus[placeName] = {}
	end
end

do
	for primaryAchievementName, v in AchievementsData do
		if not RET.Achievements.Claimed[primaryAchievementName] then
			RET.Achievements.Claimed[primaryAchievementName] = {}
		end

		if not RET.Achievements[primaryAchievementName] then
			RET.Achievements[primaryAchievementName] = {}
		end

		for secondaryAchievementName in v.Secondary do
			if not RET.Achievements[primaryAchievementName][secondaryAchievementName] then
				RET.Achievements[primaryAchievementName][secondaryAchievementName] = 0
			end

			if not RET.Achievements.Claimed[primaryAchievementName][secondaryAchievementName] then
				RET.Achievements.Claimed[primaryAchievementName][secondaryAchievementName] = false
			end
		end
	end

	for primaryAchievementName, v in RET.Achievements do
		if primaryAchievementName == 'Claimed' then continue end

		if not AchievementsData[primaryAchievementName] then
			RET.Achievements[primaryAchievementName] = nil
			continue
		end

		for secName, value in v do
			if not AchievementsData[primaryAchievementName].Secondary[secName] then
				RET.Achievements[primaryAchievementName][secName] = nil
			end
		end
	end
end


return table.freeze(RET)