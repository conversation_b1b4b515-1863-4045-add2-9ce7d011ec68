{"name": "project", "tree": {"$className": "DataModel", "ReplicatedFirst": {"$className": "ReplicatedFirst", "$ignoreUnknownInstances": true, "$path": "src/ReplicatedFirst"}, "ReplicatedStorage": {"$className": "ReplicatedStorage", "$ignoreUnknownInstances": true, "$path": "src/ReplicatedStorage"}, "ServerScriptService": {"$className": "ServerScriptService", "$ignoreUnknownInstances": true, "$path": "src/ServerScriptService"}, "ServerStorage": {"$className": "ServerStorage", "$ignoreUnknownInstances": true, "$path": "src/ServerStorage"}, "StarterGui": {"$className": "<PERSON><PERSON><PERSON><PERSON>", "$ignoreUnknownInstances": true, "$path": "src/StarterGui"}, "StarterPlayer": {"$className": "StarterPlayer", "StarterPlayerScripts": {"$className": "StarterPlayerScripts", "$ignoreUnknownInstances": true, "$path": "src/StarterPlayer/StarterPlayerScripts"}, "$ignoreUnknownInstances": true}}}