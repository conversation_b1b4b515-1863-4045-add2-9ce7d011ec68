--[=[
	Author: <PERSON><PERSON><PERSON> (Syveric)
	SetCharacterCosmetic
]=]

local ReplicatedStorage = game:GetService('ReplicatedStorage')

local Items = require(ReplicatedStorage.Data.Items)

return function(character: Model, cosmetic: string)
	for _, v in character:GetChildren() do
		if v:IsA'Model' and v.Name == cosmetic then
			v:Destroy()
		end
	end
	
	local cosmeticModel = Instance.new('Model')
	cosmeticModel.Name = cosmetic
	cosmeticModel.Parent = character
	
	for _, v in ReplicatedStorage.Assets.Cosmetics[cosmetic]:GetChildren() do
		local obj = v:Clone()
		obj.CFrame = character[v.OriginLimb.Value].CFrame * v.Offset.Value
		obj.Parent = cosmeticModel
		
		local weld = Instance.new('WeldConstraint')
		weld.Part0 = obj
		weld.Part1 = character[v.OriginLimb.Value]
		weld.Parent = obj
		
		obj.Parent = cosmeticModel
	end
end