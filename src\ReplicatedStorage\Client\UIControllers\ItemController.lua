local ReplicatedStorage = game:GetService('ReplicatedStorage')
local RunService = game:GetService('RunService')
local Players = game:GetService('Players')
local GuiService = game:GetService('GuiService')
local HttpService = game:GetService('HttpService')

local Client = require(ReplicatedStorage.Client)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local CharactersData = require(ReplicatedStorage.Data.Characters)
local Items = require(ReplicatedStorage.Data.Items)
local SetCharacterCosmetic = require(ReplicatedStorage.SharedCoreSystems.SetCharacterCosmetic)

local ItemController = {}

local Mouse = Players.LocalPlayer:GetMouse()


function ItemController.CreateItem(itemData: { Count: number?, AnchorPoint: Vector2?, Position: UDim2?, Size: UDim2?, ItemName: string?, Rarity: string?, Image: string?, Parent: Instance?, HoverItem: GuiObject? })
	local ViewportController = Client.GetController('ViewportController')
	
	local ItemID = HttpService:GenerateGUID(false)
	
	if not itemData.Rarity then
		if Items[itemData.ItemName] and Items[itemData.ItemName].Rarity then
			itemData.Rarity = Items[itemData.ItemName].Rarity
		elseif CharactersData[itemData.ItemName] and CharactersData[itemData.ItemName].Rarity then
			itemData.Rarity = CharactersData[itemData.ItemName].Rarity
		else
			itemData.Rarity = 'Common'
		end
	end
	
	local ItemObject: ImageButton = ReplicatedStorage.Assets.Templates.ItemTemplates[itemData.Rarity]:Clone()
	
	ItemObject:SetAttribute('Rarity', itemData.Rarity)
	
	if itemData.ItemName == 'Gems' then
		itemData.Rarity = ''
	end
	
	ItemObject.Content.ItemName.Text = itemData.ItemName and itemData.ItemName:upper() or ''
	ItemObject.Content.Rarity.Text = itemData.Rarity:upper()
	ItemObject.Name = itemData.ItemName or ItemObject.Name
	ItemObject.Parent = itemData.Parent or script

	if itemData.Position then
		ItemObject.Position = itemData.Position
	end
	
	if itemData.Size then
		ItemObject.Size = itemData.Size
	end
	
	if itemData.AnchorPoint then
		ItemObject.AnchorPoint = itemData.AnchorPoint
	end
	
	if itemData.ItemName == 'Gems' then
		itemData.Image = 'rbxassetid://122220601991100'
	elseif Items[itemData.ItemName] and Items[itemData.ItemName].Image then
		itemData.Image = Items[itemData.ItemName].Image
	end
	
	if itemData.Count then
		ItemObject.Content.Counter.Visible = itemData.Count > 1
		ItemObject.Content.Counter.Text = `{itemData.Count}x`
	end
	
	if itemData.Image then
		ItemObject.Content.Viewport.Visible = false
		ItemObject.Content.Img.Visible = true
		ItemObject.Content.Img.Image = itemData.Image
	elseif CharactersData[itemData.ItemName] then
		task.spawn(ViewportController.SetCharacterHeadshot, ItemObject.Content.Viewport, itemData.ItemName)
	elseif Items[itemData.ItemName].Type == 'Cosmetic' then
		local function updateViewport()
			local rig = ViewportController.SetCharacterHeadshot(ItemObject.Content.Viewport, Players.LocalPlayer:GetAttribute'Character')
			SetCharacterCosmetic(rig, itemData.ItemName)
		end
		
		local c = Players.LocalPlayer:GetAttributeChangedSignal'Character':Connect(updateViewport)
		
		ItemObject.AncestryChanged:Connect(function(child: Instance, parent: Instance)
			if not parent then
				c:Disconnect()
			end
		end)
		
		task.spawn(updateViewport)
	end
	
	if itemData.HoverItem then
		local HoverJanitor = Janitor.new()
		
		local function hovered()
			if itemData.HoverItem:GetAttribute'Disabled' then return end

			local FinalOffset = UDim2.fromOffset(
				itemData.HoverItem.Parent.AbsolutePosition.X,
				itemData.HoverItem.Parent.AbsolutePosition.Y + GuiService.TopbarInset.Height - itemData.HoverItem.AbsoluteSize.Y/2
			)
			HoverJanitor:Add(RunService.RenderStepped:Connect(function()
				local pos = UDim2.fromOffset(Mouse.X, Mouse.Y) - FinalOffset
				itemData.HoverItem.Position = pos
			end))

			HoverJanitor:Add(function()
				itemData.HoverItem.Visible = false
				itemData.HoverItem:SetAttribute('OwnershipID', nil)
			end)

			itemData.HoverItem.Visible = true
			itemData.HoverItem:SetAttribute('OwnershipID', ItemID)
		end
		
		local function unhovered()
			if itemData.HoverItem:GetAttribute'OwnershipID' == ItemID then
				HoverJanitor:Cleanup()
			end
		end
		
		ItemObject.MouseEnter:Connect(hovered)
		ItemObject.SelectionGained:Connect(hovered)
		
		ItemObject.MouseLeave:Connect(unhovered)
		ItemObject.SelectionLost:Connect(unhovered)
	end
	
	return ItemObject
end


function ItemController._Start()
	
end


return ItemController