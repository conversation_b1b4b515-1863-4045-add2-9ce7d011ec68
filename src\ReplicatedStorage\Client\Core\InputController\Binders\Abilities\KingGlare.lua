local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)
local Packets = require(ReplicatedStorage.Data.Packets)

local KingGlare = {
	Key = Enum.KeyCode.C,

	GamepadKey = Enum.KeyCode.ButtonL2
}


function KingGlare.KeyDown()
	Packets.Ability.UseAbility.send({
		AbilityName = script.Name,
		Args = {}
	})
end


function KingGlare.KeyUp()

end


function KingGlare.Click()
	KingGlare.KeyDown()
end


function KingGlare.IsOnCooldown()
	return Client.GetController'AbilityController'.IsAbilityOnCooldown(script.Name)
end


function KingGlare.CanUseAbilityAsCharacter()
	return Client.GetController'AbilityController'.CanUseAbilityAsCharacter(script.Name)
end


function KingGlare._Init()

end


return KingGlare
