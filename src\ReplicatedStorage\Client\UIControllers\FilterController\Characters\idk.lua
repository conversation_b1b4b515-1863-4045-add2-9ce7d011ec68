return {
	['All'] = {
		Order = 1,
		Func = function(frame: GuiObject)
			return true
		end
	},
	
	['Shiny'] = {
		Order = 2,
		Func = function(frame: GuiObject)
			return frame:GetAttribute'Shiny'
		end
	},
	
	['Unevolved'] = {
		Order = 3,
		Func = function(frame: GuiObject)
			return frame:GetAttribute'Shiny' == nil
		end
	},
	
	['Recent'] = {
		Order = 4,
		Func = function(frame: GuiObject)
			print(workspace:GetAttribute'time' - frame:GetAttribute'TimeSummoned')
			return workspace:GetAttribute'time' - frame:GetAttribute'TimeSummoned' < 6*60*60
		end
	}
}