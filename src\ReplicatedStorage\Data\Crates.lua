--[=[
	Author: <PERSON><PERSON><PERSON> (Syveric)
	Crates data
]=]

local ReplicatedStorage = game:GetService('ReplicatedStorage')

local SimpleMarketplace = require(ReplicatedStorage.Data.SimpleMarketplace)

return {
	['Coins Crate'] = {
		Footer = 'LEGENDARY',
		Items = {
			['Hamburger'] = {
				Chance = 20,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Hamburger', 1}
				}
			},
			['Coffee'] = {
				Chance = 20,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Coffee', 1}
				}
			},
			['Onigiri'] = {
				Chance = 20,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Onigiri', 1}
				}
			},
			['Luffy\'s Meat'] = {
				Chance = 20,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Hamburger', 1}
				}
			},
			['Premium Pass'] = {
				Chance = 1,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Premium Pass', 1}
				}
			},
		},
		Open1Price = 50,
		Open10Price = 450,
		Currency = 'Coins',
		Order = 1
	},
	
	['Gems Crate'] = {
		Footer = 'LEGENDARY',
		Items = {
			['Hamburger'] = {
				Chance = 20,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Hamburger', 1}
				}
			},
			['Coffee'] = {
				Chance = 20,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Coffee', 1}
				}
			},
			['Onigiri'] = {
				Chance = 20,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Onigiri', 1}
				}
			},
			['Luffy\'s Meat'] = {
				Chance = 20,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Hamburger', 1}
				}
			},
			['Premium Pass'] = {
				Chance = 1,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Premium Pass', 1}
				}
			},
		},
		Open1Price = 50,
		Open10Price = 450,
		Currency = 'Gems',
		Order = 2
	},
	
	['Robux Crate'] = {
		Footer = 'LEGENDARY',
		Items = {
			['Hamburger'] = {
				Chance = 20,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Hamburger', 1}
				}
			},
			['Coffee'] = {
				Chance = 20,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Coffee', 1}
				}
			},
			['Onigiri'] = {
				Chance = 20,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Onigiri', 1}
				}
			},
			['Luffy\'s Meat'] = {
				Chance = 20,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Hamburger', 1}
				}
			},
			['Premium Pass'] = {
				Chance = 1,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {'Premium Pass', 1}
				}
			},
		},
		Open1Price = SimpleMarketplace.DevProducts.Buy1Crate.ID,
		Open10Price = SimpleMarketplace.DevProducts.Buy10Crates.ID,
		Currency = 'Robux',
		Order = 3
	},
}