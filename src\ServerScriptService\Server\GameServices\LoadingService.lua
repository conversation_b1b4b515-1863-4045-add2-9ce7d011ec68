local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')
local StarterGui = game:GetService('StarterGui')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local Signal = require(ReplicatedStorage.SharedModules.Signal)

local LoadingService = {
	ExpectedPlayers = nil,
	PlayersLoaded = 0,
	Loaded = false,

	FinishedLoading = Signal.new()
}


function LoadingService.FinishLoading()
	if LoadingService.Loaded then return end

	LoadingService.Loaded = true
	workspace:SetAttribute('PlayersLoaded', true)

	LoadingService.FinishedLoading:Fire()

	Packets.Loading.PlayersLoaded.sendToAll()

	print('ALL PLAYERS LOADED')
end


function LoadingService._PlayerAdded(plr: Player)
	if LoadingService.Loaded or Client.Immutable.SERVER_TYPE == 'Lobby' then return LoadingService.FinishLoading() end

	local joinData = plr:GetJoinData()
	if not joinData or not joinData.TeleportData then return LoadingService.FinishLoading() end

	local ExpectedPlayers = joinData.TeleportData.ExpectedPlayers
	
	if not ExpectedPlayers then
		return LoadingService.FinishLoading()
	end
	
	LoadingService.ExpectedPlayers = ExpectedPlayers
	LoadingService.PlayersLoaded += 1

	if LoadingService.PlayersLoaded >= ExpectedPlayers then
		LoadingService.FinishLoading()
	end
end


function LoadingService._Start()
	if Client.Immutable.SERVER_TYPE == 'Lobby' then return end

	task.delay(Server.Immutable.Matchmaking.MAX_LOAD_TIME, LoadingService.FinishLoading)

	Packets.Loading.PlayersLoaded.listen(function(_, plr: Player)
		if LoadingService.Loaded then
			Packets.Loading.PlayersLoaded.sendTo(nil, plr)
		end
	end)
end


function LoadingService._Init()
	local ServerType = Client.Immutable.SERVER_TYPE
	
	StarterGui:WaitForChild'GameplayUI'.Enabled = false
	StarterGui:WaitForChild'LobbyUI'.Enabled = false
end


return LoadingService