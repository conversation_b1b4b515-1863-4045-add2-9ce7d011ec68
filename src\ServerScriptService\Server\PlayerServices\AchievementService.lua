local ServerScriptService = game:GetService('ServerScriptService')
local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)

local Packets = require(ReplicatedStorage.Data.Packets)
local AchievementsData = require(ReplicatedStorage.Data.Achievements)

local AchievementService = {}


function AchievementService.ReplicatePrimaryAchievement(plr: Player, primaryAchievementName: string)
	local DataService = Server.GetService('DataService')

	local PlayerAchievementsData = DataService.Get(plr, 'Achievements')
	
	local AchievementsTbl = {}
	
	for secondaryAchievementName, progress in PlayerAchievementsData[primaryAchievementName] do
		local ThisAchievementInfo = AchievementsData[primaryAchievementName].Secondary[secondaryAchievementName]
		table.insert(AchievementsTbl, {
			Name = secondaryAchievementName,
			Progress = math.clamp(progress, 0, ThisAchievementInfo.Requirement),
			Claimed = PlayerAchievementsData.Claimed[primaryAchievementName][secondaryAchievementName]
		})
	end
	
	Packets.Achievements.RequestAchievementData.sendTo({
		Name = primaryAchievementName,
		Achievements = AchievementsTbl
	}, plr)
end


function AchievementService.UpdateAchievementProgress(plr: Player, secondaryAchievementName: string, increment: number)
	local DataService = Server.GetService('DataService')
	
	local PlayerAchievementsData = DataService.Get(plr, 'Achievements')
	
	for primaryAchievementName, v in PlayerAchievementsData do
		if primaryAchievementName == 'Claimed' then continue end
		
		for secName, value in v do
			if secName == secondaryAchievementName then
				PlayerAchievementsData[primaryAchievementName][secondaryAchievementName] += increment
				AchievementService.ReplicatePrimaryAchievement(plr, primaryAchievementName)
			end
		end
	end
end


function AchievementService.IsAchievementClaimed(plr: Player, primaryAchievementName: string, secondaryAchievementName: string)
	local DataService = Server.GetService('DataService')
	return DataService.Get(plr, 'Achievements').Claimed[primaryAchievementName][secondaryAchievementName]
end


function AchievementService._Start()
	local RoundService = Server.GetService('RoundService')
	local ChallengeService = Server.GetService('ChallengeService')
	
	Packets.Achievements.RequestAchievementData.listen(function(_, plr: Player)
		for achievementName in AchievementsData do
			AchievementService.ReplicatePrimaryAchievement(plr, achievementName)
		end
	end)
	
	RoundService.RoundEndedSignal:Connect(function()
		local Act = RoundService.Act
		if Act and not RoundService.IsTournament and not ChallengeService.Challenge then
			local mapName = RoundService.MapName
			for _, plr in Players:GetPlayers() do
				AchievementService.UpdateAchievementProgress(plr, `{mapName} Act {Act}`, 1)
			end
		end
	end)
end


return AchievementService