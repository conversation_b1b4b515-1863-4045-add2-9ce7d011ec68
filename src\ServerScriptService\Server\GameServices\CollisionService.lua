local ReplicatedStorage = game:GetService('ReplicatedStorage')
local PhysicsService = game:GetService('PhysicsService')

local Client = require(ReplicatedStorage.Client)

local ClientImmutable = Client.Immutable

local CollisionService = {
	ActiveCollisionSets = {} :: { [Model | BasePart | Folder]: RBXScriptConnection }
}


local function setToColGroup(obj: any, group: string)
	if not obj:IsA'BasePart' then return end
	obj.CollisionGroup = group
end


function CollisionService.SetCollisionGroup(obj: Model | BasePart | Folder, group: string)
	if CollisionService.ActiveCollisionSets[obj] then
		CollisionService.ActiveCollisionSets[obj]:Disconnect()
	end
	
	if obj:IsA'BasePart' then
		obj.CollisionGroup = group
	end
	
	for _, v in obj:GetDescendants() do
		setToColGroup(v, group)
	end
	
	CollisionService.ActiveCollisionSets[obj] = obj.DescendantAdded:Connect(function(c)setToColGroup(c, group)end)
end


function CollisionService._PlayerAdded(plr: Player)
	local function charAdded()
		plr.Character:WaitForChild'Humanoid'
		CollisionService.SetCollisionGroup(plr.Character, ClientImmutable.CollisionGroups.PLAYERS)
	end

	if plr.Character then
		coroutine.wrap(charAdded)(plr.Character)
	end;plr.CharacterAdded:Connect(charAdded);
end


function CollisionService._Start()
	PhysicsService:CollisionGroupSetCollidable(ClientImmutable.CollisionGroups.PLAYERS, ClientImmutable.CollisionGroups.PLAYERS, false)
	PhysicsService:CollisionGroupSetCollidable(ClientImmutable.CollisionGroups.PLAYERS, ClientImmutable.CollisionGroups.HOLDABLE_ITEMS, false)
	PhysicsService:CollisionGroupSetCollidable(ClientImmutable.CollisionGroups.PLAYERS, ClientImmutable.CollisionGroups.MAP_WALLS, true)
	PhysicsService:CollisionGroupSetCollidable(ClientImmutable.CollisionGroups.HOLDABLE_ITEMS, ClientImmutable.CollisionGroups.MAP_WALLS, true)
	
	for _, v in ClientImmutable.CollisionGroups do
		PhysicsService:CollisionGroupSetCollidable(v, ClientImmutable.CollisionGroups.NULL, false)
	end
end


function CollisionService._Init()
	for k, v in ClientImmutable.CollisionGroups do
		PhysicsService:RegisterCollisionGroup(v)
	end
end


return CollisionService