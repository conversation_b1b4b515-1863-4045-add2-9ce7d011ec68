local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local GuiService = game:GetService('GuiService')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local Items = require(ReplicatedStorage.Data.Items)

local InventoryController = {
	ItemsData = {},
	EquippedCosmetics = {}
}

local Mouse = Players.LocalPlayer:GetMouse()


local function updateInventory(data: { [string]: { Count: number, TimeCreated: number } })
	InventoryController.ItemsData = data
	
	local ItemController = Client.GetController('ItemController')
	local FilterController = Client.GetController('FilterController')
	
	local InventoryFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Inventory
	local SearchBar = InventoryFrame.Content.TopHolder.SearchBar.TextBox
	local ClickFrame = InventoryFrame.Extendes.Click
	
	for itemName, itemInfo in data do
		if not Items[itemName] then continue end
		
		local ThisItemFrame = InventoryFrame.Content.Holder.Scroller:FindFirstChild(itemName)
		if not ThisItemFrame then
			ThisItemFrame = ItemController.CreateItem({
				ItemName = itemName,
				Rarity = Items[itemName].Rarity,
				Parent = InventoryFrame.Content.Holder.Scroller
			})
			
			ThisItemFrame.MouseButton1Click:Connect(function()
				if ClickFrame:GetAttribute'Selection' == itemName then
					ClickFrame.Visible = false
					return
				end
				
				ClickFrame.Details.ItemName.Text = itemName:upper()
				ClickFrame.Details.Descritpion.Text = Items[itemName].Description
				ClickFrame.Details.Rarity.Visible = false
				ClickFrame.Counter.Text = `OWNED: {InventoryController.ItemsData[itemName].Count}x`
				
				local pos = ThisItemFrame.AbsolutePosition - ClickFrame.Parent.AbsolutePosition
				ClickFrame.Position = UDim2.fromOffset(pos.X + ClickFrame.AbsoluteSize.X/2 + 10, pos.Y)
				
				ClickFrame.Visible = true
				
				ClickFrame:SetAttribute('Selection', itemName)
			end)
		end
		
		ThisItemFrame.Content.Counter.Visible = itemInfo.Count > 1
		ThisItemFrame.Content.Counter.Text = `{itemInfo.Count}x`
		
		ThisItemFrame.Visible = itemInfo.Count >= 1
	end
	
	for _, v in InventoryFrame.Content.Holder.Scroller:GetChildren() do
		if v:IsA'ImageButton' and not data[v.Name] then v:Destroy() end
	end
	
	FilterController.ApplyFilter(InventoryFrame.Content.Holder.Scroller:GetChildren(), SearchBar.Text)
end


local function setupInventoryFrame()
	local FilterController = Client.GetController('FilterController')
	local FeedingController = Client.GetController('FeedingController')
	local UIController = Client.GetController('UIController')
	
	local InventoryFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Inventory
	local FilterFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Filter
	local SearchBar = InventoryFrame.Content.TopHolder.SearchBar.TextBox
	local ClickFrame = InventoryFrame.Extendes.Click
	
	InventoryFrame.Content.TopHolder.FILTER.MouseButton1Click:Connect(function()
		FilterController.OpenFilter()
	end)
	
	InventoryFrame.Content.TopHolder.COSMETICS.MouseButton1Click:Connect(function()
		local isSelected = FilterController.HasFilter('Cosmetics')
		if isSelected then
			FilterController.RemoveCustomFilter('Cosmetics')
		else
			FilterController.AddCustomFilter('Cosmetics', function(frame: GuiObject): boolean
				local itemInfo = Items[frame.Name]
				if itemInfo and itemInfo.Type == 'Cosmetic' then
					return true
				end
				return false
			end)
		end
		
		FilterController.ApplyFilter(InventoryFrame.Content.Holder.Scroller:GetChildren(), SearchBar.Text)
		
		InventoryFrame.Content.TopHolder.COSMETICS.IsSelected.Enabled = not isSelected
		InventoryFrame.Content.TopHolder.COSMETICS.NotSelected.Enabled = isSelected
	end)
	
	do -- click frame
		
		ClickFrame.Use.MouseButton1Click:Connect(function()
			local selectedItem = ClickFrame:GetAttribute('Selection')
			if selectedItem then
				if Items[selectedItem].Type == 'Food' then
					FeedingController.SetFeedFrame()
				else
					Packets.Inventory.UseItem.send(selectedItem)
				end
				
				ClickFrame.Visible = false
			end
		end)
		
		InventoryFrame.Extendes.Click.Visible = false
	end
	
	ClickFrame:GetPropertyChangedSignal'Visible':Connect(function()
		if ClickFrame.Visible == false then
			ClickFrame:SetAttribute('Selection', nil)
		else
			local selectedItem = ClickFrame:GetAttribute('Selection')
			if Items[selectedItem].Type == 'Cosmetic' then
				local isEquipped = table.find(InventoryController.EquippedCosmetics, selectedItem)
				ClickFrame.Use.Txt.Text = isEquipped and 'UNEQUIP' or 'EQUIP'
			else
				ClickFrame.Use.Txt.Text = 'USE'
			end
		end
	end)

	UIController.PageToggled:Connect(function(frame: GuiObject, isOpen: boolean, wasOpened: Frame?)
		if frame == InventoryFrame and isOpen == true then
			FilterController.ApplyFilter(InventoryFrame.Content.Holder.Scroller:GetChildren(), SearchBar.Text)
			ClickFrame.Visible = false
		end
		
		if frame == InventoryFrame and FilterController.CurrentFilterData.FrameName ~= InventoryFrame.Name then
			FilterController.DisableFilter()
			FilterController.ApplyFilter(InventoryFrame.Content.Holder.Scroller:GetChildren(), SearchBar.Text)
		end

		if wasOpened == FilterFrame and frame == InventoryFrame and isOpen == true then
			FilterController.ApplyFilter(InventoryFrame.Content.Holder.Scroller:GetChildren(), SearchBar.Text)
		end
	end)

	SearchBar:GetPropertyChangedSignal'Text':Connect(function()
		FilterController.ApplyFilter(InventoryFrame.Content.Holder.Scroller:GetChildren(), SearchBar.Text)
	end)
end


function InventoryController._Start()
	local InventoryFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.Inventory
	
	Packets.Inventory.GetEquippedCosmetics.listen(function(data: {string})
		InventoryController.EquippedCosmetics = data
	end)
	Packets.Inventory.GetEquippedCosmetics.send()

	task.defer(function()
		setupInventoryFrame()
		Packets.Inventory.UpdateItemsInventory.listen(updateInventory)
		Packets.Inventory.UpdateItemsInventory.send()
	end)
end


return InventoryController