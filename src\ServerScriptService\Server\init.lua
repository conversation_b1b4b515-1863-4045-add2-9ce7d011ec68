--[=[
	Author: <PERSON>ike<PERSON> (Syveric)
	Initialize server modules and core game systems
]=]

local RunService = game:GetService('RunService')
local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local ServerScriptService = game:GetService('ServerScriptService')
local SoundService = game:GetService('SoundService')

local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Cmdr = require(ReplicatedStorage.SharedModules.Cmdr)

local Server = {
	Services = {},
	Immutable = require(script.ServerImmutable)
}


function Server.GetService(service: string)
	return Server.Services[service]
end


function Server._Init()
	local start = os.clock()

	do
		if not workspace:FindFirstChild'DebugMode' then
			local dbm = Instance.new('BoolValue')
			dbm.Name = 'DebugMode'
			dbm.Value = false
			dbm.Parent = workspace
		end

		workspace.DebugMode.Value = RunService:IsStudio() and workspace.DebugMode.Value or false
	end
	
	for _, v in SoundService:GetChildren() do
		if v:IsA'SoundGroup' then
			for _, sound in v:GetDescendants() do
				if sound:IsA'Sound' then
					sound.SoundGroup = v
				end
			end
		end
	end

	for _, v in script:GetDescendants() do
		if v:IsA'ModuleScript' then
			Server.Services[v.Name] = require(v)

			if type(Server.Services[v.Name]) ~= 'table' then
				Server.Services[v.Name] = nil

				if not v:FindFirstAncestorWhichIsA'ModuleScript' then
					warn(`[Server Initiation] '{v.Name}' has not been loaded. Place it as descendant of a ModuleScript or return a table to silence.`)
				end
			end
		end
	end
	
	local AnimationsServer = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
	task.spawn(AnimationsServer.Init, AnimationsServer, {
		AutoLoadAllPlayerTracks = true,
		TimeToLoadPrints = false
	})
	
	Cmdr:RegisterCommandsIn(ServerScriptService.CmdrModules.Commands)
	Cmdr:RegisterHooksIn(ServerScriptService.CmdrModules.Hooks)
	Cmdr:RegisterTypesIn(ServerScriptService.CmdrModules.Types)

	for k, v in Server.Services do
		if type(v._Init) == 'function' then
			v._Init()
		end
	end

	print(
		string.format(
			'Server successfully initialized (V%s) [%.02f ms]',
			game.PlaceVersion,
			(os.clock() - start) * 1000
		)
	)


	for k, v in Server.Services do
		if type(v._Start) == 'function' then
			task.defer(function()
				debug.setmemorycategory(k)
				v._Start()
			end)
		end

		if type(v._PlayerAdded) == 'function' then
			ClassExtension.Players.PlayerAdded(v._PlayerAdded)
		end

		if type(v._PlayerRemoving) == 'function' then
			Players.PlayerRemoving:Connect(v._PlayerRemoving)
		end
	end

	workspace:SetAttribute('ServerReady', true)
end


return Server