local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)

local PassiveModule = {}


function PassiveModule.Get(plr: Player)
	local default = Server.Immutable.CUTTING_SPEED

	local playerCountConfig = Client.Immutable.PLAYER_COUNT_CONFIG[#Players:GetPlayers()]
	if playerCountConfig and playerCountConfig.CuttingSpeedMult then
		default *= playerCountConfig.CuttingSpeedMult
	end

	if plr:GetAttribute('VampiricAgility') then
		default *= 1.1
	end
	
	if plr:GetAttribute('AlchemicalRush') then
		default *= 1.3
	end
	
	if plr:GetAttribute('Character') == 'Shoyo Hinata' then
		default *= 1.15
	elseif plr:GetAttribute('Character') == 'Gon Freecss' then
		default *= 1.1
	end
	
	return Server.Immutable.CUTTING_SPEED - (default - Server.Immutable.CUTTING_SPEED)
end


function PassiveModule.Init()
	
end


return PassiveModule