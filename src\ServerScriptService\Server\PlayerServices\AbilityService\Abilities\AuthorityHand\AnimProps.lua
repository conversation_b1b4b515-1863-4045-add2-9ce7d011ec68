local ReplicatedStorage = game:GetService('ReplicatedStorage')
local TweenService = game:GetService('TweenService')
local Debris = game:GetService('Debris')

local J = require(ReplicatedStorage.SharedModules.Janitor)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)


return function(plr: Player, rulerTrack: AnimationTrack): J.Janitor
	local Janitor = J.new()
	
	do -- instance init
		for _, v in script.LeftArm:GetChildren() do
			Janitor:Add(v:Clone()).Parent = plr.Character['Left Arm']
		end
		
		local a = Janitor:Add(script.Head.a:Clone())
		local aa = Janitor:Add(script.Head.aa:Clone())
		a.Parent = plr.Character.Head
		aa.Parent = plr.Character.Head
		a.Trail.Attachment0 = a
		a.Trail.Attachment1 = aa
		
		local b = Janitor:Add(script.Head.b:Clone())
		local bb = Janitor:Add(script.Head.bb:Clone())
		b.Parent = plr.Character.Head
		bb.Parent = plr.Character.Head
		b.Trail.Attachment0 = b
		b.Trail.Attachment1 = bb
	end
	
	Janitor:Add(rulerTrack:GetMarkerReachedSignal'Prop1':Once(function()
		VFXFunctions.Emit(plr.Character['Left Arm'].Auth['Impact'])
		plr.Character['Left Arm'].HalfTone.Enabled = true
		plr.Character['Left Arm'].Star.Enabled = true
	end))
	
	Janitor:Add(rulerTrack:GetMarkerReachedSignal'Prop2':Once(function()
		VFXFunctions.Emit(plr.Character['Left Arm'].Auth['HalfTone'])
		VFXFunctions.Emit(plr.Character['Left Arm'].Auth['Spec'])
		VFXFunctions.Emit(plr.Character['Left Arm'].Auth['Punch'])
		VFXFunctions.Emit(plr.Character['Left Arm'].Auth['Leer'])
		VFXFunctions.Emit(plr.Character['Left Arm'].Auth['Impact 2'])
		VFXFunctions.Emit(plr.Character['Left Arm'].Auth['Impact'])
		VFXFunctions.Emit(plr.Character['Left Arm'].Auth['Explosion 4'])
		VFXFunctions.Emit(plr.Character['Left Arm'].Auth['Explosion 3'])
		VFXFunctions.Emit(plr.Character['Left Arm'].Auth['Explosion 2'])
		VFXFunctions.Emit(plr.Character['Left Arm'].Auth['Explosion'])
		VFXFunctions.Emit(plr.Character['Left Arm'].Auth['Cool'])
		VFXFunctions.Emit(plr.Character['Left Arm'].Auth['Circular Smack'])
		
		plr.Character['Left Arm'].HalfTone.Enabled = false
		plr.Character['Left Arm'].Star.Enabled = false
	end))
	
	return Janitor
end