local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)

local CharactersData = require(ReplicatedStorage.Data.Characters)
local Packets = require(ReplicatedStorage.Data.Packets)
local XPPerLevel = require(ReplicatedStorage.Data.XPPerLevel)
local Items = require(ReplicatedStorage.Data.Items)

local FeedingController = {
	CurrentLevelAndXP = { Level = 1, XP = 0 },
	SelectedItems = {},
	SelectedCharacter = nil
}


local function updateLevelAndXP(data: { Level: number, XP: number }, increment: number?)
	FeedingController.CurrentLevelAndXP = data

	local FeedingFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Feed
	local xpRequired = XPPerLevel[data.Level]
	local currentXPPercent = math.clamp(data.XP / xpRequired, 0.001, 0.998)

	local incrementXP = increment or 0
	local incrementEndPercent = math.clamp((data.XP + incrementXP) / xpRequired, currentXPPercent + 0.001, 0.999)

	FeedingFrame.Contents.ProgressBar.XPAddition.Text = `+{incrementXP} XP`
	FeedingFrame.Contents.ProgressBar.XPAddition.Visible = incrementXP > 0

	FeedingFrame.Contents.ProgressBar.Lvl.Text = `LVL: {data.Level}`
	FeedingFrame.Contents.ProgressBar.XP.Text = `{data.XP} / {xpRequired} XP`

	FeedingFrame.Contents.ProgressBar.Holder.Loader.UIGradient.Transparency = NumberSequence.new{
		NumberSequenceKeypoint.new(0, 0),
		NumberSequenceKeypoint.new(currentXPPercent, 0),
		NumberSequenceKeypoint.new(currentXPPercent + 0.001, 0.5),
		NumberSequenceKeypoint.new(incrementEndPercent, 0.5),
		NumberSequenceKeypoint.new(incrementEndPercent + 0.001, 1),
		NumberSequenceKeypoint.new(1, 1)
	}
end


function FeedingController.LoadUnselectedFoods()
	local ItemController = Client.GetController('ItemController')
	local InventoryController = Client.GetController('InventoryController')
	
	local FeedingFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Feed
	
	local FoodItemsHolder = FeedingFrame.Contents.Contents.Feed.Inventory.Items.Scroller
	
	for _, v in FoodItemsHolder:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	for itemName, itemInfo in InventoryController.ItemsData do
		if not Items[itemName] or Items[itemName].Type ~= 'Food' then continue end
		
		local thisItemCount = itemInfo.Count - (FeedingController.SelectedItems[itemName] or 0)
		local ThisItemFrame = ItemController.CreateItem({
			ItemName = itemName,
			Parent = FoodItemsHolder,
			Count = thisItemCount
		})

		ThisItemFrame.Visible = thisItemCount >= 1
		
		ThisItemFrame.MouseButton1Click:Connect(function()
			FeedingController.SelectedItems[itemName] = (FeedingController.SelectedItems[itemName] or 0) + 1
			FeedingController.LoadSelectedFeedingItems()
		end)
	end
end


function FeedingController.ClearSelectedItems()
	FeedingController.SelectedItems = {}
	FeedingController.LoadSelectedFeedingItems()
end


function FeedingController.LoadSelectedFeedingItems()
	local ItemController = Client.GetController('ItemController')
	
	local FeedingFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Feed
	
	for _, v in FeedingFrame.Contents.Contents.Feed.Feed.Items.Scroller:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	local XPIncrement = 0
	for itemName, count in FeedingController.SelectedItems do
		local ThisItemFrame = ItemController.CreateItem({
			ItemName = itemName,
			Parent = FeedingFrame.Contents.Contents.Feed.Feed.Items.Scroller,
			Count = count
		})

		ThisItemFrame.Visible = count >= 1
		
		ThisItemFrame.MouseButton1Click:Connect(function()
			FeedingController.SelectedItems[itemName] -= 1
			FeedingController.LoadSelectedFeedingItems()
		end)
		
		XPIncrement += Items[itemName].XPReward * count
	end
	
	FeedingController.LoadUnselectedFoods()
	
	updateLevelAndXP(FeedingController.CurrentLevelAndXP, XPIncrement)
end


function FeedingController.ToggleConfirm(enabled: boolean)
	local FeedingFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Feed
	FeedingFrame.Extendes.Buttons.Confirm.Visible = enabled
end


function FeedingController.SetFeedFrame(characterID: string)
	if not characterID then
		-- TODO
	end
	
	local ItemController = Client.GetController('ItemController')
	local UIController = Client.GetController('UIController')
	local CharacterInventoryController = Client.GetController('CharacterInventoryController')
	
	local FeedingFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Feed
	local CharactersInventoryFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Characters
	
	local CharacterName = CharacterInventoryController.GetCharacterNameFromID(characterID)
	
	FeedingController.SelectedCharacter = characterID
	
	if FeedingFrame.Contents.Contents.Unit:FindFirstChild('Unit') then
		FeedingFrame.Contents.Contents.Unit.Unit:Destroy()
	end
	
	local ItemFrame = ItemController.CreateItem({
		ItemName = CharacterName,
		Position = script.UnitContainer.Position,
		Size = script.UnitContainer.Size,
		Rarity = CharactersData[CharacterName].Rarity,
		Parent = FeedingFrame.Contents.Contents.Unit,
		AnchorPoint = script.UnitContainer.AnchorPoint
	})
	
	ItemFrame.Name = 'Unit'
	
	ItemFrame.MouseButton1Click:Connect(function()
		UIController.Open(CharactersInventoryFrame)
	end)
	
	Packets.Inventory.GetCharacterLevel.send({Character = characterID})
	
	FeedingController.ClearSelectedItems()
	FeedingController.LoadUnselectedFoods()
	
	UIController.Open(FeedingFrame)
end


function FeedingController._Start()
	local UIController = Client.GetController('UIController')
	local InventoryController = Client.GetController('InventoryController')
	
	local FeedingFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.Feed
	
	UIController.PageToggled:Connect(function(page: GuiObject, isOpen: boolean)
		if page == FeedingFrame and isOpen == false then
			FeedingController.ToggleConfirm(false)
		end
	end)
	
	FeedingFrame.Contents.Contents.Feed.Feed.Buttons.RemoveAll.MouseButton1Click:Connect(function()
		FeedingController.ClearSelectedItems()
	end)
	
	FeedingFrame.Contents.Contents.Feed.Feed.Buttons.AddAll.MouseButton1Click:Connect(function()
		FeedingController.ClearSelectedItems()
		
		for itemName, itemInfo in InventoryController.ItemsData do
			if not Items[itemName] or Items[itemName].Type ~= 'Food' then continue end
			
			FeedingController.SelectedItems[itemName] = itemInfo.Count
		end
		
		FeedingController.LoadSelectedFeedingItems()
	end)
	
	FeedingFrame.Contents.Contents.Feed.Inventory.Buttons.Cancel.MouseButton1Click:Connect(function()
		UIController.Close(FeedingFrame)
	end)
	
	FeedingFrame.Contents.Contents.Feed.Inventory.Buttons.Feed.MouseButton1Click:Connect(function()
		if not FeedingController.SelectedCharacter then return end
		FeedingController.ToggleConfirm(true)
	end)
	
	FeedingFrame.Extendes.Buttons.Confirm.MouseButton1Click:Connect(function()
		if not FeedingController.SelectedCharacter then return end

		Packets.Inventory.FeedCharacter.send({
			Character = FeedingController.SelectedCharacter,
			Foods = FeedingController.SelectedItems
		})
		
		FeedingController.ToggleConfirm(false)
	end)
	
	Packets.Inventory.CharacterFed.listen(function()
		FeedingController.ClearSelectedItems()
		Packets.Inventory.GetCharacterLevel.send({Character = FeedingController.SelectedCharacter})
	end)
	
	Packets.Inventory.GetCharacterLevel.listen(updateLevelAndXP)
	
	task.defer(FeedingController.ToggleConfirm, false)
end


return FeedingController