local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local TweenService = game:GetService('TweenService')
local Debris = game:GetService('Debris')
local SoundService = game:GetService('SoundService')
local CollectionService = game:GetService('CollectionService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local CookingService: typeof(require(ServerScriptService.Server.GameServices.CookingService)) = Server.GetService('CookingService')

local VampiricAgility = {
	COOLDOWN = 5,
	EFFECT_TIME = 20,
	
	Cooldowns = {} :: { [Player]: number },
	AbilityJanitors = {} :: { [Player]: Janitor.Janitor }
}


function VampiricAgility.GetCooldownTimeLeft(plr: Player): number
	if VampiricAgility.Cooldowns[plr] then
		return VampiricAgility.COOLDOWN - (os.clock() - VampiricAgility.Cooldowns[plr])
	end
	return 0
end


function VampiricAgility.CanUse(plr: Player): boolean
	if VampiricAgility.Cooldowns[plr] then
		return os.clock() - VampiricAgility.Cooldowns[plr] > VampiricAgility.COOLDOWN
	end
	
	return true
end


function VampiricAgility.IsInUse(plr: Player): boolean
	if VampiricAgility.AbilityJanitors[plr] and VampiricAgility.AbilityJanitors[plr]:Get('Active') then
		return true
	end

	return false
end


function VampiricAgility.UseAbility(plr: Player): boolean
	if not VampiricAgility.CanUse(plr) or VampiricAgility.IsInUse(plr) then return false end

	VampiricAgility.Cooldowns[plr] = os.clock()

	task.delay(VampiricAgility.COOLDOWN, function()
		VampiricAgility.Cooldowns[plr] = nil
	end)

	if not VampiricAgility.AbilityJanitors[plr] then
		VampiricAgility.AbilityJanitors[plr] = Janitor.new()
	end
	
	VampiricAgility.AbilityJanitors[plr]:Add(math.random, nil, 'Active')
	
	ClassExtension.Player.DisableMovement(plr, true)
	plr:SetAttribute(script.Name, true)
	
	local VampTrack: AnimationTrack = Animations:PlayTrack(plr, 'VampiricAgility')
	
	SoundService.SFX.Abilities.VampiricAgility:Play()
	
	do
		local CharFX = {} :: { ParticleEmitter }
		for _, bodyPart in script.CharFX:GetChildren() do
			for _, v in bodyPart:GetChildren() do
				local ThisFX = v:Clone()
				ThisFX.Parent = plr.Character[bodyPart.Name]
				table.insert(CharFX, ThisFX)
			end
		end

		VampiricAgility.AbilityJanitors[plr]:Add(function()
			for _, v in CharFX do
				VFXFunctions.DisableDescendants(v)
				Debris:AddItem(v, VFXFunctions.GetHighestWaitTime(v))
			end
		end)
	end
	
	VampiricAgility.AbilityJanitors[plr]:Add(task.delay(VampiricAgility.EFFECT_TIME, function()
		VampiricAgility.AbilityJanitors[plr]:Cleanup()
	end))
	
	VampiricAgility.AbilityJanitors[plr]:Add(VampTrack.Ended:Connect(function()
		ClassExtension.Player.DisableMovement(plr, false)
		VampiricAgility.AbilityJanitors[plr]:Remove('Active')
	end))
	
	VampiricAgility.AbilityJanitors[plr]:Add(function()
		plr:SetAttribute(script.Name, nil)
		Animations:StopTrack(plr, 'VampiricAgility')
		ClassExtension.Player.DisableMovement(plr, false)
		
		Server.GetService('AbilityService').UpdatePassives(plr)
	end)
	
	return true
end


function VampiricAgility.CancelAbility(plr: Player)
	if not VampiricAgility.IsInUse(plr) then return end

	if VampiricAgility.AbilityJanitors[plr] then
		VampiricAgility.AbilityJanitors[plr]:Cleanup()
	end
end


return VampiricAgility