--[=[
	Author: <PERSON><PERSON><PERSON> (Syveric)
	Sky map moving platform
]=]

local TweenService = game:GetService('TweenService')

local start = CFrame.new(-17.0999985, -11.2000055, -90.1270142, 0, 0, -1, 0, -1, 0, -1, 0, 0)
local finish = CFrame.new(-17.0999985, -11.2000055, -188.477005, 0, 0, -1, 0, -1, 0, -1, 0, 0)

local function TweenModel(model: Model, targetCFrame: CFrame, tweenInfo: TweenInfo)
	local cframeValue = Instance.new('CFrameValue')
	cframeValue.Value = model:GetPivot()

	cframeValue:GetPropertyChangedSignal'Value':Connect(function()
		model:PivotTo(cframeValue.Value)
	end)

	local tween = TweenService:Create(cframeValue, tweenInfo, { Value = targetCFrame })
	tween:Play()

	tween.Completed:Connect(function()
		cframeValue:Destroy()
	end)
end


local t = 13.5

script.Parent:PivotTo(finish)
while true do
	TweenModel(script.Parent, start, TweenInfo.new(t, Enum.EasingStyle.Linear))
	task.wait(t)
	TweenModel(script.Parent, finish, TweenInfo.new(t, Enum.EasingStyle.Linear))
	task.wait(t)
end