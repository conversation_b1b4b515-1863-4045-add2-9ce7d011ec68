local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local MarketplaceService = game:GetService('MarketplaceService')
local RunService = game:GetService('RunService')
local TweenService = game:GetService('TweenService')

local Client = require(ReplicatedStorage.Client)

local SimlpeMarketplace = require(ReplicatedStorage.Data.SimpleMarketplace)
local Packets = require(ReplicatedStorage.Data.Packets)
local Janitor = require(ReplicatedStorage.SharedModules.Janitor)

local StoreController = {
	CurrentGiftingItemId = nil
}


local function loadStore()
	local ItemController = Client.GetController('ItemController')
	
	local StoreFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Store
	
	for _, v in StoreFrame.ScrollingFrame:GetChildren() do
		if v:IsA'GuiObject' then v:Destroy() end
	end
	
	-- Packs
	local PACK_ORDER = 100
	local FirstPack
	for packName, packInfo in Client.Immutable.Packs do
		local PackContainer = script.PackContainer:Clone()
		PackContainer.LayoutOrder = PACK_ORDER
		PackContainer.Name = packName
		PackContainer.PackName.Text = packName
		PackContainer.Description.Text = packInfo.Description
		PackContainer.Parent = StoreFrame.ScrollingFrame
		
		for _, v in PackContainer.Contents:GetChildren() do
			if v:IsA'GuiObject' then v:Destroy() end
		end
		
		for _, itemInfo in packInfo.Items do
			ItemController.CreateItem({
				Count = itemInfo.Count,
				ItemName = itemInfo.Name,
				Size = script.PackContainer.Contents.Container.Size,
				Parent = PackContainer.Contents
			})
		end
		
		task.spawn(function()
			local prodInfo = SimlpeMarketplace.GetProductInfo(packInfo.ID, Enum.InfoType.Product, 20)
			if prodInfo then
				PackContainer.Buy.Txt.Text = `{prodInfo.PriceInRobux} R$`
			end
		end)
		
		PackContainer.Buy.MouseButton1Click:Connect(function()
			MarketplaceService:PromptProductPurchase(Players.LocalPlayer, packInfo.ID)
		end)
		
		PackContainer.Gift.MouseButton1Click:Connect(function()
			StoreController.SetGifting(packInfo.ID)
		end)
		
		FirstPack = PackContainer
	end
	
	-- Gamepasses
	local PASS_ORDER = 200
	local FirstPass
	for passName, passInfo in SimlpeMarketplace.Gamepasses do
		local GamepassContainer = script.GamepassContainer:Clone()
		GamepassContainer.LayoutOrder = PASS_ORDER
		GamepassContainer.Name = passName
		GamepassContainer.PassName.Text = passName
		GamepassContainer.Description.Text = passInfo.Description
		GamepassContainer.Parent = StoreFrame.ScrollingFrame
		
		task.spawn(function()
			local prodInfo = SimlpeMarketplace.GetProductInfo(passInfo.ID, Enum.InfoType.GamePass, 20)
			if prodInfo then
				GamepassContainer.Icon.Image = 'rbxassetid://' .. prodInfo.IconImageAssetId
				GamepassContainer.Buy.Txt.Text = `{prodInfo.PriceInRobux} R$`
			end
		end)
		
		GamepassContainer.Buy.MouseButton1Click:Connect(function()
			MarketplaceService:PromptGamePassPurchase(Players.LocalPlayer, passInfo.ID)
		end)
		
		GamepassContainer.Gift.MouseButton1Click:Connect(function()
			StoreController.SetGifting(passInfo.GiftID)
		end)
		
		FirstPass = GamepassContainer
	end
	
	-- Currency
	local CURRENCY_ORDER = 300
	local FirstCoins
	local FirstGems
	for prodName, devProdInfo in SimlpeMarketplace.DevProducts do
		if not devProdInfo.Currency then continue end
		
		local CurrencyContainer = script.CurrencyContainer:Clone()
		CurrencyContainer.LayoutOrder = CURRENCY_ORDER + devProdInfo.Order
		CurrencyContainer.Name = prodName
		CurrencyContainer.Parent = StoreFrame.ScrollingFrame
		
		task.spawn(function()
			local prodInfo = SimlpeMarketplace.GetProductInfo(devProdInfo.ID, Enum.InfoType.Product, 20)
			if prodInfo then
				CurrencyContainer.PassName.Text = prodInfo.Name
				CurrencyContainer.Icon.Image = 'rbxassetid://' .. prodInfo.IconImageAssetId
				CurrencyContainer.Buy.Txt.Text = `{prodInfo.PriceInRobux} R$`
				CurrencyContainer.Description.Text = prodInfo.Description
			end
		end)
		
		CurrencyContainer.Buy.MouseButton1Click:Connect(function()
			MarketplaceService:PromptProductPurchase(Players.LocalPlayer, devProdInfo.ID)
		end)
		
		CurrencyContainer.Gift.MouseButton1Click:Connect(function()
			StoreController.SetGifting(devProdInfo.ID)
		end)
		
		if devProdInfo.Currency == 'Coins' then
			if not FirstCoins then
				FirstCoins = CurrencyContainer
			else
				if CurrencyContainer.LayoutOrder < FirstCoins.LayoutOrder then
					FirstCoins = CurrencyContainer
				end
			end
		elseif devProdInfo.Currency == 'Gems' then
			if not FirstGems then
				FirstGems = CurrencyContainer
			else
				if CurrencyContainer.LayoutOrder < FirstGems.LayoutOrder then
					FirstGems = CurrencyContainer
				end
			end
		end
	end
	
	for _, v in StoreFrame.Categories:GetChildren() do
		if not v:IsA'ImageButton' then continue end
		
		local Reference = (v.Name == 'Bundles' and FirstPack)
			or (v.Name == 'Passes' and FirstPass)
			or (v.Name == 'Coins' and FirstCoins)
			or (v.Name == 'Gems' and FirstGems)
		
		v.MouseButton1Click:Connect(function()
			local currentPos = StoreFrame.ScrollingFrame.CanvasPosition
			StoreFrame.ScrollingFrame.CanvasPosition = Vector2.zero
			local res = Vector2.new(0, Reference.AbsolutePosition.Y - StoreFrame.ScrollingFrame.AbsolutePosition.Y)
			StoreFrame.ScrollingFrame.CanvasPosition = currentPos
			
			TweenService:Create(
				StoreFrame.ScrollingFrame,
				TweenInfo.new(.25, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
				{CanvasPosition = res}
			):Play()
		end)
	end
end


local GiftedJanitor = Janitor.new()
local function gotGifted(data: { FromUsername: string, ID: number })
	local GiftReceivedFrame = Players.LocalPlayer.PlayerGui.LobbyUI.GiftRecive
	
	GiftedJanitor:Cleanup()
	
	GiftReceivedFrame.Banner.Title.Text = `YOU GOT A GIFT FROM @{data.FromUsername}!`
	
	local prodInfo = SimlpeMarketplace.GetProductInfo(data.ID, Enum.InfoType.Product, 20)
	if prodInfo then
		GiftReceivedFrame.Banner.Worth.Text = `WORTH {prodInfo.PriceInRobux}R$`
		GiftReceivedFrame.Banner.GiftName.Text = prodInfo.Name:upper()
	end
	
	GiftedJanitor:Add(function()
		GiftReceivedFrame.Visible = false
	end)
	
	GiftedJanitor:Add(GiftReceivedFrame.Buttons.Accept.MouseButton1Click:Connect(function()
		GiftedJanitor:Cleanup()
	end))
	
	local tLeft = 30
	GiftedJanitor:Add(RunService.RenderStepped:Connect(function(dt: number)
		GiftReceivedFrame.Bar.Timer.Text = `{math.ceil(tLeft)}s`
		
		local progress = tLeft / 30
		GiftReceivedFrame.Bar.Holder.Loader.UIGradient.Transparency = NumberSequence.new{
			NumberSequenceKeypoint.new(0, 0),
			NumberSequenceKeypoint.new(math.clamp(progress, .001, .998), 0),
			NumberSequenceKeypoint.new(math.clamp(progress + .001, .002, .999), 1),
			NumberSequenceKeypoint.new(1, 1),
		}
		tLeft -= dt
	end))
	
	GiftReceivedFrame.Visible = true
end


local function setupGiftFrame()
	local UIController = Client.GetController('UIController')
	
	local GiftFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Gift
	local StoreFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Store
	
	for _, v in GiftFrame.Players.Slots.Scroller:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	local function addPlayer(plr: Player)
		if plr == Players.LocalPlayer then return end
		
		local ThisPlayerTemplate = script.Player:Clone()
		ThisPlayerTemplate.PlayerName.Text = `@{plr.Name:upper()}`
		ThisPlayerTemplate.Name = plr.Name
		ThisPlayerTemplate.Parent = GiftFrame.Players.Slots.Scroller
		
		ThisPlayerTemplate.Invite.MouseButton1Click:Connect(function()
			Packets.Store.SetGiftingUser.send(plr.Name)
			MarketplaceService:PromptProductPurchase(plr, StoreController.CurrentGiftingItemId)
		end)
	end
	
	for _, v in Players:GetPlayers() do
		addPlayer(v)
	end
	
	Players.PlayerAdded:Connect(addPlayer)
	
	Players.PlayerRemoving:Connect(function(plr: Player)
		if GiftFrame.Players.Slots.Scroller:FindFirstChild(plr.Name) then
			GiftFrame.Players.Slots.Scroller[plr.Name]:Destroy()
		end
	end)
	
	GiftFrame.Cancel.MouseButton1Click:Connect(function()
		UIController.Open(StoreFrame)
	end)
end


function StoreController.SetGifting(id: number)
	local UIController = Client.GetController('UIController')
	
	local GiftFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Gift

	UIController.Open(GiftFrame)

	local GiftName = SimlpeMarketplace.GetNameFromID(id)
	GiftFrame.Banner.GiftName.Text = `GIFTING: {GiftName}`
	
	for _, v in GiftFrame.Players.Slots.Scroller:GetChildren() do
		if v:IsA'GuiObject' then
			local ThisPlayer = Players:FindFirstChild(v.Name)
			if ThisPlayer then
				v.Visible = false
				
				local prodName = SimlpeMarketplace.GetNameFromID(id)
				if SimlpeMarketplace.Gamepasses[prodName] then
					task.spawn(function()
						if not SimlpeMarketplace.UserOwnsGamePassAsync(ThisPlayer.UserId, id) then
							v.Visible = true
						end
					end)
				else
					v.Visible = true
				end
			end
		end
	end
	
	task.spawn(function()
		local prodInfo = SimlpeMarketplace.GetProductInfo(id, Enum.InfoType.Product, 1)
		if prodInfo then
			GiftFrame.Banner.Worth.Text = `WORTH: {prodInfo.PriceInRobux} R$`
		end
	end)
	
	StoreController.CurrentGiftingItemId = id
end


function StoreController._Start()
	local UIController = Client.GetController('UIController')
	
	Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'
	
	Packets.Store.GiftReceived.listen(gotGifted)
	
	task.defer(loadStore)
	task.defer(setupGiftFrame)
end


return StoreController