local ReplicatedStorage = game:GetService('ReplicatedStorage')

local Packets = require(ReplicatedStorage.Data.Packets)

local EffectController = {
	Effects = {}
}


function EffectController.Do(effectName: string, ...)
	EffectController.Effects[effectName](...)
end


function EffectController._Start()
	Packets.EffectController.Do.listen(function(data: { Args: {unknown}, Effect: string })
		EffectController.Do(data.Effect, table.unpack(data.Args))
	end)
end


function EffectController._Init()
	for _, v in script:GetDescendants() do
		if v:IsA'ModuleScript' then
			EffectController.Effects[v.Name] = require(v)
			
			if type(EffectController.Effects[v.Name]) ~= 'function' then
				EffectController.Effects[v.Name] = nil
				warn(`[EffectController] {v.Name} is not a valid Effect module`)
			end
		end
	end
end


return EffectController