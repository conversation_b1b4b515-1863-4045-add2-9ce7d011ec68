local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local PassiveModule = {}


function PassiveModule.Get(isSmallOrder: boolean)
	local TutorialService = Server.GetService('TutorialService')
	local ChallengeService = Server.GetService('ChallengeService')
	local OrderService = Server.GetService('OrderService')
	local RoundService = Server.GetService('RoundService')
	
	local default = 60
	
	if Client.Immutable.Round.WaveOrderSpeeds[OrderService.Wave] then
		default = isSmallOrder and Client.Immutable.Round.WaveOrderSpeeds[OrderService.Wave].SMALL_ORDER_COMPLETION_TIME
			or Client.Immutable.Round.WaveOrderSpeeds[OrderService.Wave].BIG_ORDER_COMPLETION_TIME
	end
	
	if RoundService.Act then
		local res = Client.Immutable.Servers.PLACE_INFO[RoundService.MapName].ActsInfo[RoundService.Act][RoundService.Difficulty].TimeLimitPerOrder
		default = res or default
	end
	
	if ChallengeService.Modifiers[script.Name] then
		default = ChallengeService.Modifiers[script.Name]
	end
	
	if TutorialService.Tutorial then
		default = 9999
	end
	
	return default
end


function PassiveModule.Init()

end


return PassiveModule