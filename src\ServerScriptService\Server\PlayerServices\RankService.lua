local Players = game:GetService('Players')
local ReplicatedStorage = game:GetService('ReplicatedStorage')

local Signal = require(ReplicatedStorage.SharedModules.Signal)

local RankService = {
	GROUP_ID = 35717082
}


local cachedRanks, cachedRoles = {}, {}
local infoLoaded = Signal.new()


function RankService.GetRank(player: Player, maxYield: number?)
	if type(maxYield) ~= 'number' then
		maxYield = nil
	end

	if not player or not player:IsDescendantOf(Players) then return 0 end

	if cachedRanks[player] then
		return cachedRanks[player]
	else
		local yieldSignal = Signal.new()
		local loadedSignal = infoLoaded:Connect(function(loadedPlayer: Player, loaaded: boolean)
			if player == loadedPlayer then
				yieldSignal:Fire()
			end
		end)

		local dead = false
		task.delay(maxYield or 25, function()
			if dead then return end

			yieldSignal:Fire()
			loadedSignal:Destroy()
			yieldSignal:Destroy()
		end)

		yieldSignal:Wait()

		dead = true
		yieldSignal:Destroy()
		loadedSignal:Destroy()
	end

	return cachedRanks[player] or 0
end


function RankService.GetRole(player: Player, maxYield: number?)
	if type(maxYield) ~= 'number' then
		maxYield = nil
	end

	if not player or not player:IsDescendantOf(Players) then return 'Unknown' end

	if cachedRoles[player] then
		return cachedRoles[player]
	else
		local yieldSignal = Signal.new()
		local loadedSignal = infoLoaded:Connect(function(loadedPlayer: Player, loaaded: boolean)
			if player == loadedPlayer then
				yieldSignal:Fire()
			end
		end)

		local dead = false
		task.delay(maxYield or 25, function()
			if dead then return end

			yieldSignal:Fire()
			loadedSignal:Destroy()
			yieldSignal:Destroy()
		end)

		yieldSignal:Wait()

		dead = true
		yieldSignal:Destroy()
		loadedSignal:Destroy()
	end

	return cachedRoles[player] or 'Unknown'
end


function RankService._PlayerRemoving(plr: Player)
	cachedRanks[plr] = nil
	cachedRoles[plr] = nil
end


function RankService._PlayerAdded(plr: Player)
	repeat
		local success, fail = pcall(function()
			cachedRanks[plr] = plr:GetRankInGroup(RankService.GROUP_ID)
			plr:SetAttribute('Rank', cachedRanks[plr])
			
			cachedRoles[plr] = plr:GetRoleInGroup(RankService.GROUP_ID)
			plr:SetAttribute('Role', cachedRoles[plr])
		end)
		
		if not success then
			warn('Rank fetch failure for: ' .. plr.Name)
			task.wait(3)
		end
	until success or not plr:IsDescendantOf(game)

	infoLoaded:Fire(plr, plr:IsDescendantOf(game))
end


function RankService._Start()

end


return RankService