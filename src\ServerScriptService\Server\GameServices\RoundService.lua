local ServerStorage = game:GetService('ServerStorage')
local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local ServerScriptService = game:GetService('ServerScriptService')
local HttpService = game:GetService('HttpService')
local Lighting = game:GetService('Lighting')
local CollectionService = game:GetService('CollectionService')
local RunService = game:GetService('RunService')
local TeleportService = game:GetService('TeleportService')

local Client = require(ReplicatedStorage.Client)
local Server = require(ServerScriptService.Server)

local ClientImmutable = Client.Immutable
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Packets = require(ReplicatedStorage.Data.Packets)
local Signal = require(ReplicatedStorage.SharedModules.Signal)
local NumberController = require(ReplicatedStorage.SharedModules.NumberController)


local RoundService = {
	MapName = nil :: string?,
	MapFolder = nil :: Folder?,
	
	IsRoundActive = false,
	RoundStart = 0,
	
	RoundEndedSignal = Signal.new(),
	RoundStartedSignal = Signal.new(),
	
	RoundID = 0,
	
	ComboMult = 1,
	Score = 0,
	
	Act = nil,
	Difficulty = 'Easy',
	
	IsTournament = false,
	
	Tips = 0,
	
	ReplayVotes = 0,
	Votes = {},
	PlayersEndedWith = 10
}


function RoundService.SendRoundData(plr: Player?)
	local OrderService = Server.GetService('OrderService')

	local dataToSend = {
		RoundStart = RoundService.RoundStart,
		OrdersFailed = OrderService.OrdersFailed,
		OrdersCompleted = OrderService.OrdersCompleted,
		Act = RoundService.Act,
		Difficulty = RoundService.Difficulty,
		FailableOrders = RoundService.GetFailableOrders(),
		OrdersToComplete = RoundService.GetOrdersToComplete(),
		RoundLen = RoundService.GetRoundLen()
	}
	
	if plr then
		Packets.Round.GetRoundInfo.sendTo(dataToSend, plr)
	else
		Packets.Round.GetRoundInfo.sendToAll(dataToSend)
	end
end


local function startGame()
	local TutorialService = Server.GetService('TutorialService')
	
	local s, e = pcall(function()
		RoundService.StartRound()
		
		if TutorialService.Tutorial then return end
		
		local thisRoundID = RoundService.RoundID
		task.delay(RoundService.GetRoundLen() - RoundService.Elapsed(), function()
			if thisRoundID == RoundService.RoundID then
				RoundService.EndRound(true)
			end
		end)
	end)
	
	if not s then
		warn(e)
		RoundService.EndRound()
	end
end


local function handleScore()
	local OrderService: typeof(require(script.Parent.OrderService)) = Server.GetService('OrderService')
	
	local FirstOrder
	OrderService.OrderCreated:Connect(function(orderData)
		FirstOrder = OrderService.Orders[1].OrderID
	end)
	
	OrderService.OrderDeleted:Connect(function(orderData, expired: boolean?, plr: Player?)
		if expired then
			RoundService.IncrementScore(ClientImmutable.Round.Scoring.EXPIRED_ORDER)
		elseif plr then
			if orderData.OrderID == FirstOrder then
				RoundService.ComboMult += ClientImmutable.Round.Scoring.COMBO_SEQUENCE_MULTIPLIER
			end
			
			local tip = math.random(ClientImmutable.Round.Scoring.TIP_BONUS_RANGE.Min, ClientImmutable.Round.Scoring.TIP_BONUS_RANGE.Max)
			
			RoundService.IncrementScore(ClientImmutable.Round.Scoring.ORDER_COMPLETED)
			RoundService.IncrementScore(tip)
			
			RoundService.Tips += tip
		end
	end)
end


local function giveRoundRewards()
	local RewardsInfo = Client.Immutable.Servers.PLACE_INFO[RoundService.MapName].ActsInfo[RoundService.Act][RoundService.Difficulty].Rewards
	for _, plr in Players:GetPlayers() do
		if RewardsInfo then
			for _, reward in RewardsInfo do
				Server.GetService(reward.Reward.Service)[reward.Reward.Function](plr, table.unpack(reward.Reward.Args))
			end
		end
	end
end


function RoundService.GetRoundLen()
	return ClientImmutable.Round.ROUND_LEN
end


function RoundService.GetFailableOrders()
	if RoundService.IsTournament then
		return 999
	end
	
	if RoundService.Act and RoundService.MapName then
		local FailableOrders = ClientImmutable.Servers.PLACE_INFO[RoundService.MapName].ActsInfo[RoundService.Act][RoundService.Difficulty].FailableOrders
		if FailableOrders then
			return FailableOrders
		end
	end
	
	return 3
end


function RoundService.GetOrdersToComplete()
	if RoundService.Act and RoundService.MapName then
		local OrdersToComplete = ClientImmutable.Servers.PLACE_INFO[RoundService.MapName].ActsInfo[RoundService.Act][RoundService.Difficulty].OrdersToComplete
		if OrdersToComplete then
			return OrdersToComplete
		end
	end
	return 5
end


function RoundService.IncrementScore(increment: number)
	increment = NumberController.FixNumber(increment, 0)
	
	if increment > 0 then
		increment *= RoundService.ComboMult
		
		if RoundService.Act then
			local ActMult = ClientImmutable.ACT_CONFIG[RoundService.Act].ScoreMult
			increment *= ActMult
		end
	end
	
	RoundService.Score = math.max(0, RoundService.Score + increment)
	
	Packets.Round.ScoreUpdated.sendToAll(RoundService.Score)
end


function RoundService.Elapsed()
	return workspace:GetServerTimeNow() - RoundService.RoundStart
end


function RoundService._LoadMap(mapName: string): Folder
	local CookingService = Server.GetService('CookingService')
	
	local Map: Folder = ServerStorage.Assets.Maps[mapName]:Clone()
	Map.Name = 'Map'
	
	local MapModel do -- fall height
		MapModel = Instance.new('Model')
		Map.Parent = MapModel
		
		local cf, sz = MapModel:GetBoundingBox()
		local height = math.max(Server.Immutable.FALL_HEIGHT_MIN, cf.Y - sz.Y/2)
		
		local p = Instance.new('Part')
		p.Transparency = 1
		p.Anchored = true
		p.CanCollide = true
		p.Size = Vector3.new(sz.X, 2, sz.Y)
		p.Position = Vector3.new(cf.X, height, cf.Z)
		p.Parent = workspace
		
		local MapSpawn = ClassExtension.Instance.FindFirstChildOfClass(Map, 'SpawnLocation', true)
		p.Touched:Connect(function(hit: BasePart)
			local Humanoid = hit.Parent:FindFirstChild'Humanoid' or hit.Parent.Parent:FindFirstChild'Humanoid'
			if Humanoid then
				Humanoid.Parent:PivotTo(MapSpawn.CFrame * CFrame.new(0, 2, 0))
			end
		end)
	end
	
	do -- setup lighting
		Lighting:ClearAllChildren()
		for _, v in Map.Lighting.Instances:GetChildren() do
			v:Clone().Parent = Lighting
		end
		
		for _, v in Map.Lighting.Properties:GetChildren() do
			Lighting[v.Name] = v.Value
		end
	end
	
	if ClientImmutable.SERVER_TYPE == 'Round' then
		local PlatesToHave = RoundService.GetOrdersToComplete() - 1
		
		local plates = {}
		for _, v in Map:GetDescendants() do
			if v:IsA'BasePart' and CookingService.IsSubmittableFoodSurface(v) and v.Name == 'Plate' then
				table.insert(plates, v)
			end
		end

		local toRemove = #plates - PlatesToHave
		for i = 1, toRemove do
			plates[i]:Destroy()
		end
	end
	
	Map.Parent = workspace
	MapModel:Destroy()
	
	return Map
end


function RoundService.StartRound()
	local PlaceName = ClientImmutable.Servers.PLACES[game.GameId][game.PlaceId]
	local PlaceMapName = ClientImmutable.Servers.PLACE_INFO[PlaceName].Map
	
	if RoundService.MapFolder then
		RoundService.MapFolder:Destroy()
	end
	
	RoundService.MapName = PlaceMapName
	RoundService.MapFolder = RoundService._LoadMap(PlaceMapName)

	for _, v in Players:GetPlayers() do
		v:LoadCharacter()
		ClassExtension.Player.DisableMovement(v, true)
	end
	
	table.clear(RoundService.Votes)
	RoundService.ReplayVotes = 0
	
	RoundService.Tips = 0
	
	RoundService.RoundID = HttpService:GenerateGUID(false)
	RoundService.IsRoundActive = true
	RoundService.RoundStart = workspace:GetServerTimeNow() + 5
	
	RoundService.ComboMult = 1
	RoundService.IncrementScore(-RoundService.Score)
	
	task.delay(4.8, function()
		local DataService = Server.GetService('DataService')
		
		for _, v in Players:GetPlayers() do
			ClassExtension.Player.DisableMovement(v, false)
			
			task.spawn(function()
				local TutorialStatus = DataService.Get(v, 'TutorialStatus')
				if not TutorialStatus[PlaceMapName].TutorialShownCount
					or TutorialStatus[PlaceMapName].TutorialShownCount <= 0
					or not TutorialStatus[PlaceMapName].CompletedAnOrder then
					if not TutorialStatus[PlaceMapName].TutorialShownCount then
						TutorialStatus[PlaceMapName].TutorialShownCount = 0
					end
					TutorialStatus[PlaceMapName].TutorialShownCount += 1
					Packets.Round.ShowTutorial.sendTo(nil, v)
				end
			end)
		end
	end)
	
	Packets.Round.Start.sendToAll()
	
	for _, v in Players:GetPlayers() do
		RoundService.SendRoundData(v)
	end
	
	RoundService.RoundStartedSignal:Fire()
end


function RoundService.EndRound(timerRanOut: boolean?, isWin: boolean?)
	if not RoundService.IsRoundActive then return end
	
	local DataService = Server.GetService('DataService')
	local OrderService = Server.GetService('OrderService')
	local CurrencyService = Server.GetService('CurrencyService')
	local BattlepassService = Server.GetService('BattlepassService')
	local CharacterService = Server.GetService('CharacterService')
	
	print('ROUND ENDED')
	
	RoundService.IsRoundActive = false
	
	local IsWin = if isWin then true else not not timerRanOut

	RoundService.RoundEndedSignal:Fire(IsWin)
	
	RoundService.PlayersEndedWith = #Players:GetPlayers()
	
	local IsWin = not not timerRanOut
	
	if IsWin then
		task.spawn(giveRoundRewards)
	end
	
	Packets.Round.RoundEnded.sendToAll({
		Act = RoundService.Act,
		Difficulty = RoundService.Difficulty,
		MapName = RoundService.MapName,
		OrdersDelivered = OrderService.OrdersCompleted,
		OrdersFailed = OrderService.OrdersFailed,
		Tips = RoundService.Tips,
		Win = IsWin,
		Elapsed = RoundService.Elapsed(),
		Score = RoundService.Score,
		PlayersEndedWith = #Players:GetPlayers()
	})
	
	for _, v in CollectionService:GetTagged(ClientImmutable.Tags.THROWN_ITEMS) do
		v:Destroy()
	end
	
	for _, v in Players:GetPlayers() do
		local profile = DataService.GetProfile(v)
		
		if IsWin then
			BattlepassService.AddXP(v, 50)
			DataService.Increment(v, 'Wins', 1)
		else
			DataService.Increment(v, 'Losses', 1)
		end
		
		DataService.Increment(v, 'RoundsPlayed', 1)
		
		CurrencyService.AddCoins(v, RoundService.Score, Enum.AnalyticsEconomyTransactionType.Gameplay)
		
		local PlayerCharacters = DataService.Get(v, 'Characters')
		if PlayerCharacters[v:GetAttribute'CharacterID'] then
			Packets.Round.RoundXPGain.sendTo({
				Level = PlayerCharacters[v:GetAttribute'CharacterID'].Level,
				XP = PlayerCharacters[v:GetAttribute'CharacterID'].XP,
				XPGained = CharacterService.SessionXPGains[v] or 0
			}, v)
		end
	end
end


function RoundService.PlayerVoted(plr: Player, vote: 'Replay' | 'Return')
	if RoundService.Votes[plr] then return end
	
	RoundService.Votes[plr] = true
	
	if vote == 'Replay' then
		RoundService.ReplayVotes += 1
		
		Packets.Round.VoteReplay.sendToAll(RoundService.ReplayVotes)
		
		if RoundService.ReplayVotes >= RoundService.PlayersEndedWith then
			startGame()
		end
	else
		Packets.Round.VoteReturn.sendToAll(RoundService.ReturnVotes)
		
		local lobbyPlaceId
		for placeId, placeName in ClientImmutable.Servers.PLACES[game.GameId] do
			if placeName == 'Lobby' then
				lobbyPlaceId = placeId
				break
			end
		end
		
		TeleportService:Teleport(lobbyPlaceId, plr, nil, ReplicatedStorage.Assets.Visuals.TeleportUI:Clone())
	end
end


function RoundService._PlayerAdded(plr: Player)
	local joinData = plr:GetJoinData()
	if not joinData or not joinData.TeleportData then return end
	
	RoundService.Act = joinData.TeleportData.Act or RoundService.Act
	RoundService.Difficulty = joinData.TeleportData.Difficulty or RoundService.Difficulty
	
	RoundService.IsTournament = joinData.TeleportData.Tournament or false
	
	if ClientImmutable.SERVER_TYPE == 'Round' then
		Server.GetService'QuestService'.UpdateQuestProgress(plr, 'PlayRound', 1)
		Server.GetService'AbilityService'.UpdatePassives(plr)
	end
end


function RoundService._Start()
	local CharacterService = Server.GetService('CharacterService')
	local LoadingService = Server.GetService('LoadingService')
	
	if ClientImmutable.SERVER_TYPE ~= 'Round' then return end
	
	Packets.Round.GetRoundInfo.listen(function(_, plr: Player)RoundService.SendRoundData(plr)end)
	Packets.Round.ScoreUpdated.listen(function(_, plr: Player)Packets.Round.ScoreUpdated.sendTo(RoundService.Score, plr)end)
	
	if LoadingService.Loaded == false then
		LoadingService.FinishedLoading:Wait()
		
		task.wait(11)
	else
		task.wait(5)
	end
	
	task.defer(startGame)
	task.defer(handleScore)
	
	Packets.Round.VoteReplay.listen(function(_, plr: Player?)
		RoundService.PlayerVoted(plr, 'Replay')
	end)
	
	Packets.Round.VoteReturn.listen(function(_, plr: Player)
		RoundService.PlayerVoted(plr, 'Return')
	end)
end


function RoundService._Init()
	local CollisionService = Server.GetService('CollisionService')
	
	for _, v: BasePart in ServerStorage.Assets.Maps:GetChildren() do
		if v:FindFirstChild'Collision_Parts' then
			CollisionService.SetCollisionGroup(v.Collision_Parts, ClientImmutable.CollisionGroups.MAP_WALLS)
			CollectionService:AddTag(v, ClientImmutable.Tags.MAP_WALLS)
		end
	end
	
	for _, v: BasePart in ServerStorage.Assets.Maps:GetChildren() do
		if v:FindFirstChild'MapSurface' then
			for _, obj: BasePart in v.MapSurface:GetChildren() do
				obj.CanCollide = false
				obj.CanQuery = true
			end
		end
	end
	
	if not RunService:IsStudio() then
		local PlaceName = ClientImmutable.Servers.PLACES[game.GameId][game.PlaceId]
		local PlaceMapName = ClientImmutable.Servers.PLACE_INFO[PlaceName].Map

		for _, v in ServerStorage.Assets.Maps:GetChildren() do
			if v.Name ~= PlaceMapName then
				v:Destroy()
			else
				for _, mapInstance in v:GetDescendants() do
					if mapInstance:IsA'BasePart' then
						mapInstance.CanTouch = false
					end
				end
			end
		end
	end
end


return RoundService