local ReplicatedStorage = game:GetService('ReplicatedStorage')
local TweenService = game:GetService('TweenService')
local ServerScriptService = game:GetService('ServerScriptService')
local CollectionService = game:GetService('CollectionService')
local Players = game:GetService('Players')
local SoundService = game:GetService('SoundService')

local Client = require(ReplicatedStorage.Client)
local Server = require(ServerScriptService.Server)

local ClientImmutable = Client.Immutable
local Packets = require(ReplicatedStorage.Data.Packets)
local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local Signal = require(ReplicatedStorage.SharedModules.Signal)
local Ingredients = require(ReplicatedStorage.Data.Ingredients)
local Recipes = require(ReplicatedStorage.Data.Recipes)
local TableUtil = require(ReplicatedStorage.SharedModules.TableUtil)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local Math = require(ReplicatedStorage.SharedModules.Math)

local CookingService = {
	PickableItems = {},
	PickupJanitors = {} :: { Janitor },

	NearestInteraction = {},

	KitchenwareModules = {},

	ItemPlaced = Signal.new(),
	ItemPickedUp = Signal.new(),

	ItemThrown = Signal.new(),
	FireExtinguished = Signal.new(),
	FireStarted = Signal.new(),

	DishWashed = Signal.new(),
	FinishedChopping = Signal.new(),
	FinishedCooking = Signal.new(),
	PlateRespawned = Signal.new()
}


function CookingService.GetItemOnInteractable(interactable: BasePart | Model): BasePart | Model
	local itemsInInteractable = TableUtil.Filter(interactable:GetChildren(), function(v): boolean
		return v:IsA'BasePart' or v:IsA'Model'
	end)

	-- Plates/surfaces are priority
	for _, v in ClientImmutable.FOOD_SURFACES do
		if interactable:FindFirstChild(v) then
			return interactable[v]
		end
	end

	for _, v in itemsInInteractable do
		if CookingService.IsIngredient(v) then
			return v
		end
	end

	if interactable:FindFirstChild'FireExtinguisher' then
		return interactable.FireExtinguisher
	end
end


function CookingService.GetIngredientOnInteractable(interactable: BasePart | Model): BasePart | Model
	if not interactable then return nil end

	local itemsInInteractable = TableUtil.Filter(interactable:GetDescendants(), function(v): boolean
		return v:IsA'BasePart' or v:IsA'Model'
	end)

	for _, v in itemsInInteractable do
		if CookingService.IsIngredient(v) then
			return v
		end
	end
end


function CookingService.GetAllIngredientsOnInteractable(interactable: BasePart): {string}
	local ingredients = {}

	local itemsInInteractable = TableUtil.Filter(interactable:GetDescendants(), function(v): boolean
		return v:IsA'BasePart' or v:IsA'Model'
	end)

	for _, v in itemsInInteractable do
		if CookingService.IsIngredient(v) then
			table.insert(ingredients, v.Name)
		end
	end

	return ingredients
end


function CookingService.IsIngredient(item: BasePart): boolean
	return ReplicatedStorage.Assets.Ingredients:FindFirstChild(item.Name) ~= nil
end


function CookingService.IsRawIngredient(ingredient: BasePart): boolean
	if #Ingredients[ingredient.Name].PrepSteps == 0 then return true end
	return CookingService.GetNextIngredientStep(ingredient) == Ingredients[ingredient.Name].PrepSteps[1].Step
end


function CookingService.IsInteractableKitchenware(interactable: BasePart): string?
	for _, obj in interactable:GetDescendants() do
		for _, tag in ClientImmutable.KITCHENWARES do
			if obj:HasTag(tag) then
				return tag
			end
		end
	end

	for _, tag in ClientImmutable.KITCHENWARES do
		if interactable:HasTag(tag) then
			return tag
		end
	end
end


function CookingService.CanPlaceTogetherOnPlate(item: BasePart, targetInteractable: BasePart)
	local OrderService = Server.GetService('OrderService')

	local plateIngredients = {}

	for _, v in targetInteractable:GetChildren() do
		if CookingService.IsIngredient(v) then
			table.insert(plateIngredients, v.Name)
		end
	end

	if CookingService.IsIngredient(item) then
		table.insert(plateIngredients, item.Name)
	elseif CookingService.IsItemFoodSurface(item) then
		for _, v in item:GetChildren() do
			if CookingService.IsIngredient(v) then
				table.insert(plateIngredients, v.Name)
			end
		end
	end

	if #plateIngredients == 1 then
		return true
	end

	for k, v in Recipes do
		local foundMatchingIngredients = 0
		for _, ingr in v.Ingredients do
			if table.find(plateIngredients, ingr) then
				foundMatchingIngredients += 1
			end
		end

		if foundMatchingIngredients == #plateIngredients then
			return true
		end
	end

	return OrderService.GetFoodFromIngredients(plateIngredients) ~= nil
end


function CookingService.CanPlaceOnPlateType(item: BasePart, plate: BasePart): boolean
	local plateType = plate:GetAttribute'PlaceSurfaceType' or 'Plate'

	local ingredientInfo = Ingredients[item.Name]
	if not ingredientInfo then return false end

	return ingredientInfo.PlaceSurfaceType == plateType
end


function CookingService.CanItemBePlacedOnInteractable(item: BasePart | Model, targetInteractable: BasePart): boolean
	if not item:IsDescendantOf(workspace) or not targetInteractable:IsDescendantOf(workspace) then return false end

	if CookingService.IsInteractableOnFire(targetInteractable) then return false end

	local ItemOnInteractable = CookingService.GetItemOnInteractable(targetInteractable)

	if targetInteractable:HasTag(ClientImmutable.Tags.SINK) or targetInteractable:HasTag(ClientImmutable.Tags.GARBAGE) then
		return false
	end

	local kitchenware = CookingService.IsInteractableKitchenware(targetInteractable)

	if CookingService.IsIngredient(item) and kitchenware then
		return CookingService.GetNextIngredientStep(item) == kitchenware
			and not CookingService.GetIngredientOnInteractable(targetInteractable)
	end

	if item.Name == 'Plate' or item.Name == 'Bowl' or item.Name == 'Mug' then
		if targetInteractable:HasTag(ClientImmutable.Tags.DISHES) then return true end
		if kitchenware then return false end
		if CookingService.IsDirty(item) then return false end
		if ItemOnInteractable and CookingService.IsIngredient(ItemOnInteractable) then
			if item:FindFirstChild(ItemOnInteractable.Name) or ItemOnInteractable:GetAttribute'Burned' then return false end
			return CookingService.GetNextIngredientStep(ItemOnInteractable) == nil
				and CookingService.CanPlaceTogetherOnPlate(item, targetInteractable)
				and CookingService.CanPlaceOnPlateType(ItemOnInteractable, item)
		end
	end

	if targetInteractable:FindFirstChild(item.Name) then return false end -- no duplicates
	if item:GetAttribute'Burned' then return false end

	local heldFoodSurface = CookingService.GetFoodSurfaceObject(item)

	if ItemOnInteractable and (ItemOnInteractable.Name == 'Plate' or ItemOnInteractable.Name == 'Bowl' or ItemOnInteractable.Name == 'Mug') then
		if CookingService.IsDirty(ItemOnInteractable) then
			return false
		elseif CookingService.IsIngredient(item) then
			return CookingService.GetNextIngredientStep(item) == nil
				and CookingService.CanPlaceTogetherOnPlate(item, targetInteractable)
				and CookingService.CanPlaceOnPlateType(item, ItemOnInteractable)
		elseif heldFoodSurface then
			local ingredient = CookingService.GetIngredientOnInteractable(heldFoodSurface)
			if ingredient and not CookingService.GetNextIngredientStep(ingredient) then
				return CookingService.CanPlaceTogetherOnPlate(item, targetInteractable)
					and CookingService.CanPlaceOnPlateType(ingredient, ItemOnInteractable)
			end
		end
	end

	local foodSurface = CookingService.GetFoodSurfaceObject(targetInteractable)
	if CookingService.IsIngredient(item) and CookingService.GetNextIngredientStep(item) then
		if foodSurface then return true end
		return ItemOnInteractable == nil
	end

	if heldFoodSurface then
		local ingredient = CookingService.GetIngredientOnInteractable(heldFoodSurface)
		if ingredient and kitchenware and CookingService.GetNextIngredientStep(ingredient) == kitchenware and not CookingService.GetIngredientOnInteractable(foodSurface) then
			return true
		end
	end

	return ItemOnInteractable == nil
end


function CookingService.GetInteractableFromItem(item: BasePart): BasePart | Model
	if not item:IsDescendantOf(workspace) then return end

	for _, tag in ClientImmutable.VALID_INTERACTABLES do
		if item.Parent:HasTag(tag) then
			return item.Parent
		elseif item.Parent.Parent:HasTag(tag) then
			return item.Parent.Parent
		end
	end
end


function CookingService.CanPickupItem(plr: Player, item: BasePart): boolean
	if Players:GetPlayerFromCharacter(item.Parent) then return false end

	local interactable = CookingService.GetInteractableFromItem(item)
	if interactable then
		for _, v in ClientImmutable.BURNABLE_INTERACTABLES do
			if interactable:HasTag(v) and CookingService.IsInteractableOnFire(interactable) then return false end
		end
	end

	if CookingService.IsIngredient(item) and item:FindFirstChild('Fire', true) then return false end

	return true
end


function CookingService.GetFoodSurfaceObject(interactable: BasePart)
	for _, v in ClientImmutable.FOOD_SURFACES do
		if interactable:FindFirstChild(v) then
			return interactable[v]
		elseif interactable.Name == v then
			return interactable
		end
	end
end


function CookingService.IsSubmittableFoodSurface(item: BasePart)
	return table.find(ClientImmutable.SUBMITTABLE_FOOD_SURFACES, item.Name) ~= nil
end


function CookingService.IsItemFoodSurface(item: BasePart)
	return table.find(ClientImmutable.FOOD_SURFACES, item.Name) ~= nil
end


function CookingService.CanPlaceFoodSurfaceObject(item: BasePart, targetInteractable: BasePart): boolean
	if (item.Name == 'Plate' or item.Name == 'Mug' or item.Name == 'Bowl') and targetInteractable:HasTag(ClientImmutable.Tags.COUNTERS) then
		return true
	end

	if targetInteractable:HasTag(ClientImmutable.Tags.COUNTERS) then return false end

	if targetInteractable:HasTag(ClientImmutable.Tags.STOVE) and item.Name ~= 'Plate' then
		return CookingService.GetFoodSurfaceObject(targetInteractable) == nil
	end

	return false
end


function CookingService.ReplaceIngredientModel(original: BasePart, newIngredient: BasePart): BasePart
	newIngredient.Name = original.Name
	newIngredient.Anchored = original.Anchored
	newIngredient:PivotTo(original:GetPivot())
	newIngredient.Parent = original.Parent

	for _, v in original:GetChildren() do
		v.Parent = newIngredient
	end

	for k, v in original:GetAttributes() do
		newIngredient:SetAttribute(k, v)
	end

	original:Destroy()

	return newIngredient
end


function CookingService.PlaceItem(item: BasePart | Model, targetInteractable: BasePart, plr: Player?, passPrimaryValidation: true?): boolean
	if not passPrimaryValidation and not CookingService.CanItemBePlacedOnInteractable(item, targetInteractable) then return false end

	if plr and CookingService.PickupJanitors[plr] then
		CookingService.PickupJanitors[plr]:Cleanup()
	end

	CollectionService:RemoveTag(item, ClientImmutable.Tags.THROWN_ITEMS)

	if not (item.Name == 'Plate' or item.Name == 'Mug' or item.Name == 'Bowl') and CookingService.IsItemFoodSurface(item) and not CookingService.CanPlaceFoodSurfaceObject(item, targetInteractable) then
		for _, ingredient in item:GetChildren() do
			if CookingService.IsIngredient(ingredient) then
				CookingService.PlaceItem(ingredient, targetInteractable, plr, true)
			end
		end

		CookingService.PickupItem(plr, item, true)
	else
		local raycastOrigin = targetInteractable.Position

		local rp = RaycastParams.new() do
			rp.FilterType = Enum.RaycastFilterType.Include
			rp.CollisionGroup = 'Default'
			rp.FilterDescendantsInstances = {targetInteractable}
		end

		local foodSurface = CookingService.GetFoodSurfaceObject(targetInteractable)
		if foodSurface then
			rp.FilterDescendantsInstances = {foodSurface}

			local offset = foodSurface:GetAttribute'OriginalOffset'
			if offset and foodSurface.Name == 'FryingPan' then
				raycastOrigin -= Vector3.new(offset.X, 0, offset.Z)
			else
				raycastOrigin = foodSurface.Position - Vector3.new(0, foodSurface.Size.Y/2, 0)
			end
		end

		if item:IsA'BasePart' then
			item.Anchored = true
		end

		item.Parent = workspace
		local res = workspace:Raycast(raycastOrigin + Vector3.new(0, (targetInteractable.Size.Y/2) + 2.25, 0), Vector3.new(0, -50, 0), rp)
		item.Parent = targetInteractable

		if item:GetAttribute('OriginalOffset') then
			item:PivotTo(targetInteractable:GetPivot() * item:GetAttribute('OriginalOffset'))
		elseif res then
			if not item:GetAttribute'OriginalRotation' then
				item:SetAttribute('OriginalRotation', item:GetPivot().Rotation)
			end

			item:PivotTo(item:GetAttribute'OriginalRotation')

			local szy = Math.GetWorldHeightY(item)
			local final = (CFrame.new(res.Position + Vector3.new(0, szy/2, 0)) * item:GetAttribute('OriginalRotation'))
			item:PivotTo(final)
		else
			-- TODO
			print('wtf??')
		end

		if plr then
			Animations:PlayTrack(plr, 'PlatePutDown', .1, 2)
		end

		if foodSurface or CookingService.IsItemFoodSurface(item) then
			local ingredientOnInteractable = CookingService.GetIngredientOnInteractable(targetInteractable)
			if ingredientOnInteractable and not passPrimaryValidation then
				CookingService.PlaceItem(ingredientOnInteractable, targetInteractable, plr, true)
			end

			for _, ingredient in item:GetChildren() do
				if CookingService.IsIngredient(ingredient) then
					CookingService.PlaceItem(ingredient, targetInteractable, plr, true)
				end
			end
		end
	end

	SoundService.SFX.ItemPlaced:Play()
	CookingService.ItemPlaced:Fire(item, targetInteractable, plr)

	return true
end


function CookingService.PickupItem(plr: Player, item: BasePart | Model, rawPickup: boolean?): boolean
	if not rawPickup and not CookingService.CanPickupItem(plr, item) then return false end

	CookingService.PickableItems[item.Name] = true

	local PlayerPickupJanitor = CookingService.PickupJanitors[plr] or Janitor.new()
	CookingService.PickupJanitors[plr] = PlayerPickupJanitor

	PlayerPickupJanitor:Cleanup()

	if not item:GetAttribute'OriginalRotation' then
		item:SetAttribute('OriginalRotation', item:GetPivot().Rotation)
	end

	if not rawPickup and table.find(ClientImmutable.FOOD_SURFACES, item.Name) then
		for _, ingredient in item.Parent:GetChildren() do
			if CookingService.IsIngredient(ingredient) then
				if item:FindFirstChild('Fire', true) then PlayerPickupJanitor:Cleanup() return false end

				ingredient.Parent = item

				ingredient.Anchored = false
				ingredient.Massless = true

				PlayerPickupJanitor:Add(function()
					ingredient.Anchored = true
				end)

				local Weld = PlayerPickupJanitor:Add(Instance.new('WeldConstraint'))
				Weld.Name = 'PlateWeld'
				Weld.Parent = item

				Weld.Part0 = item
				Weld.Part1 = ingredient
			end
		end
	end

	Server.GetService'CollisionService'.SetCollisionGroup(item, ClientImmutable.CollisionGroups.HOLDABLE_ITEMS)
	CollectionService:RemoveTag(item, ClientImmutable.Tags.THROWN_ITEMS)

	do
		local AnimationObjectFolder = ReplicatedStorage.Assets.AnimationObjects:FindFirstChild(item.Name)
			and ReplicatedStorage.Assets.AnimationObjects[item.Name] or ReplicatedStorage.Assets.AnimationObjects.Default

		local targetLimbName: string = AnimationObjectFolder.Limb.Value

		for _, v in item:GetDescendants() do
			if v:IsA'BasePart' then
				v.Massless = true
				v.Anchored = false
			end
		end

		if item:IsA'BasePart' then
			item.Massless = true
			item.Anchored = false
		end

		item.Parent = plr.Character

		local motor = PlayerPickupJanitor:Add(AnimationObjectFolder:FindFirstChildOfClass('Motor6D'):Clone())
		motor.Parent = plr.Character[targetLimbName]

		motor.Part0 = plr.Character[targetLimbName]
		motor.Part1 = item:FindFirstChild'Primary' or item

		PlayerPickupJanitor:Add(function()
			for _, v in item:GetDescendants() do
				if v:IsA'BasePart' then
					v.Anchored = true
				end
			end

			if item:IsA'BasePart' then
				item.Anchored = true
			end
		end)
	end

	do
		Animations:StopTrack(plr, 'PlatePutDown')

		local platePickupTrack: AnimationTrack = Animations:PlayTrack(plr, 'PlatePickup')

		PlayerPickupJanitor:Add(platePickupTrack:GetMarkerReachedSignal'Hold':Connect(function()
			Animations:PlayTrack(plr, 'PlateHold')
		end))

		PlayerPickupJanitor:Add(function()
			Animations:StopTrack(plr, 'PlatePickup')
			Animations:StopTrack(plr, 'PlateHold')
		end)
	end

	PlayerPickupJanitor:Add(item.AncestryChanged:Connect(function(_, parent: Instance?)
		if parent == nil then
			PlayerPickupJanitor:Cleanup()
		end
	end))

	CookingService.ItemPickedUp:Fire(item, plr)

	return true
end


function CookingService.GetHeldItem(plr: Player): (BasePart | Model)?
	if plr.Character then
		for k in CookingService.PickableItems do
			if plr.Character:FindFirstChild(k) then
				return plr.Character[k]
			end
		end
	end
end


function CookingService.GetNextIngredientStep(ingredient: BasePart): string?
	local ingredientSteps = Ingredients[ingredient.Name].PrepSteps

	for stepNum, v in ingredientSteps do
		if not CookingService.KitchenwareModules[v.Step].IsActionComplete(ingredient, stepNum) then
			return v.Step, stepNum
		end
	end
end


function CookingService.GetCurrentIngredientStep(ingredient: BasePart): string?
	local ingredientSteps = Ingredients[ingredient.Name].PrepSteps

	for stepNum, v in ingredientSteps do
		if not CookingService.KitchenwareModules[v.Step].IsActionComplete(ingredient, stepNum) then
			local previousStep = ingredientSteps[stepNum - 1]
			if previousStep then
				return previousStep.Step, stepNum - 1
			end
		end
	end
end


function CookingService.IsDirty(item: BasePart)
	return item:FindFirstChild'Dirty' ~= nil
end


function CookingService.RespawnPlate(dirty: boolean?, plateType: string?)
	if not Server.GetService'RoundService'.IsRoundActive then return end

	local PlateToRespawn = ReplicatedStorage.Assets.Props[plateType or 'Plate']:Clone()
	PlateToRespawn.Parent = workspace

	CookingService.PlateRespawned:Fire()

	if dirty then
		local Dirt = Instance.new('Decal')
		Dirt.Name = 'Dirty'
		Dirt.Face = Enum.NormalId.Top
		Dirt.Texture = ClientImmutable.DIRTY_DISH_TEXTURES[math.random(1, #ClientImmutable.DIRTY_DISH_TEXTURES)]
		Dirt.Parent = PlateToRespawn
	end

	for _, v in CollectionService:GetTagged(ClientImmutable.Tags.DISHES) do
		if v:IsDescendantOf(workspace) then
			CookingService.PlaceItem(PlateToRespawn, v)
			return
		end
	end

	for _, interactable in CollectionService:GetTagged(Client.Immutable.Tags.COUNTERS) do
		if CookingService.CanItemBePlacedOnInteractable(PlateToRespawn, interactable) then
			CookingService.PlaceItem(PlateToRespawn, interactable)
			return
		end
	end

	PlateToRespawn:Destroy()
end


function CookingService.Interacted(interactable: BasePart, plr: Player): boolean
	if not Server.GetService'RoundService'.IsRoundActive then return false end

	local heldItem = CookingService.GetHeldItem(plr)

	local isKitchenware = CookingService.IsInteractableKitchenware(interactable)

	if interactable:HasTag(ClientImmutable.Tags.COUNTERS) and not isKitchenware then
		if heldItem then
			CookingService.PlaceItem(heldItem, interactable, plr)
		else
			local pickableItem = CookingService.GetItemOnInteractable(interactable)
			if pickableItem then
				CookingService.PickupItem(plr, pickableItem)
			end
		end
	elseif interactable:HasTag(ClientImmutable.Tags.DISHES) then
		if heldItem then return false end

		local pickableItem = CookingService.GetItemOnInteractable(interactable)
		if pickableItem then
			CookingService.PickupItem(plr, pickableItem)
		end
	elseif interactable:HasTag(ClientImmutable.Tags.INGREDIENT_BOX) then
		if heldItem then return false end

		local thisIngredient: string = interactable.Parent:GetAttribute('Ingredient')
		assert(ReplicatedStorage.Assets.Ingredients:FindFirstChild(thisIngredient) ~= nil, `Ingredient missing: {thisIngredient}`)
		CookingService.PickupItem(plr, ReplicatedStorage.Assets.Ingredients[thisIngredient]:Clone())

		do
			local Hinge: BasePart = interactable.Parent.Hinge

			local OpenTween: Tween = TweenService:Create(Hinge,
				TweenInfo.new(.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
				{CFrame = interactable.Parent.Bottom.OpenCF.WorldCFrame}
			)

			OpenTween:Play()
			OpenTween.Completed:Once(function()
				Hinge.CFrame = interactable.Parent.Bottom.OpenCF.WorldCFrame

				local t = TweenService:Create(Hinge,
					TweenInfo.new(.5, Enum.EasingStyle.Bounce, Enum.EasingDirection.Out),
					{CFrame = interactable.Parent.Bottom.CloseCF.WorldCFrame}
				);t:Play();
				t.Completed:Connect(function(playbackState: Enum.PlaybackState)
					if playbackState == Enum.PlaybackState.Completed then
						Hinge.CFrame = interactable.Parent.Bottom.CloseCF.WorldCFrame
					end
				end)
			end)
		end
	elseif interactable:HasTag(ClientImmutable.Tags.GARBAGE) and heldItem then
		local function trashItem(item): boolean
			if item:FindFirstChild'Fire' then return false end
			item:Destroy()
			SoundService.SFX.Trash:Play()
			return true
		end

		local isThrowableFoodSurface  = heldItem.Name == 'Plate' or heldItem.Name == 'Mug' or heldItem.Name == 'Bowl'
		if (isThrowableFoodSurface and not CookingService.IsDirty(heldItem)) or CookingService.IsIngredient(heldItem) then
			if isThrowableFoodSurface then
				CookingService.RespawnPlate(false, heldItem:GetAttribute'PlaceSurfaceType')
			end

			trashItem(heldItem)
		else
			local foodSurface = CookingService.GetFoodSurfaceObject(heldItem)
			if foodSurface then
				for _, v in foodSurface:GetChildren() do
					if CookingService.IsIngredient(v) then
						local success = trashItem(v)
						if success then
							CookingService.PickupItem(plr, heldItem, true)
						end
					end
				end
			end
		end
	elseif interactable:HasTag(ClientImmutable.Tags.SUBMIT_ORDER) and heldItem
		and (heldItem.Name == 'Plate' or heldItem.Name == 'Mug' or heldItem.Name == 'Bowl') then
		local plateIngredients = {}

		for _, v in heldItem:GetChildren() do
			if CookingService.IsIngredient(v) then
				table.insert(plateIngredients, v.Name)
			end
		end

		if Server.GetService'OrderService'.SubmitOrder(plateIngredients, plr) then
			local placeSurfaceType = heldItem:GetAttribute'PlaceSurfaceType'
			heldItem:Destroy()

			CookingService.RespawnPlate(true, placeSurfaceType)
		end
	elseif interactable:HasTag(ClientImmutable.Tags.THROWN_ITEMS) and not heldItem then
		CookingService.PickupItem(plr, interactable, true)
	elseif interactable:HasTag(ClientImmutable.Tags.FIRE_EXTINGUISHER) and not heldItem then
		CookingService.PickupItem(plr, interactable)
	end

	if isKitchenware then
		local ingredientOnInteractable = CookingService.GetIngredientOnInteractable(interactable)
		if ingredientOnInteractable then
			CookingService.KitchenwareModules[isKitchenware].Interact(plr, interactable, ingredientOnInteractable)
		elseif heldItem and CookingService.IsIngredient(heldItem) then
			local nextStep = CookingService.GetNextIngredientStep(heldItem)

			if nextStep == isKitchenware then
				CookingService.KitchenwareModules[isKitchenware].Interact(plr, interactable, heldItem)
			end
		elseif CookingService.KitchenwareModules[isKitchenware].CanInteract(plr, interactable) then
			CookingService.KitchenwareModules[isKitchenware].Interact(plr, interactable, heldItem)
		end
	end

	return true
end


do -- fire stuff
	local FlammableObjects

	local PLANE_EDGES = { Vector3.new(1, 0, 0), Vector3.new(-1, 0, 0), Vector3.new(0, 0, 1), Vector3.new(0, 0, -1), Vector3.new(1, 0, 1), Vector3.new(-1, 0, 1), Vector3.new(1, 0, -1), Vector3.new(-1, 0, -1) }
	local GRACE_DISTANCING = 1.7
	local function getAdjacentFlammableInteractables(interactable: BasePart): {BasePart}
		local res = {}

		for _, v in FlammableObjects do
			if v == interactable or not v:IsDescendantOf(workspace) then continue end

			for _, side in PLANE_EDGES do
				local sidePos = v.Position + (side * Vector3.new(v.Size.X/2, 0, v.Size.Z/2))
				local dist = (sidePos - interactable.Position).Magnitude
				if dist - GRACE_DISTANCING <= math.min(interactable.Size.X, interactable.Size.Z)/2 then
					table.insert(res, v)
					break
				end
			end
		end

		return res
	end


	function CookingService.SetInteractableOnFire(interactable: BasePart)
		if not CookingService.IsInteractableOnFire(interactable) then
			local LastExtinguish = interactable:GetAttribute('LastExtinguish')
			if LastExtinguish and os.clock() - LastExtinguish < Server.Immutable.BURN_AGAIN_AFTER_X_SEC then
				return
			end

			local fireAtt = ReplicatedStorage.Assets.Visuals.Fire.Fire:Clone()
			fireAtt.Position = Vector3.new(0, interactable.Size.Y/2, 0)
			fireAtt.Parent = interactable

			local FireProgressBar = ReplicatedStorage.Assets.Templates.ProgressBar:Clone()
			FireProgressBar.Bar.Progress.ImageColor3 = Color3.fromRGB(255, 0, 0)
			FireProgressBar.Bar.Progress.UIGradient.Transparency = NumberSequence.new(0)
			FireProgressBar.Enabled = false
			FireProgressBar.Parent = fireAtt

			local BurnWarning = ReplicatedStorage.Assets.Templates.BurnWarning:Clone()
			BurnWarning.Parent = interactable

			CookingService.FireStarted:Fire(interactable)

			task.spawn(function()
				while BurnWarning:IsDescendantOf(interactable) do
					BurnWarning.Enabled = true
					task.wait(.2)
					if BurnWarning:IsDescendantOf(interactable) then
						BurnWarning.Enabled = false
						task.wait(.2)
					end
				end
			end)
		end
	end


	function CookingService.SpreadFire()
		if not FlammableObjects then
			FlammableObjects = {}

			for _, tag in ClientImmutable.BURNABLE_INTERACTABLES do
				for _, v in CollectionService:GetTagged(tag) do
					table.insert(FlammableObjects, v)
				end
			end
		end

		local AbilityService = Server.GetService('AbilityService')

		while true do
			task.wait(Server.Immutable.FIRE_SPREAD_TIME/3)

			local currentlyBurning = {}
			for _, interactable in FlammableObjects do
				if CookingService.IsInteractableOnFire(interactable) then
					table.insert(currentlyBurning, interactable)
				end
			end

			for _, interactable in currentlyBurning do
				for _, v in getAdjacentFlammableInteractables(interactable) do
					local spreadTime = AbilityService.GetPassiveValue(v.Position, 'FireSpreadTime')
					task.delay(spreadTime, function()
						if not CookingService.IsInteractableOnFire(interactable) then return end

						local newSpreadTime = AbilityService.GetPassiveValue(v.Position, 'FireSpreadTime')
						local diff = math.max(newSpreadTime, spreadTime) - math.min(newSpreadTime, spreadTime)
						task.delay(diff, function()
							if not CookingService.IsInteractableOnFire(interactable) then return end

							CookingService.SetInteractableOnFire(v)
						end)
					end)
				end
			end
		end
	end


	function CookingService.IsInteractableOnFire(interactable: BasePart)
		return interactable:FindFirstChild('Fire', true) ~= nil
	end


	function CookingService.Extinguish(interactable: BasePart, plr: Player?)
		if CookingService.IsInteractableOnFire(interactable) then
			interactable:SetAttribute('LastExtinguish', os.clock())

			local BurnWarning = interactable:FindFirstChild('BurnWarning', true)
			if BurnWarning then BurnWarning:Destroy() end
			interactable:FindFirstChild('Fire', true):Destroy()

			CookingService.FireExtinguished:Fire(interactable, plr)
		end
	end
end


function CookingService.GetInteractablesInRegion(origin: Vector3, range: number): {Instance}
	local RegionParams: OverlapParams do
		RegionParams = OverlapParams.new()
		RegionParams.FilterDescendantsInstances = {CollectionService:GetTagged(Client.Immutable.Tags.COUNTERS)}
		RegionParams.FilterType = Enum.RaycastFilterType.Include
	end

	return workspace:GetPartBoundsInRadius(origin, range, RegionParams)
end


local IngredientTemplate = ReplicatedStorage.Assets.Templates.SurfaceIngredientDesc.Frame.Ingredient
function CookingService.UpdateSurfaceIngredientDescription(item: BasePart, interactable: BasePart?)
	if CookingService.IsItemFoodSurface(item) and item.Name ~= 'Plate' then return end

	local surface
	if interactable then
		local thisSurface = CookingService.GetFoodSurfaceObject(interactable)
		if thisSurface and (thisSurface.Name == 'Plate' or thisSurface.Name == 'Mug' or thisSurface.Name == 'Bowl') then surface = thisSurface end
		item = surface or item
	end

	if not (CookingService.IsItemFoodSurface(item) or CookingService.IsIngredient(item)) then return end

	local SurfaceIngredientDesc = item:FindFirstChild('SurfaceIngredientDesc')
	if not SurfaceIngredientDesc then
		SurfaceIngredientDesc = ReplicatedStorage.Assets.Templates.SurfaceIngredientDesc:Clone()
		SurfaceIngredientDesc.Parent = item
	end

	for _, v in SurfaceIngredientDesc.Frame:GetChildren() do
		if v:IsA'GuiObject' then v:Destroy() end
	end

	for _, v in TableUtil.Extend(interactable and item.Parent:GetChildren() or {}, item:GetChildren(), {item}) do
		if CookingService.IsIngredient(v) then
			if surface and v:FindFirstChild'SurfaceIngredientDesc' then
				v.SurfaceIngredientDesc:Destroy()
			end

			if SurfaceIngredientDesc.Frame:FindFirstChild(v.Name) then continue end

			local ThisIngredient = IngredientTemplate:Clone()
			ThisIngredient.Name = v.Name
			ThisIngredient.Image = Ingredients[v.Name].Image
			ThisIngredient.LayoutOrder = Ingredients[v.Name].Order
			ThisIngredient.Parent = SurfaceIngredientDesc.Frame

			if Ingredients[v.Name] and Ingredients[v.Name].StepImages then
				local _, stepIdx = CookingService.GetNextIngredientStep(v)
				local stepImages = Ingredients[v.Name].StepImages
				ThisIngredient.Image = stepIdx and stepImages[stepIdx] or stepImages[#stepImages]
			end
		end
	end
end


function CookingService._PlayerRemoving(plr: Player)
	if CookingService.PickupJanitors[plr] then
		CookingService.PickupJanitors[plr]:Destroy()
		CookingService.PickupJanitors[plr] = nil
	end
end


function CookingService._Start()
	Packets.Interaction.Interact.listen(CookingService.Interacted)

	Packets.Interaction.CancelInteraction.listen(function(_, plr: Player)
		for k, module in CookingService.KitchenwareModules do
			module.StopInteract(plr)
		end
	end)

	local ThrowFood = require(script.ThrowFood)
	Packets.Interaction.ThrowFood.listen(ThrowFood)

	local FireExtinguisherModule = require(script.FireExtinguisher)
	Packets.Interaction.FireExtinguisher.listen(function(enabled: boolean, plr: Player)
		if enabled then FireExtinguisherModule.Enable(plr) else FireExtinguisherModule.Disable(plr) end
	end)

	Packets.Interaction.NearestInteraction.listen(function(data: BasePart?, plr: Player)
		CookingService.NearestInteraction[plr] = data
	end)

	Server.GetService'RoundService'.RoundEndedSignal:Connect(function()
		for _, v in Players:GetPlayers() do
			if CookingService.PickupJanitors[v] then
				CookingService.PickupJanitors[v]:Cleanup()
			end
		end
	end)

	do
		CookingService.ReorganizeRecipeVisual = require(script.ReorganizeRecipeVisual)

		CookingService.ItemPlaced:Connect(function(item: BasePart | Model, interactable: BasePart, plr: Player?)
			CookingService.ReorganizeRecipeVisual(item, interactable)

			if item.Name == 'FireExtinguisher' then
				Packets.HUD.SetActionInfo.sendTo({Text = '', Priority = 5}, plr)
				CollectionService:AddTag(item, ClientImmutable.Tags.FIRE_EXTINGUISHER)
				return
			end

			CookingService.UpdateSurfaceIngredientDescription(item, interactable)
		end)

		CookingService.ItemPickedUp:Connect(function(item: BasePart, plr: Player)
			if item.Name == 'FireExtinguisher' then
				Packets.HUD.SetActionInfo.sendTo({Text = 'HOLD SPACE', Priority = 5}, plr)
				CollectionService:RemoveTag(item, ClientImmutable.Tags.FIRE_EXTINGUISHER)
				return
			end

			CookingService.UpdateSurfaceIngredientDescription(item)
		end)
	end

	task.defer(CookingService.SpreadFire)
end


function CookingService._Init()
	for _, v in script.Kitchenware:GetChildren() do
		CookingService.KitchenwareModules[v.Name] = require(v)

		if CookingService.KitchenwareModules[v.Name]._Init then
			CookingService.KitchenwareModules[v.Name]._Init()
		end
	end
end


return CookingService