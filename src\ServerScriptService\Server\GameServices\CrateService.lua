local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Math = require(ReplicatedStorage.SharedModules.Math)
local Packets = require(ReplicatedStorage.Data.Packets)
local CratesData = require(ReplicatedStorage.Data.Crates)
local Items = require(ReplicatedStorage.Data.Items)

local CrateService = {}


function CrateService.BuyCrate(plr: Player, crateName: string)
	local InventoryService = Server.GetService('InventoryService')
	local DataService = Server.GetService('DataService')
	local BoostService = Server.GetService('BoostService')
	
	local ThisCrateInfo = CratesData[crateName]
	
	local CrateMythicalPity = DataService.Get(plr, 'CrateMythicalPity')
	local CrateLegendaryPity = DataService.Get(plr, 'CrateLegendaryPity')
	
	local itemGot
	
	local input = {}
	for itemName, itemInfo in ThisCrateInfo.Items do
		input[itemName] = itemInfo.Chance
		
		local ItemRarity = Items[itemName].Rarity
		if CrateMythicalPity > 0 and CrateMythicalPity % Client.Immutable.CRATE_MYTHICAL_PITY == 0 and ItemRarity == 'Mythical' then
			print('okk yes')
			itemGot = itemName
			DataService.Set(plr, 'CrateMythicalPity', -1)
			break
		elseif CrateLegendaryPity > 0 and CrateLegendaryPity % Client.Immutable.CRATE_LEGENDARY_PITY == 0 and ItemRarity == 'Legendary' then
			itemGot = itemName
			DataService.Set(plr, 'CrateLegendaryPity', -1)
			break
		end
	end
	
	itemGot = itemGot or Math.RNG(input, BoostService.GetLuckBoost(plr))
	print(itemGot)
	
	local RewardInfo = ThisCrateInfo.Items[itemGot].Reward
	Server.GetService(RewardInfo.Service)[RewardInfo.Function](plr, table.unpack(RewardInfo.Args))
	
	DataService.Increment(plr, 'CrateMythicalPity', 1)
	DataService.Increment(plr, 'CrateLegendaryPity', 1)
	
	if Items[itemGot].Rarity == 'Mythical' then
		DataService.Set(plr, 'CrateMythicalPity', 0)
	elseif Items[itemGot].Rarity == 'Legendary' then
		DataService.Set(plr, 'CrateLegendaryPity', 0)
	end
	
	Packets.Crates.UpdateCratesOpened.sendTo({
		LegendaryPity = DataService.Get(plr, 'CrateLegendaryPity'),
		MythicalPity = DataService.Get(plr, 'CrateMythicalPity')
	}, plr)
end


function CrateService._Start()
	local CurrencyService = Server.GetService('CurrencyService')
	local DataService = Server.GetService('DataService')
	
	Packets.Crates.UpdateCratesOpened.listen(function(_, plr: Player)
		Packets.Crates.UpdateCratesOpened.sendTo({
			LegendaryPity = DataService.Get(plr, 'CrateLegendaryPity'),
			MythicalPity = DataService.Get(plr, 'CrateMythicalPity')
		}, plr)
	end)
	
	Packets.Crates.BuyCrate.listen(function(data: { CrateName: string, TenTimes: boolean }, plr: Player)
		local ThisCrateInfo = CratesData[data.CrateName]
		local price = data.TenTimes and ThisCrateInfo.Open10Price or ThisCrateInfo.Open1Price
		
		if (ThisCrateInfo.Currency == 'Gems' and CurrencyService.GetGems(plr) or CurrencyService.GetCoins(plr)) >= price then
			for i = 1, data.TenTimes and 10 or 1 do
				CrateService.BuyCrate(plr, data.CrateName)
			end
			
			if ThisCrateInfo.Currency == 'Gems' then
				CurrencyService.AddGems(plr, -price, 'Crate')
			else
				CurrencyService.AddCoins(plr, -price, 'Crate')
			end
		end
	end)
end


return CrateService