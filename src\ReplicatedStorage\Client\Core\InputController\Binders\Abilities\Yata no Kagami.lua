local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local RunService = game:GetService('RunService')
local GamepadService = game:GetService('GamepadService')
local UserInputService = game:GetService('UserInputService')

local Client = require(ReplicatedStorage.Client)
local Janitor = require(ReplicatedStorage.SharedModules.Janitor)

local Packets = require(ReplicatedStorage.Data.Packets)
local InputController: typeof(require(script.Parent.Parent.Parent)) = Client.GetController('InputController')

local YatanoKagami = {
	Key = Enum.KeyCode.V,

	GamepadKey = Enum.KeyCode.ButtonR2
}


function YatanoKagami.KeyDown(second: true?)
	if InputController.Preferred == 'Gamepad' and second == nil then
		YatanoKagami.Click()
		return
	end

	YatanoKagami.Cleanup()
	
	if YatanoKagami.IsOnCooldown() or not Ya<PERSON>Kagami.CanUseAbilityAsCharacter() then return end

	local hitRp = RaycastParams.new()
	hitRp.FilterType = Enum.RaycastFilterType.Include
	hitRp.FilterDescendantsInstances = {workspace.Map.MapSurface} --{workspace.Map.Collision_Parts}
	
	local hitRes = InputController.Inputs.Mouse.Hit(hitRp)
	if hitRes then
		Packets.Ability.UseAbility.send({
			AbilityName = script.Name,
			Args = {hitRes.Position}
		})
	end
end


function YatanoKagami.KeyUp()
	
end


local ClickJanitor = Janitor.new()
function YatanoKagami.Click()
	if ClickJanitor:Get('Click') then
		return YatanoKagami.Cleanup()
	end
	
	if YatanoKagami.IsOnCooldown() or not YatanoKagami.CanUseAbilityAsCharacter() then return end
	
	ClickJanitor:Add(Players.LocalPlayer:GetMouse().Button1Down:Connect(function()
		task.wait()
		YatanoKagami.KeyDown()
		YatanoKagami.Cleanup()
	end), nil, 'Click')
	
	GamepadService:EnableGamepadCursor(nil)
	ClickJanitor:Add(UserInputService.InputBegan:Connect(function(input: InputObject, processed: boolean)
		if input.KeyCode == Enum.KeyCode.ButtonA then
			YatanoKagami.KeyDown(true)
			YatanoKagami.Cleanup()
		elseif input.KeyCode == Enum.KeyCode.ButtonB then
			YatanoKagami.Cleanup()
		end
	end))
	
	ClickJanitor:Add(function()
		GamepadService:DisableGamepadCursor()
	end)
end


function YatanoKagami.Cleanup()
	ClickJanitor:Cleanup()
end


function YatanoKagami.IsOnCooldown()
	return Client.GetController'AbilityController'.IsAbilityOnCooldown(script.Name)
end


function YatanoKagami.CanUseAbilityAsCharacter()
	return Client.GetController'AbilityController'.CanUseAbilityAsCharacter(script.Name)
end


function YatanoKagami._Init()
	
end


return YatanoKagami
