local CollectionService = game:GetService('CollectionService')
local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local RunService = game:GetService('RunService')
local Debris = game:GetService('Debris')
local Players = game:GetService('Players')
local SoundService = game:GetService('SoundService')

local Client = require(ReplicatedStorage.Client)
local Server = require(ServerScriptService.Server)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local TableUtil = require(ReplicatedStorage.SharedModules.TableUtil)
local AlarmClock = require(ReplicatedStorage.SharedModules.AlarmClock)
local CookingService = require(script.Parent)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local InteractionController = require(ReplicatedStorage.Client.Core.Cooking.InteractionController)
local Math = require(ReplicatedStorage.SharedModules.Math)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)


return function(direction: Vector3, plr: Player)
	local AbilityService = Server.GetService('AbilityService')
	
	if not AbilityService.GetPassiveValue(plr, 'CanThrow') then return end
	
	local heldItem = CookingService.GetHeldItem(plr)

	if heldItem and CookingService.IsIngredient(heldItem) and CookingService.IsRawIngredient(heldItem) then
		if CookingService.PickupJanitors[plr] then
			CookingService.PickupJanitors[plr]:Cleanup()
		end

		local ThrownFoodJanitor = Janitor.new()

		for _, v in TableUtil.Extend(heldItem:GetDescendants(), heldItem:IsA'BasePart' and {heldItem} or {}) do
			if v:IsA'BasePart' then
				v.Anchored = false
				v.CanCollide = true
				v.Massless = true
				v.AssemblyLinearVelocity = Vector3.zero
				v.AssemblyAngularVelocity = Vector3.zero

				AlarmClock:ForbidSleep(v)

				ThrownFoodJanitor:Add(function()
					v.Massless = true
					AlarmClock:AllowSleep(v)
				end)
			end
		end

		ThrownFoodJanitor:Add(ClassExtension.Instance.Weld(heldItem))

		local throwDirection, lockedCharacter: Model? do
			throwDirection = direction

			for _, v in ClassExtension.Players.GetCharacters() do
				if v == plr.Character then continue end

				if CookingService.GetHeldItem({Character = v}) == nil
					and Math.IsInFront(plr.Character.PrimaryPart, v.PrimaryPart, 45, 25) then
					lockedCharacter = v
					break
				end
			end

			if lockedCharacter then
				throwDirection = -(plr.Character.PrimaryPart.Position - lockedCharacter.PrimaryPart.Position).Unit
			end

			throwDirection += Vector3.new(0, .55, 0)
		end


		local throwPosition = plr.Character.PrimaryPart.Position + throwDirection * AbilityService.GetPassiveValue(plr, 'ThrowDistance')

		heldItem:PivotTo(CFrame.new(throwPosition) * plr.Character.PrimaryPart.CFrame.Rotation)

		local primaryPart = heldItem:IsA'Model' and heldItem.PrimaryPart or heldItem
		primaryPart.Massless = false
		primaryPart:SetNetworkOwner(nil)

		RunService.Stepped:Wait()

		do -- throw physics
			local att = ThrownFoodJanitor:Add(Instance.new('Attachment'))
			att.Parent = primaryPart

			local vectorForce = ThrownFoodJanitor:Add(Instance.new('VectorForce'))
			vectorForce.Attachment0 = att
			vectorForce.Force = throwDirection * primaryPart:GetMass() * 630
			vectorForce.ApplyAtCenterOfMass = true
			vectorForce.RelativeTo = Enum.ActuatorRelativeTo.World
			vectorForce.Parent = primaryPart

			Debris:AddItem(vectorForce, .05)
		end

		heldItem.Parent = workspace

		SoundService.SFX.Throw:Play()
		CookingService.ItemThrown:Fire(plr, heldItem)

		CollectionService:AddTag(heldItem, Client.Immutable.Tags.THROWN_ITEMS)

		local throwTrack: AnimationTrack = Animations:PlayTrack(plr, 'Throw', 0)
		throwTrack:AdjustSpeed(1.2)

		do -- prevent flipping over
			local att = ThrownFoodJanitor:Add(Instance.new('Attachment'))
			att.Parent = primaryPart

			local faceUp = ThrownFoodJanitor:Add(Instance.new('AlignOrientation'))
			faceUp.Attachment0 = att
			faceUp.Mode = Enum.OrientationAlignmentMode.OneAttachment
			faceUp.CFrame = plr.Character.PrimaryPart.CFrame.Rotation
			faceUp.Responsiveness = 3
			--faceUp.RigidityEnabled = true
			faceUp.Parent = primaryPart
		end

		do -- Join interactable if possible
			local lastCfWhenOn: CFrame?
			local lastObjOn: BasePart?
			
			local rayParams do
				rayParams = RaycastParams.new()
				rayParams.FilterType = Enum.RaycastFilterType.Include
				rayParams.FilterDescendantsInstances = { CollectionService:GetTagged(Client.Immutable.Tags.MOVING_SURFACE) }
			end

			ThrownFoodJanitor:Add(RunService.Stepped:Connect(function(dt: number)
				if lockedCharacter and (lockedCharacter.PrimaryPart.Position - primaryPart.Position).Magnitude < 5
					and CookingService.GetHeldItem({Character = lockedCharacter}) == nil then
					local targetPlayer = Players:GetPlayerFromCharacter(lockedCharacter)
					if targetPlayer then
						ThrownFoodJanitor:Cleanup()
						CookingService.PickupItem(targetPlayer, heldItem, true)
						Animations:PlayTrack(plr, 'Catch', nil, 1.5)
						return
					end
				end

				local interactables = CookingService.GetInteractablesInRegion(primaryPart.Position, 2.5)
				local closestInteractable = InteractionController.GetClosestInteractable(primaryPart.Position, interactables)

				if closestInteractable then
					local directionToTarget = (closestInteractable.Position - primaryPart.Position).Unit
					if throwDirection:Dot(directionToTarget) > 0
						and CookingService.CanItemBePlacedOnInteractable(heldItem, closestInteractable) then
						local placed = CookingService.PlaceItem(heldItem, closestInteractable)
						if placed then
							ThrownFoodJanitor:Cleanup()
							return
						end
					end
				end

				do -- moving surfaces
					local res = workspace:Raycast(primaryPart.Position, Vector3.new(0, -15, 0), rayParams)
					if res and (lastObjOn == nil or res.Instance == lastObjOn) then
						local instCf = res.Instance:GetPivot()

						lastObjOn = res.Instance

						if not lastCfWhenOn then
							lastCfWhenOn = instCf
						end
						
						local rel = instCf * lastCfWhenOn:Inverse()
						primaryPart.CFrame = rel * primaryPart.CFrame

						lastCfWhenOn = instCf
					else
						if lastObjOn and lastCfWhenOn then
							local rel = lastObjOn.CFrame * lastCfWhenOn:Inverse()
							primaryPart.CFrame = rel * primaryPart.CFrame
						end

						lastObjOn = nil
						lastCfWhenOn = nil
					end
				end
			end), nil, 'Stepped')
		end

		ThrownFoodJanitor:Add(CollectionService:GetInstanceRemovedSignal(Client.Immutable.Tags.THROWN_ITEMS):Connect(function(item)
			if item ~= heldItem then return end
			ThrownFoodJanitor:Cleanup()
		end))

		ThrownFoodJanitor:Add(function()
			CollectionService:RemoveTag(heldItem, Client.Immutable.Tags.THROWN_ITEMS)
		end)
	end
end