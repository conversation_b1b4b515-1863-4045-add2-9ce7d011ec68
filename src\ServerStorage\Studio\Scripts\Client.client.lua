--//SERVICES
local Players		= game:GetService('Players')
local Workspace		= game:GetService('Workspace')
local RunService	= game:GetService('RunService')
--//VARIABLES
local Player		= Players.LocalPlayer
local PlayerGui		= Player:WaitForChild('PlayerGui')

local Arena			= Workspace:WaitForChild('Arena')
local Set			= Arena:WaitForChild('Set')
local CenterPart	= Set:WaitForChild('Center')
local PlayersFolder	= Set:WaitForChild('Players')

local HUD_UI		= PlayerGui:WaitForChild('HUD')
local AngleBox_UI	= HUD_UI:WaitForChild('Theta')
local Button_UI		= HUD_UI:WaitForChild('Button')

local CenterCF		= CenterPart.CFrame
local Camera		= Workspace.CurrentCamera

local FOV			= 60
--//FUNCTIONS
function DistanceFromCenter (A)
	return (A.Position - CenterPart.Position).magnitude
end

function ReturnCenter ()
	local GetPlayers		= PlayersFolder:GetChildren()
	local CenterPosition	= Vector3.new(0,0,0)
	
	for i, v in pairs(GetPlayers) do
		CenterPosition += v.Position
	end
	
	return (CenterPosition/#GetPlayers)
end

function FindFarthestPlayer ()
	local MaxDistance = 0
	
	for _, v in pairs(PlayersFolder:GetChildren()) do
		local Distance = DistanceFromCenter(v)
		
		if (Distance > MaxDistance) then
			MaxDistance = Distance
		end
	end
	
	return MaxDistance
end

function OnRenderCamera (DeltaTime)
	if (Camera.CameraType ~= Enum.CameraType.Scriptable) then
		Camera.CameraType = Enum.CameraType.Scriptable
	end
	
	local PlayersCenter		= ReturnCenter()
	
	local angleA			= math.rad(90) --//Right Triangle
	local angleB			= math.rad(FOV) --//Theta is the given angle
	local angleC			= math.rad((180 - (math.deg(angleA) + math.deg(angleB)))) --//A triangle equeals 180°
	
	local sideC				= (PlayersFolder.PlayerA.Position - PlayersFolder.PlayerB.Position).Magnitude or FindFarthestPlayer() --//In a multiplayer game idealy it'll be FindFarthestPlayer
	local sideA				= sideC * math.sin(angleA) / math.sin(angleC) --//Law Of Sine
	
	local ToCenterCF		= CenterPart.CFrame:ToObjectSpace(CFrame.new(PlayersCenter)) --//PlayersCenter to the CenterParts CFrame
	
	Camera.CFrame = CenterPart.CFrame * CFrame.new(ToCenterCF.X,ToCenterCF.Y,sideA)
end
--
Camera.CameraType = Enum.CameraType.Scriptable
--
RunService.RenderStepped:Connect(OnRenderCamera)




--//Testing Stuff
function OnButtonDown()
	local ToAngle = tonumber(AngleBox_UI.Text)
	
	if ToAngle then
		Theta							= math.clamp(ToAngle, 0, 90)
		AngleBox_UI.PlaceholderText		= ToAngle
	end
end

Button_UI.MouseButton1Down:Connect(OnButtonDown)