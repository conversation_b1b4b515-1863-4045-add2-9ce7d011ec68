--[=[
	Author: <PERSON><PERSON><PERSON> (Syveric)
	SetCharacterAttachments
]=]

local ReplicatedStorage = game:GetService('ReplicatedStorage')

local CharactersData = require(ReplicatedStorage.Data.Characters)

return function(character: Model, characterName: string)
	local thisCharacterInfo = CharactersData[characterName]
	
	for _, v in character:GetChildren() do
		if v:IsA'Model' then
			v:Destroy()
		end
	end
	
	if thisCharacterInfo and thisCharacterInfo.Attachments then
		for _, v in thisCharacterInfo.Attachments do
			local attachmentFolder = ReplicatedStorage.Assets.CharacterAttachments[v]

			local Object = attachmentFolder:FindFirstChildOfClass'Model':Clone()
			local Motor = attachmentFolder:FindFirstChildOfClass'Motor6D':Clone()

			Motor.Parent = character[attachmentFolder.Limb.Value]
			Motor.Part0 = character[attachmentFolder.Limb.Value]
			Motor.Part1 = Object.PrimaryPart
			Object.Parent = character
		end
	end
end