local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)
local Packets = require(ReplicatedStorage.Data.Packets)

local LightFlashIntervention = {
	Key = Enum.KeyCode.C,

	GamepadKey = Enum.KeyCode.ButtonL2
}


function LightFlashIntervention.KeyDown()
	Packets.Ability.UseAbility.send({
		AbilityName = script.Name,
		Args = {}
	})
end


function LightFlashIntervention.KeyUp()

end


function LightFlashIntervention.Click()
	LightFlashIntervention.KeyDown()
end


function LightFlashIntervention.IsOnCooldown()
	return Client.GetController'AbilityController'.IsAbilityOnCooldown(script.Name)
end


function LightFlashIntervention.CanUseAbilityAsCharacter()
	return Client.GetController'AbilityController'.CanUseAbilityAsCharacter(script.Name)
end


function LightFlashIntervention._Init()

end


return LightFlashIntervention
