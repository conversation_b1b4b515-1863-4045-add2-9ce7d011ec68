local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)

local QuestsData = require(ReplicatedStorage.Data.Quests)
local Packets = require(ReplicatedStorage.Data.Packets)

local QuestController = {
	CurrentQuestSelection = ''
}


local function setupClaimButtons()
	local QuestFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Quests
	
	QuestFrame.Content.Holder.Tab.Claim.MouseButton1Click:Connect(function()
		local category, questId = QuestController.CurrentQuestSelection:match'(.+),(.+)'
		if category and questId then
			Packets.Quest.ClaimQuest.send({
				Category = category,
				QuestID = questId
			})
		end
	end)
	
	QuestFrame.Content.Holder.Quests.ClaimAll.MouseButton1Click:Connect(function()
		Packets.Quest.ClaimAllQuests.send()
	end)
end


local function setupTopbarButtons()
	local QuestFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Quests
	local QuestObjectsContainer = QuestFrame.Content.Holder.Quests.Slots.Scroller
	local TopbarButtons = QuestFrame.Content.Buttons
	
	for _, v in TopbarButtons:GetChildren() do
		if v:IsA'ImageButton' then
			v.MouseButton1Click:Connect(function()
				QuestController.SetSlotDisplayCategory(v.Name)
			end)
		end
	end
	
	QuestController.SetSlotDisplayCategory('All')
	
	local function updateCounters()
		local counts = {}
		local total = 0
		
		for _, v in QuestObjectsContainer:GetChildren() do
			if v:IsA'ImageButton' then
				local category = v.Name:match'^(%w+)'
				counts[category] = counts[category] and counts[category] + 1 or 1
				total += 1
			end
		end
		
		counts.All = total
		
		for _, topButton in TopbarButtons:GetChildren() do
			if topButton:IsA'ImageButton' then
				local amount = counts[topButton.Name] or 0
				topButton.Txt.Text = `{topButton.Name} ({amount})`
			end
		end
	end
	
	QuestObjectsContainer.ChildAdded:Connect(updateCounters)
	QuestObjectsContainer.ChildRemoved:Connect(updateCounters)
	
	task.defer(updateCounters)
end


local function refreshQuestSlot(data: { Category: string, Claimed: boolean, Progress: number, QuestID: string })
	local QuestFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Quests
	local QuestObjectsContainer = QuestFrame.Content.Holder.Quests.Slots.Scroller
	
	local QuestSlotID = `{data.Category},{data.QuestID}`
	local ThisQuestInfo = QuestsData.FindQuestFromCategoryAndID(data.Category, data.QuestID)
	
	local QuestSlot = QuestObjectsContainer:FindFirstChild(QuestSlotID)
	if not QuestSlot then
		QuestSlot = ReplicatedStorage.Assets.Templates.QuestTemplate:Clone()
		
		QuestSlot.Name = QuestSlotID
		QuestSlot.Details.Description.Text = ThisQuestInfo.Description
		QuestSlot.Details.Txt.Text = ThisQuestInfo.Name
		QuestSlot.Badge.Txt.Text = data.Category:upper()
		QuestSlot.Stroke.IsSelected.Enabled = false
		QuestSlot.Stroke.NotSelected.Enabled = true
		
		QuestSlot.Parent = QuestObjectsContainer
		
		QuestSlot.MouseButton1Click:Connect(function()
			QuestController.UpdateQuestSelection(data.Category, data.QuestID)
		end)
	end

	QuestSlot:SetAttribute('Progress', data.Progress)
	QuestSlot:SetAttribute('Claimed', data.Claimed)
	
	if QuestController.CurrentQuestSelection == '' then
		QuestSlot.LayoutOrder -= 1
		QuestController.UpdateQuestSelection(data.Category, data.QuestID)
	elseif QuestController.CurrentQuestSelection == QuestSlotID then
		QuestController.UpdateQuestSelection(data.Category, data.QuestID)
	end
end


function QuestController.SetSlotDisplayCategory(category: string)
	local QuestFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Quests
	local QuestObjectsContainer = QuestFrame.Content.Holder.Quests.Slots.Scroller
	local TopbarButtons = QuestFrame.Content.Buttons
	
	for _, v in QuestObjectsContainer:GetChildren() do
		if v:IsA'ImageButton' then
			local buttonCategory = v.Name:match'^(%w+)'
			v.Visible = buttonCategory == category or category == 'All'
		end
	end
	
	for _, v in TopbarButtons:GetChildren() do
		if v:IsA'ImageButton' then
			v.IsSelected.Enabled = v.Name == category
			v.NotSelected.Enabled = v.Name ~= category
		end
	end
end


function QuestController.UpdateQuestSelection(category: string, questId: string)
	local QuestFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Quests
	local QuestObjectsContainer = QuestFrame.Content.Holder.Quests.Slots.Scroller
	local QuestInfoTab = QuestFrame.Content.Holder.Tab
	
	local OldQuestFrame = QuestObjectsContainer:FindFirstChild(QuestController.CurrentQuestSelection)
	if OldQuestFrame then
		OldQuestFrame.Stroke.IsSelected.Enabled = false
		OldQuestFrame.Stroke.NotSelected.Enabled = true
	end
	
	QuestController.CurrentQuestSelection = `{category},{questId}`
	
	local ThisQuestFrame = QuestObjectsContainer:FindFirstChild(QuestController.CurrentQuestSelection)
	if ThisQuestFrame then
		ThisQuestFrame.Stroke.IsSelected.Enabled = true
		ThisQuestFrame.Stroke.NotSelected.Enabled = false
		
		-- TODO: Update main display
		local ThisQuestInfo = QuestsData.FindQuestFromCategoryAndID(category, questId)
		
		QuestInfoTab.Descritpion.Text = ThisQuestInfo.Description
		QuestInfoTab.Header.Txt.Text = ThisQuestInfo.Name
		QuestInfoTab.Header.Badge.Txt.Text = category:upper()
		
		QuestInfoTab.Claim.Txt.Text = ThisQuestFrame:GetAttribute'Claimed' and 'CLAIMED' or 'CLAIM QUEST REWARDS'
		
		local progress = math.clamp(ThisQuestFrame:GetAttribute'Progress' or 0, 0, ThisQuestInfo.Requirement)
		QuestInfoTab.ProgressBar.Txt.Text = `Progress: {progress} / {ThisQuestInfo.Requirement}`
		
		local progressRatio = math.clamp(ThisQuestFrame:GetAttribute'Progress' / ThisQuestInfo.Requirement, 0, 1)
		QuestInfoTab.ProgressBar.Holder.Loader.UIGradient.Transparency = NumberSequence.new{
			NumberSequenceKeypoint.new(0, 0),
			NumberSequenceKeypoint.new(math.clamp(progressRatio, .001, .998), 0),
			NumberSequenceKeypoint.new(math.clamp(progressRatio + .001, .002, .999), 1),
			NumberSequenceKeypoint.new(1, 1),
		}
	end
end


function QuestController._Start()
	local QuestFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.Quests

	for _, v in QuestFrame.Content.Holder.Quests.Slots.Scroller:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	task.defer(setupTopbarButtons)
	task.defer(setupClaimButtons)
	
	Packets.Quest.RequestQuestData.listen(refreshQuestSlot)
	Packets.Quest.RequestQuestData.send()
end


return QuestController