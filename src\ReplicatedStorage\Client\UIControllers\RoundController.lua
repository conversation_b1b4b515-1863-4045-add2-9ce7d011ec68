local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local TweenService = game:GetService('TweenService')
local RunService = game:GetService('RunService')
local ContentProvider = game:GetService('ContentProvider')
local TeleportService = game:GetService('TeleportService')

local Client = require(ReplicatedStorage.Client)

local ClientImmutable = Client.Immutable
local Packets = require(ReplicatedStorage.Data.Packets)
local NumberController = require(ReplicatedStorage.SharedModules.NumberController)
local CharactersData = require(ReplicatedStorage.Data.Characters)
local XPPerLevel = require(ReplicatedStorage.Data.XPPerLevel)

local RoundController = {
	RoundStart = 0,
	Difficulty = 'Easy',
	Act = nil,
	OrdersFailed = 0,
	OrdersCompleted = 0,
	PlayersEndedWith = 0,
	RoundLen = ClientImmutable.Round.ROUND_LEN
}


local function setupTimer()
	local TimerFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'GameplayUI'.BottomRight.TimerFrame

	RunService.RenderStepped:Connect(@native function(dt: number)
		local elapsed = (workspace:GetServerTimeNow() - RoundController.RoundStart)

		local timeLeft = math.clamp(RoundController.RoundLen - elapsed, 0, RoundController.RoundLen)

		TimerFrame.TimeLeft.Text = NumberController.ToMS(timeLeft)
		TimerFrame.Progress.Bar.Size = UDim2.fromScale(timeLeft/RoundController.RoundLen, 1)
	end)
end


local function updateRoundInfo(data: { RoundLen: number, FailableOrders: number, OrdersToComplete: number?, Difficulty: string, Act: number?, OrdersFailed: number, OrdersCompleted: number, RoundStart: number }?)
	local GameplayUI = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'GameplayUI'
	
	for k, v in data do
		RoundController[k] = v
	end
	-- GameplayUI.BottomRight.OrderStates[Correct Errors][ImageLabel TextLabel]
	-- RS.Assets.Templates[CorrectOrder ErrorOrder]

	for _, v in GameplayUI.BottomRight.OrderStates.Correct:GetChildren() do
		if v:IsA'ImageLabel' then
			v:Destroy()
		end
	end

	for _, v in GameplayUI.BottomRight.OrderStates.Errors:GetChildren() do
		if v:IsA'ImageLabel' then
			v:Destroy()
		end
	end

	if data.FailableOrders < 1000 and data.OrdersFailed > 0 then
		for i = 1, data.OrdersFailed do
			local FaliedOrderTemplate = ReplicatedStorage.Assets.Templates.ErrorOrder:Clone()
			FaliedOrderTemplate.Parent = GameplayUI.BottomRight.OrderStates.Errors
		end

		GameplayUI.BottomRight.OrderStates.Errors.TextLabel.Text = `{data.OrdersFailed} / {data.FailableOrders}`
		GameplayUI.BottomRight.OrderStates.Errors.Visible = true
	else
		GameplayUI.BottomRight.OrderStates.Errors.Visible = false
	end

	if data.OrdersToComplete then
		for i = 1, data.OrdersCompleted do
			local FaliedOrderTemplate = ReplicatedStorage.Assets.Templates.CorrectOrder:Clone()
			FaliedOrderTemplate.Parent = GameplayUI.BottomRight.OrderStates.Correct
		end

		if data.OrdersToComplete < 1000 then
			GameplayUI.BottomRight.OrderStates.Correct.TextLabel.Text = `{data.OrdersCompleted} / {data.OrdersToComplete}`
		else
			GameplayUI.BottomRight.OrderStates.Correct.TextLabel.Text = `{data.OrdersCompleted}    `
		end
		GameplayUI.BottomRight.OrderStates.Correct.Visible = true
	else
		GameplayUI.BottomRight.OrderStates.Correct.Visible = false
	end
	
	GameplayUI.BottomRight.OrderStates.Visible = GameplayUI.BottomRight.OrderStates.Correct.Visible or GameplayUI.BottomRight.OrderStates.Errors.Visible
	
	print(data, 'aaaaaaaaaaaaaaaa')
end


local function showTutorial()
	local PlaceName = ClientImmutable.Servers.PLACES[game.GameId][game.PlaceId]
	local TutorialImage = ClientImmutable.Servers.PLACE_INFO[PlaceName].TutorialImage

	local TutorialFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'GameplayUI'.CookWarnings.Tutorial
	TutorialFrame.Image = TutorialImage
	TutorialFrame.Visible = true

	TutorialFrame.Close.MouseButton1Click:Connect(function()
		TutorialFrame.Visible = false
	end)
end


local function start()
	local UIController = Client.GetController('UIController')

	local CookWarnings = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'GameplayUI'.CookWarnings
	local EndScreen = Players.LocalPlayer.PlayerGui.GameplayUI.EndScreen

	UIController.Close(EndScreen)

	CookWarnings.ReadyWarning.UIScale.Scale = 0
	CookWarnings.GoWarning.UIScale.Scale = 0

	CookWarnings.ReadyWarning.Visible = true
	CookWarnings.GoWarning.Visible = true

	task.wait(.3)

	TweenService:Create(CookWarnings.ReadyWarning.UIScale, TweenInfo.new(.5, Enum.EasingStyle.Back, Enum.EasingDirection.InOut), {Scale = 1}):Play()

	task.wait(2.3)

	TweenService:Create(CookWarnings.ReadyWarning.UIScale, TweenInfo.new(.4, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {Scale = 0}):Play()
	TweenService:Create(CookWarnings.GoWarning.UIScale, TweenInfo.new(.35, Enum.EasingStyle.Back, Enum.EasingDirection.InOut), {Scale = 1}):Play()

	task.wait(1.8)

	TweenService:Create(CookWarnings.GoWarning.UIScale, TweenInfo.new(.1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {Scale = 0}):Play()
	
	Packets.Round.GetRoundInfo.send()
end


local function roundEnded(data: { PlayersEndedWith: number, Act: number?, Difficulty: string, MapName: string, OrdersDelivered: number, OrdersFailed: nuumber, Tips: number, Win: boolean, Elapsed: number, Score: number })
	local UIController = Client.GetController('UIController')
	local ItemController = Client.GetController('ItemController')

	local EndScreen = Players.LocalPlayer.PlayerGui.GameplayUI.EndScreen

	local character = Players.LocalPlayer:GetAttribute'Character'

	RoundController.PlayersEndedWith = data.PlayersEndedWith
	RoundController.RoundStart = 0

	UIController.Open(EndScreen)

	if data.Win then
		EndScreen.Banner.UnderTxt.Text = 'VICTORY'
		EndScreen.Banner.UnderTxt.Txt.Text = 'VICTORY'
		EndScreen.Contents.Title.Text = 'ENDED LEVEL SUCCESFULLY'
	else
		EndScreen.Banner.UnderTxt.Text = 'DEFEAT'
		EndScreen.Banner.UnderTxt.Txt.Text = 'DEFEAT'
		EndScreen.Contents.Title.Text = 'FAILED LEVEL'
	end

	EndScreen.Contents.Banner.LevelName.Text = data.MapName:upper()

	if data.Act then
		EndScreen.Contents.Banner.ActNumber.Text = 'ACT ' .. (data.Act == 255 and '∞' or data.Act)
	else
		EndScreen.Contents.Banner.ActNumber.Visible = false
	end

	if data.Difficulty then
		EndScreen.Contents.Banner.Diffiulcty.Text = 'DIFFICULTY: ' .. data.Difficulty:upper()
	else
		EndScreen.Contents.Banner.Diffiulcty.Visible = false
	end

	EndScreen.Contents.Stats.Stats.Playtime.Number.Text = math.max(1, math.floor(data.Elapsed / 60)) .. 'm'
	EndScreen.Contents.Stats.Stats.OrdersDelivered.Number.Text = `{data.OrdersDelivered} ({data.Score - data.Tips}$)`
	EndScreen.Contents.Stats.Stats.OrdersFailed.Number.Text = data.OrdersFailed
	EndScreen.Contents.Stats.Stats.Tips.Number.Text = `${data.Tips}`

	EndScreen.Contents.Stats.TotalEarned.Stat.Text = `TOTAL EARNED: +{data.Score}$`

	EndScreen.Buttons.Replay.People.Text = `(0/{data.PlayersEndedWith})`

	if character then
		EndScreen.Contents.UnitXp.Info.UnitName.Text = `Unit Used: {character:upper()}`
	end

	--[[UIController.PageToggled:Connect(function(frame: GuiObject, isOpen: boolean)
		if frame == EndScreen and isOpen == false then
			local lobbyPlaceId
			for placeId, placeName in ClientImmutable.Servers.PLACES do
				if placeName == 'Lobby' then
					lobbyPlaceId = placeId
					break
				end
			end
			
			TeleportService:Teleport(lobbyPlaceId)
		end
	end)]]

	do
		EndScreen.Contents.UnitXp.Unit:Destroy()

		local ItemFrame = ItemController.CreateItem({
			ItemName = character,
			Size = script.Unit.Size,
			Rarity = CharactersData[character].Rarity,
			Parent = EndScreen.Contents.UnitXp,
		})
		ItemFrame.LayoutOrder = script.Unit.LayoutOrder
		ItemFrame.Name = 'Unit'
	end
end


function RoundController._Start()
	if ClientImmutable.SERVER_TYPE ~= 'Round' then return end

	Packets.Round.ShowTutorial.listen(showTutorial)
	Packets.Round.Start.listen(start)

	Packets.Round.GetRoundInfo.send()
	Packets.Round.GetRoundInfo.listen(updateRoundInfo)

	task.defer(setupTimer)

	Packets.Round.RoundEnded.listen(roundEnded)

	local CookWarnings = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'GameplayUI'.CookWarnings
	ContentProvider:PreloadAsync(CookWarnings:GetChildren())

	do
		local EndScreen = Players.LocalPlayer.PlayerGui.GameplayUI.EndScreen

		Players.PlayerRemoving:Connect(function()
			if EndScreen.Visible then
				EndScreen.Buttons.Visible = false
			end
		end)

		Packets.Round.VoteReplay.listen(function(data: number)
			EndScreen.Buttons.Replay.People.Text = `({data}/{RoundController.PlayersEndedWith})`
		end)

		EndScreen.Buttons.Replay.MouseButton1Click:Connect(function()
			Packets.Round.VoteReplay.send()
		end)

		EndScreen.Buttons.Return.MouseButton1Click:Connect(function()
			Packets.Round.VoteReturn.send()
		end)

		Packets.Round.RoundXPGain.listen(function(data: { Level: number, XP: number, XPGained: number })
			EndScreen.Contents.UnitXp.Info.XPgain.Text = `XP Gained: {data.XPGained}xp`

			do
				local function update(xp, level)
					EndScreen.Contents.UnitXp.Info.ProgressBar.XP.Text = `{math.ceil(xp)} / {XPPerLevel[level]} XP`
					EndScreen.Contents.UnitXp.Info.ProgressBar.Lvl.Text = `LVL: {level}`

					local progressRatio = xp / XPPerLevel[level]
					EndScreen.Contents.UnitXp.Info.ProgressBar.Holder.Loader.UIGradient.Transparency = NumberSequence.new{
						NumberSequenceKeypoint.new(0, 0),
						NumberSequenceKeypoint.new(math.clamp(progressRatio, .001, .998), 0),
						NumberSequenceKeypoint.new(math.clamp(progressRatio + .001, .002, .999), 1),
						NumberSequenceKeypoint.new(1, 1),
					}
				end

				local currentXP = data.XP
				local currentLevel = data.Level

				currentXP -= data.XPGained

				if currentXP < 0  then
					for i = data.Level - 1, 1, -1 do
						currentXP += XPPerLevel[i]

						if currentXP >= 0 then
							currentLevel = i
							break
						end
					end
				end

				local SPEED = 8.2
				local c c = RunService.RenderStepped:Connect(function(dt: number)
					currentXP += dt * SPEED

					if currentXP >= XPPerLevel[currentLevel] then
						currentLevel += 1
						currentXP = 0
					end

					if currentXP >= data.XP and currentLevel >= data.Level then
						update(data.XP, data.Level)
						c:Disconnect()
						return
					end

					update(currentXP, currentLevel)
				end)
			end
		end)
	end
end


return RoundController
