local ReplicatedStorage = game:GetService('ReplicatedStorage')
local MessagingService = game:GetService('MessagingService')
local ServerScriptService = game:GetService('ServerScriptService')
local HttpService = game:GetService('HttpService')
local Players = game:GetService('Players')
local RunService = game:GetService('RunService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)
local ServerImmutable = Server.Immutable
local ClientImmutable = Client.Immutable

local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)

type ServerInfo = {
	CreateTime: number,
	ServerName: string,
	Players: {number},
	JobId: string,
	PlaceId: number
}

local GameServerService = {
	IndexedServers = {} :: { [string]: ServerInfo }
}


function GameServerService.GetServerName(): string
	return GameServerService.IndexedServers[game.JobId] and GameServerService.IndexedServers[game.JobId].ServerName or 'LOADING'
end


do
	local function randomSyllable(): string
		local c = ServerImmutable.Letters.CONSONANTS_CLUSTER[math.random(#ServerImmutable.Letters.CONSONANTS_CLUSTER)]
		local v = ServerImmutable.Letters.VOWELS_CLUSTER[math.random(#ServerImmutable.Letters.VOWELS_CLUSTER)]
		local syllable = c .. v
		
		if math.random(2) == 1 then
			syllable ..= ServerImmutable.Letters.CONSONANTS_CLUSTER[math.random(#ServerImmutable.Letters.CONSONANTS_CLUSTER)]
		end
		
		return syllable
	end


	function GameServerService._GenerateServerName()
		local ChatHandlerService = Server.GetService('ChatHandlerService')

		while GameServerService.IndexedServers[game.JobId] do
			local serverName = randomSyllable() .. randomSyllable()
			serverName = serverName:sub(1, 1):upper() .. serverName:sub(2)

			local exists = false
			for k, v in GameServerService.IndexedServers do
				if v.ServerName == serverName then
					exists = true
					break
				end
			end

			if exists then continue end

			if not ChatHandlerService.FilterString(serverName):find'#' and GameServerService.IndexedServers[game.JobId] then
				GameServerService.IndexedServers[game.JobId].ServerName = serverName

				if GameServerService.IndexedServers[game.JobId] then
					MessagingService:PublishAsync(ServerImmutable.Topics.SERVER_INDEXING, {
						Function = 'Update',
						Info = GameServerService.IndexedServers[game.JobId]
					})
				end

				break
			end
			
			task.wait(1)
		end
		
		if GameServerService.IndexedServers[game.JobId] then
			print('[SERVER NAME]: ', GameServerService.IndexedServers[game.JobId].ServerName)
		end
	end
end


function GameServerService.Shutdown(shutdownedServerData: ServerInfo)
	if not shutdownedServerData then return end
	GameServerService.IndexedServers[shutdownedServerData.JobId] = nil
end


function GameServerService.Update(updatedServerData: ServerInfo)
	if #updatedServerData.Players == 0 and os.time() - updatedServerData.CreateTime > 60 then
		return GameServerService.Shutdown(updatedServerData)
	end
	
	if not GameServerService.IndexedServers[updatedServerData.JobId] then
		GameServerService.IndexedServers[updatedServerData.JobId] = {}
	end

	GameServerService.IndexedServers[updatedServerData.JobId] = updatedServerData
end


function GameServerService.Create(createdServerData: ServerInfo)
	GameServerService.IndexedServers[createdServerData.JobId] = createdServerData
end


function GameServerService._PlayerAdded(plr: Player)
	local serverData = GameServerService.IndexedServers[game.JobId]

	table.insert(serverData.Players, plr.UserId)

	if GameServerService.IndexedServers[game.JobId] then
		MessagingService:PublishAsync(ServerImmutable.Topics.SERVER_INDEXING, {
			Function = 'Update',
			Info = serverData
		})
	end
end


function GameServerService._PlayerRemoving(plr: Player)
	local serverData = GameServerService.IndexedServers[game.JobId]

	if not serverData then return end
	
	local index = table.find(serverData.Players, plr.UserId)
	if index then
		table.remove(serverData.Players, index)

		if GameServerService.IndexedServers[game.JobId] then
			MessagingService:PublishAsync(ServerImmutable.Topics.SERVER_INDEXING, {
				Function = 'Update',
				Info = serverData
			})
		end
	end
end


function GameServerService._Start()
	game:BindToClose(function()
		MessagingService:PublishAsync(ServerImmutable.Topics.SERVER_INDEXING, {
			Function = 'Shutdown',
			Info = GameServerService.IndexedServers[game.JobId]
		})
	end)
	
	ClassExtension.Instance.WaitForChildOfClass(Players, 'Player')
	
	while GameServerService.IndexedServers[game.JobId] do
		if GameServerService.IndexedServers[game.JobId] then
			MessagingService:PublishAsync(ServerImmutable.Topics.SERVER_INDEXING, {
				Function = 'Update',
				Info = GameServerService.IndexedServers[game.JobId]
			})
		end

		task.wait(45)
	end
end


function GameServerService._Init()
	local RoundService = Server.GetService('RoundService')
	
	if ClientImmutable.SERVER_TYPE == 'Lobby' then
		RoundService._LoadMap('Lobby')
	elseif ClientImmutable.SERVER_TYPE == 'AFK' then
		RoundService._LoadMap('AFK')
	end
	
	local thisServerData = {
		Players = {},

		CreateTime = os.time(),
		ServerName = HttpService:GenerateGUID(false):match'%w+-(%w+)',
		JobId = game.JobId,
		PlaceId = game.PlaceId
	}

	GameServerService.IndexedServers[game.JobId] = thisServerData

	task.spawn(MessagingService.PublishAsync, MessagingService, ServerImmutable.Topics.SERVER_INDEXING, {
		Function = 'Create',
		Info = thisServerData
	})

	MessagingService:SubscribeAsync(ServerImmutable.Topics.SERVER_INDEXING, function(data)
		data = data.Data

		if GameServerService[data.Function] then
			GameServerService[data.Function](data.Info)
		end
	end)

	task.defer(GameServerService._GenerateServerName)
end


return GameServerService