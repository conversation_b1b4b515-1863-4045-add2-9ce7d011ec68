local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)

local PassiveModule = {}


function PassiveModule.Get()
	local ChallengeService = Server.GetService('ChallengeService')
	
	local default = 0
	
	if ChallengeService.Modifiers[script.Name] then
		default = ChallengeService.Modifiers[script.Name]
	end
	
	return default
end


function PassiveModule.Init()

end


return PassiveModule