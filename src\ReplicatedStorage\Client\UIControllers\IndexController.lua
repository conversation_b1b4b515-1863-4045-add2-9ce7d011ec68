local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local CharactersData = require(ReplicatedStorage.Data.Characters)

local IndexController = {}


local function updateOwned(data: {string})
	local IndexFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Index
	
	local RarityCounts = {}
	local OwnedRarityCounts = {}
	
	for k, v in CharactersData do
		RarityCounts[v.Rarity] = (RarityCounts[v.Rarity] or 0) + 1
	end
	
	for _, v in IndexFrame.Holder.Scroller:GetChildren() do
		if v:IsA'ImageButton' then
			local Owned = table.find(data, v.Name) ~= nil
			
			local CharRarity = CharactersData[v.Name].Rarity
			OwnedRarityCounts[CharRarity] = (OwnedRarityCounts[CharRarity] or 0) + (Owned and 1 or 0)
			
			v.LockedLayer.Visible = not Owned
		end
	end
	
	for _, v in Client.Immutable.RARITIES do
		local RarityHeader = IndexFrame.Holder.Scroller[`{v}Header`]
		RarityHeader.Percentage.Text = (math.ceil(OwnedRarityCounts[v] / RarityCounts[v] * 10_000) / 100) .. '%'
	end
end


local function setupIndex()
	local ItemController = Client.GetController('ItemController')
	
	local IndexFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Index
	
	for _, v in IndexFrame.Holder.Scroller:GetChildren() do
		if v:IsA'ImageButton' then
			v:Destroy()
		end
	end
	
	for k, v in CharactersData do
		local ItemFrame = ItemController.CreateItem({
			ItemName = v.Name,
			Rarity = v.Rarity,
			Size = script.UnitContainer.Size,
			Parent = IndexFrame.Holder.Scroller,
		})
		
		local rarityIndex = table.find(Client.Immutable.RARITIES, v.Rarity) * 1_000
		ItemFrame.LayoutOrder = rarityIndex + 1
		
		script.UnitContainer.UIAspectRatioConstraint:Clone().Parent = ItemFrame
		script.LockedLayer:Clone().Parent = ItemFrame
	end
end


function IndexController._Start()
	local IndexFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.Index
	
	task.defer(function()
		setupIndex()
		
		Packets.Inventory.OwnedCharacters.listen(updateOwned)
		Packets.Inventory.OwnedCharacters.send()
	end)
end


return IndexController