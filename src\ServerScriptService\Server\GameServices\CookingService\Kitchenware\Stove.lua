local ReplicatedStorage = game:GetService('ReplicatedStorage')
local TweenService = game:GetService('TweenService')
local ServerScriptService = game:GetService('ServerScriptService')
local CollectionService = game:GetService('CollectionService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local CookingService = require(script.Parent.Parent)
local Ingredients = require(ReplicatedStorage.Data.Ingredients)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)

local Stove = {
	StoveContainers = {'FryingPan', 'Pot'},
	BURN_AFTER = 11,

	StoveJanitors = {}
}


function Stove.Interact(plr: Player, interactable: BasePart, ingredient: BasePart?, replaceAsIngredient: boolean?)
	local ingredientOnInteractable = CookingService.GetIngredientOnInteractable(interactable)
	if ingredientOnInteractable and ingredientOnInteractable:GetAttribute('Stoving') then return end

	local AbilityService = Server.GetService('AbilityService')
	local TutorialService = Server.GetService('TutorialService')

	local ItemOnStove = CookingService.GetItemOnInteractable(interactable)
	local HeldItem = CookingService.GetHeldItem(plr)

	if replaceAsIngredient then
		HeldItem = ingredient
	end

	if ItemOnStove and not ItemOnStove:GetAttribute'OriginalOffset' then
		ItemOnStove:SetAttribute('OriginalOffset', interactable:GetPivot():ToObjectSpace(ItemOnStove:GetPivot()))
	end

	if not HeldItem and ItemOnStove and ingredientOnInteractable and Stove.IsActionComplete(ingredientOnInteractable) then -- pickup pan
		if Stove.StoveJanitors[interactable] then
			Stove.StoveJanitors[interactable]:Cleanup()
		end

		CookingService.PickupItem(plr, ItemOnStove)
	elseif HeldItem then -- place pan or ingredient
		if CookingService.IsIngredient(HeldItem) and ItemOnStove then
			local _, stepIdx = CookingService.GetNextIngredientStep(HeldItem)
			local stepInfo = Ingredients[HeldItem.Name].PrepSteps[stepIdx]

			if ItemOnStove.Name ~= stepInfo.StoveType then return end

			local placed = CookingService.PlaceItem(replaceAsIngredient and CookingService.GetHeldItem(plr) or HeldItem, interactable, plr)
			if not placed then return end

			Stove.StoveJanitors[interactable] = Janitor.new()

			HeldItem:SetAttribute('Stoving', true)
			
			if interactable:FindFirstChild'Pot' and interactable.Pot:FindFirstChild'Water' then
				interactable.Pot.Water.Transparency = .4
			end
			
			VFXFunctions.EnableDescendants(interactable)

			local ProgressBar = Stove.StoveJanitors[interactable]:Add(ReplicatedStorage.Assets.Templates.ProgressBar:Clone())
			ProgressBar.Parent = ingredient

			local progressNV = Stove.StoveJanitors[interactable]:Add(Instance.new('NumberValue'))
			progressNV.Value = 0

			local t = TweenService:Create(progressNV,
				TweenInfo.new(AbilityService.GetPassiveValue(plr, 'CookTime'), Enum.EasingStyle.Linear),
				{Value = 1}
			);t:Play();

			Stove.StoveJanitors[interactable]:Add(progressNV:GetPropertyChangedSignal'Value':Connect(function()
				ProgressBar.Bar.Progress.UIGradient.Transparency = NumberSequence.new{
					NumberSequenceKeypoint.new(0, 0),
					NumberSequenceKeypoint.new(math.clamp(progressNV.Value, .001, .998), 0),
					NumberSequenceKeypoint.new(math.clamp(progressNV.Value + .001, .002, .999), 1),
					NumberSequenceKeypoint.new(1, 1)
				}
			end))

			Stove.StoveJanitors[interactable]:Add(t.Completed:Once(function(playbackState: Enum.PlaybackState)
				if playbackState == Enum.PlaybackState.Completed then
					HeldItem = Stove.SetIngredientAsCooked(HeldItem)
					ingredient = HeldItem
				end
			end))

			if not TutorialService.Tutorial then
				local burnTime = AbilityService.GetPassiveValue(plr, 'BurnTime')
				Stove.StoveJanitors[interactable]:Add(task.delay(burnTime, function()
					local newBurnTime = AbilityService.GetPassiveValue(plr, 'BurnTime')
					local diff = math.max(newBurnTime, burnTime) - math.min(newBurnTime, burnTime)
					Stove.StoveJanitors[interactable]:Add(task.delay(diff, function()
						if not ingredient:FindFirstChild'ProgressBar' then return end

						HeldItem = Stove.SetIngredientAsBurnt(HeldItem)
						ingredient = HeldItem

						ProgressBar.Enabled = false

						CookingService.SetInteractableOnFire(ingredient)
					end))
				end))
			end
			
			Stove.StoveJanitors[interactable]:Add(function()
				VFXFunctions.DisableDescendants(interactable, false)
				
				if interactable:FindFirstChild'Pot' and interactable.Pot:FindFirstChild'Water' then
					interactable.Pot.Water.Transparency = 1
				end
				
				local fire = interactable:FindFirstChild('Fire', true)
				if fire then
					VFXFunctions.EnableDescendants(fire)
				end
			end)
		elseif table.find(Stove.StoveContainers, HeldItem.Name) then
			local foundIngredientInHand = CookingService.GetIngredientOnInteractable(HeldItem)
			if foundIngredientInHand and not CookingService.GetIngredientOnInteractable(interactable) then
				local containerOnInteractable = CookingService.GetFoodSurfaceObject(interactable)
				if containerOnInteractable then
					local _, stepIdx = CookingService.GetNextIngredientStep(foundIngredientInHand)
					local stepInfo = Ingredients[foundIngredientInHand.Name].PrepSteps[stepIdx]

					if stepInfo and stepInfo.StoveType == containerOnInteractable.Name then
						Stove.Interact(plr, interactable, foundIngredientInHand, true)
						return
					end
				end
			end

			CookingService.PlaceItem(HeldItem, interactable, plr)
		end
	end
end


function Stove.SetIngredientAsCooked(ingredient: BasePart): BasePart
	local stepName, stepIdx = CookingService.GetNextIngredientStep(ingredient)
	if stepName ~= script.Name then return ingredient end

	local stepInfo = Ingredients[ingredient.Name].PrepSteps[stepIdx]

	local cookedModel = ReplicatedStorage.Assets.Ingredients:FindFirstChild(stepInfo.StoveType .. ingredient.Name)
	if not cookedModel then return ingredient end

	local newIngredient = cookedModel:Clone()
	ingredient = CookingService.ReplaceIngredientModel(ingredient, newIngredient)

	ingredient:SetAttribute('Stoving', nil)
	ingredient:SetAttribute('Stove', true)
	ingredient:SetAttribute(stepInfo.StoveType, true)
	
	CookingService.UpdateSurfaceIngredientDescription(ingredient)
	
	CookingService.FinishedCooking:Fire()

	return ingredient
end


function Stove.SetIngredientAsBurnt(ingredient: BasePart): BasePart
	if not Stove.IsActionComplete(ingredient) then
		ingredient = Stove.SetIngredientAsCooked(ingredient)
	end
	
	local burntModel = ReplicatedStorage.Assets.Ingredients:FindFirstChild('Burnt' .. ingredient.Name)
	if not burntModel then warn(ingredient.Name, 'BURN MODEL NOT FOUND') return ingredient end

	local newIngredient = burntModel:Clone()
	ingredient = CookingService.ReplaceIngredientModel(ingredient, newIngredient)

	ingredient:SetAttribute('Stoving', nil)
	ingredient:SetAttribute('Stove', true)

	ingredient:SetAttribute('Burned', true)
	
	CookingService.UpdateSurfaceIngredientDescription(ingredient)

	return ingredient
end


function Stove.IsActionComplete(ingredient: BasePart, stepNum: number?)
	local ingredientInfo = Ingredients[ingredient.Name]
	if stepNum and not ingredient:GetAttribute(ingredientInfo.PrepSteps[stepNum].StoveType) then
		return false
	end

	return ingredient:GetAttribute('Stove')
end


function Stove.CanInteract(plr: Player, interactable: BasePart): boolean
	local ingredientOnInteractable = CookingService.GetIngredientOnInteractable(interactable)

	if ingredientOnInteractable then
		local _, stepIdx = CookingService.GetNextIngredientStep(ingredientOnInteractable)
		if Stove.IsActionComplete(ingredientOnInteractable, stepIdx - 1) then return true end
	end

	local HeldItem = CookingService.GetHeldItem(plr)

	if not HeldItem then return end

	if table.find(Stove.StoveContainers, HeldItem.Name) then
		return true
	end

	return false
end


function Stove.StopInteract(plr: Player)

end


function Stove._Init()
	local function handleStove(stove: BasePart)
		if stove:FindFirstChild'Pot' and stove.Pot:FindFirstChild'Water' then
			stove.Pot.Water.Transparency = 1
		end
	end
	
	for _, v in CollectionService:GetTagged(Client.Immutable.Tags.STOVE) do
		handleStove(v)
	end
	CollectionService:GetInstanceAddedSignal(Client.Immutable.Tags.STOVE):Connect(handleStove)
end


return Stove