local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')
local TeleportService = game:GetService('TeleportService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)

type LobbyData = {
	Map: string,
	Act: number,
	FriendsOnly: boolean,
	Difficulty: string,

	Host: Player?,
	Players: {Player}?,

	StorySpace: Folder,

	TargetPlaceID: number?
}

local MatchmakingService = {
	Lobbies = {} :: { Host: Player, Players: {Player} }
}


function MatchmakingService.GetPlayerLobby(plr: Player): number
	for idx, v in MatchmakingService.Lobbies do
		if (v.Players and table.find(v.Players, plr)) or (v.Host == plr) then
			return idx
		end
	end
end


function MatchmakingService.GetEmptyStorySpace()
	local StorySpaces = workspace.Map.StorySpaces

	for _, v in StorySpaces:GetChildren() do
		local IsInUse = false
		for _, lobbyData in MatchmakingService.Lobbies do
			if lobbyData.StorySpace == v then
				IsInUse = true
				break
			end
		end

		if not IsInUse then
			return v
		end
	end
end


function MatchmakingService.CreateLobby(lobbyData: LobbyData, plr: Player)
	local LobbyInfo = {
		Map = lobbyData.Map,
		Act = lobbyData.Act,
		FriendsOnly = lobbyData.FriendsOnly,
		Difficulty = lobbyData.Difficulty,

		Players = {plr},
		Host = plr,

		StorySpace = MatchmakingService.GetEmptyStorySpace()
	}

	local targetPlaceID
	for pid, v in Client.Immutable.Servers.PLACES[game.GameId] do
		if v == lobbyData.Map then
			targetPlaceID = pid
			break
		end
	end

	LobbyInfo.TargetPlaceID = targetPlaceID

	table.insert(MatchmakingService.Lobbies, LobbyInfo)

	local SurfaceUI = LobbyInfo.StorySpace.Barrier.SurfaceGui.Container
	SurfaceUI.ActNumber.Visible = true
	SurfaceUI.StageName.Visible = true
	SurfaceUI.Effect.Visible = true
	SurfaceUI.ActNumber.Text = `ACT {LobbyInfo.Act}`
	SurfaceUI.StageName.Text = LobbyInfo.Map:upper()
	SurfaceUI.Effect.Text = `DIFFICULTY: {LobbyInfo.Difficulty:upper()}`
	SurfaceUI.Holder.Txt.Text = `{#LobbyInfo.Players}/4 Players`

	Packets.Matchmaking.LobbyUpdated.sendToAll({
		Act = LobbyInfo.Act,
		Host = plr.Name,
		Map = LobbyInfo.Map,
		Players = LobbyInfo.Players,
		Difficulty = LobbyInfo.Difficulty,
		FriendsOnly = LobbyInfo.FriendsOnly,
		UserAdded = plr.Name
	})

	plr:SetAttribute('InALobby', true)
	plr.Character.PrimaryPart.CFrame = LobbyInfo.StorySpace.Area.CFrame
end


function MatchmakingService.JoinLobby(hostName: string, plr: Player): boolean
	if MatchmakingService.GetPlayerLobby(plr) then return end

	local HostPlayer = Players:FindFirstChild(hostName)
	if HostPlayer then
		local HostLobby = MatchmakingService.GetPlayerLobby(HostPlayer)
		if HostLobby then
			local LobbyInfo = MatchmakingService.Lobbies[HostLobby]

			if #LobbyInfo.Players >= 4 then return end

			if LobbyInfo.FriendsOnly and not plr:IsFriendsWith(HostPlayer.UserId) then warn'NOT FRIENDS WITH HOST' return end

			plr:SetAttribute('InALobby', true)
			table.insert(LobbyInfo.Players, plr)

			local SurfaceUI = LobbyInfo.StorySpace.Barrier.SurfaceGui.Container
			SurfaceUI.ActNumber.Text = `ACT {LobbyInfo.Act}`
			SurfaceUI.StageName.Text = LobbyInfo.Map:upper()
			SurfaceUI.Effect.Text = `DIFFICULTY: {LobbyInfo.Difficulty:upper()}`
			SurfaceUI.Holder.Txt.Text = `{#LobbyInfo.Players}/4 Players`

			Packets.Matchmaking.LobbyUpdated.sendToAll({
				Act = LobbyInfo.Act,
				Host = HostPlayer.Name,
				Map = LobbyInfo.Map,
				Players = LobbyInfo.Players,
				Difficulty = LobbyInfo.Difficulty,
				FriendsOnly = LobbyInfo.FriendsOnly,
				UserAdded = plr.Name
			})

			plr.Character.PrimaryPart.CFrame = LobbyInfo.StorySpace.Area.CFrame
			
			return true
		end
	end
end


function MatchmakingService.LeaveLobby(_, plr: Player)
	local ThisPlayerLobby = MatchmakingService.GetPlayerLobby(plr)
	if ThisPlayerLobby then
		local LobbyInfo = MatchmakingService.Lobbies[ThisPlayerLobby]
		
		plr:SetAttribute('InALobby', false)
		table.remove(LobbyInfo.Players, table.find(LobbyInfo.Players, plr))

		-- TODO: Delete if host leaves
		Packets.Matchmaking.LobbyUpdated.sendToAll({
			Act = LobbyInfo.Act,
			Host = plr.Name,
			Map = LobbyInfo.Map,
			Players = LobbyInfo.Players,
			Difficulty = LobbyInfo.Difficulty,
			FriendsOnly = LobbyInfo.FriendsOnly,
			UserRemoved = plr.Name
		})

		if plr.Character and plr.Character.PrimaryPart then
			plr.Character.PrimaryPart.CFrame = workspace.Map.Areas.Play.CFrame
		end

		local SurfaceUI = LobbyInfo.StorySpace.Barrier.SurfaceGui.Container
		if #LobbyInfo.Players == 0 then
			SurfaceUI.ActNumber.Visible = false
			SurfaceUI.StageName.Visible = false
			SurfaceUI.Effect.Visible = false
			SurfaceUI.Holder.Txt.Text = 'Waiting For Players'
			
			table.remove(MatchmakingService.Lobbies, ThisPlayerLobby)
		else
			SurfaceUI.ActNumber.Text = `ACT {LobbyInfo.Act}`
			SurfaceUI.StageName.Text = LobbyInfo.Map:upper()
			SurfaceUI.Effect.Text = `DIFFICULTY: {LobbyInfo.Difficulty:upper()}`
			SurfaceUI.Holder.Txt.Text = `{#LobbyInfo.Players}/4 Players`
		end
	end
end


function MatchmakingService.Teleport(lobbyIdx: number)
	local thisLobbyInfo = MatchmakingService.Lobbies[lobbyIdx]

	local code, privateServerID = TeleportService:ReserveServer(thisLobbyInfo.TargetPlaceID)

	local TeleportUI = ReplicatedStorage.Assets.Visuals.TeleportUI:Clone()
	TeleportUI.Frame.Container.Title.Text = `Teleporting To {thisLobbyInfo.Map}...`
	TeleportUI.Frame.Background.Image = Client.Immutable.Servers.PLACE_INFO[thisLobbyInfo.Map].MapImage

	local success, err = pcall(TeleportService.TeleportToPrivateServer, TeleportService,
		thisLobbyInfo.TargetPlaceID, code, thisLobbyInfo.Players, nil, {
			ExpectedPlayers = #thisLobbyInfo.Players,
			Act = thisLobbyInfo.Act,
			Difficulty = thisLobbyInfo.Difficulty
		}, TeleportUI
	)
	if not success then
		warn(err)


	end
end


function MatchmakingService._PlayerAdded(plr: Player)
	local DataService = Server.GetService('DataService')
	
	local profile = DataService.GetProfile(plr)
	
	local mapStats = profile.Analytics.MapStats
	for map, mapRate in mapStats do
		local actsCompleted = 0
		for k, v in mapRate do
			actsCompleted += v.Wins >= 1 and 1 or 0
		end
		
		local val = Instance.new('IntValue')
		val.Name = `Act{map}`
		val.Value = actsCompleted + 1
		val.Parent = plr
	end
end


function MatchmakingService._PlayerRemoving(plr: Player)
	MatchmakingService.LeaveLobby(nil, plr)
end


function MatchmakingService._Start()
	Packets.Matchmaking.CreateLobby.listen(MatchmakingService.CreateLobby)

	Packets.Matchmaking.JoinLobby.listen(MatchmakingService.JoinLobby)
	Packets.Matchmaking.LeaveLobby.listen(MatchmakingService.LeaveLobby)

	Packets.Matchmaking.Start.listen(function(_, plr: Player)
		local ThisLobbyIdx = MatchmakingService.GetPlayerLobby(plr)
		if ThisLobbyIdx then
			MatchmakingService.Teleport(ThisLobbyIdx)
		end
	end)
	
	Packets.Matchmaking.InvitePlayer.listen(function(data: { PlayerName: string }, plr: Player)
		local TargetPlayer = Players:FindFirstChild(data.PlayerName)
		if TargetPlayer then
			local ThisPlayerLobby = MatchmakingService.GetPlayerLobby(plr)
			if ThisPlayerLobby then
				local LobbyInfo = MatchmakingService.Lobbies[ThisPlayerLobby]
				
				if #LobbyInfo.Players >= 4 then return end
				
				Packets.Matchmaking.InvitePlayer.sendTo({
					PlayerName = plr.Name,
					LobbyData = {
						Act = LobbyInfo.Act,
						Difficulty = LobbyInfo.Difficulty,
						MapName = LobbyInfo.Map,
						Host = LobbyInfo.Host.Name
					}
				}, TargetPlayer)
			end
		end
	end)

	if Client.Immutable.SERVER_TYPE == 'Lobby' then
		for _, v in workspace:WaitForChild'Map'.StorySpaces:GetChildren() do
			v.Barrier.CanTouch = true

			local SurfaceUI = v.Barrier.SurfaceGui.Container
			SurfaceUI.ActNumber.Visible = false
			SurfaceUI.StageName.Visible = false
			SurfaceUI.Effect.Visible = false
			SurfaceUI.Holder.Txt.Text = 'Waiting For Players'

			v.Barrier.Touched:Connect(function(hit: BasePart)
				local Humanoid = hit.Parent:FindFirstChild'Humanoid' or hit.Parent.Parent:FindFirstChild'Humanoid'
				if Humanoid then
					local ThisPlayer = Players:GetPlayerFromCharacter(Humanoid.Parent)
					if ThisPlayer and not ThisPlayer:GetAttribute'InALobby' then
						local joined = false
						
						for _, lobbyInfo in MatchmakingService.Lobbies do
							if lobbyInfo.StorySpace == v then
								joined = MatchmakingService.JoinLobby(lobbyInfo.Host.Name, ThisPlayer)
								break
							end
						end
						
						if not joined then
							Packets.Matchmaking.PromptLobbyUI.sendTo(nil, ThisPlayer)
						end
					end
				end
			end)
		end
	end
end


return MatchmakingService