local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)

local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)

local PassiveModule = {}


function PassiveModule.Get(plr: Player)
	local default = Server.Immutable.THROW_FORCE

	if plr:GetAttribute('VampiricAgility') then
		default *= 1.25
	end
	
	if plr:GetAttribute('SpatialDistortion') then
		default *= 1.3
	end
	
	if plr:GetAttribute('Character') == 'Vash the Stampede' then
		default *= 1.1
	elseif plr:GetAttribute('Character') == 'Kenshiro' then
		default *= 1.1
	elseif plr:GetAttribute('Character') == 'Gon Freecss' then
		default *= 1.1
	elseif plr:GetAttribute('Character') == '<PERSON> Spiegel' then
		default *= 1.1
	elseif plr:GetAttribut<PERSON>('Character') == '<PERSON><PERSON><PERSON> Ackerman' then
		default *= 1.1
	elseif plr:GetAttribute('Character') == '<PERSON>ru Gojo' then
		default *= 1.3
	elseif plr:GetAttribute('Character') == 'Levi Ackerman' then
		default *= 1.1
	end
	
	for _, v in Players:GetPlayers() do
		if v:GetAttribute'FlameSurge' then
			default *= 1.2
		end
	end
	
	return default
end


function PassiveModule.Init()
	
end


return PassiveModule