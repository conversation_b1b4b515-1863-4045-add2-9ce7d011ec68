local ReplicatedStorage = game:GetService('ReplicatedStorage')
local SoundService = game:GetService('SoundService')
local TweenService = game:GetService('TweenService')
local RunService = game:GetService('RunService')

local Client = require(ReplicatedStorage.Client)

local MusicController = {}


function MusicController._Start()
	local LoadingController = Client.GetController('LoadingController')
	
	local PlaceName = Client.Immutable.Servers.PLACES[game.GameId][game.PlaceId]
	
	LoadingController.LoadingFrameFadedAway:Connect(function()
		local ThisPlaceSound = SoundService.Music:FindFirstChild(PlaceName)
		if ThisPlaceSound then
			local originalVolume = ThisPlaceSound.Volume
			ThisPlaceSound:SetAttribute('OriginalVolume', originalVolume)
			
			ThisPlaceSound.Volume = 0
			
			ThisPlaceSound.Looped = true
			ThisPlaceSound:Play()
			
			TweenService:Create(
				ThisPlaceSound,
				TweenInfo.new(.8, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
				{ Volume = originalVolume }
			):Play()
			
			for _, v in SoundService:GetChildren() do
				if v:IsA'SoundGroup' then
					v.Volume = 1
				end
			end
		else
			warn('Music not found for map', PlaceName)
		end
	end)
end


function MusicController._Init()
	if RunService:IsStudio() then return end
	
	for _, v in SoundService:GetChildren() do
		if v:IsA'SoundGroup' then
			v.Volume = 0
		end
	end
end


return MusicController