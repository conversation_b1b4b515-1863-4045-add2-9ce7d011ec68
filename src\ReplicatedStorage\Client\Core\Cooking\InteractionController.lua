--!native

local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local TweenService = game:GetService('TweenService')
local RunService = game:GetService('RunService')
local UserInputService = game:GetService('UserInputService')
local ContextActionService = game:GetService('ContextActionService')
local CollectionService = game:GetService('CollectionService')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local Signal = require(ReplicatedStorage.SharedModules.Signal)


local InteractionController = {
	InteractHighlight = ReplicatedStorage.Assets.Visuals.Interact,
	ClosestInteractable = nil :: Instance?
}


local function locateInteractableWithValidTag(source: BasePart | Model, lastValid: Instance?): (BasePart | Model)?
	if source == workspace then return lastValid end
	
	for _, v in source:GetTags() do
		if table.find(Client.Immutable.VALID_INTERACTABLES, v) then
			lastValid = source
			break
		end
	end

	return locateInteractableWithValidTag(source.Parent, lastValid)
end


local function GetInteractablesInRegion(origin: Vector3): {Instance}
	local RegionParams: OverlapParams do
		RegionParams = OverlapParams.new()
		for _, v in Client.Immutable.VALID_INTERACTABLES do
			RegionParams:AddToFilter(CollectionService:GetTagged(v))
		end
		RegionParams.FilterType = Enum.RaycastFilterType.Include
	end

	return workspace:GetPartBoundsInRadius(origin, 5, RegionParams)
end


function InteractionController.GetClosestInteractable(Origin: Vector3, Interactables: {BasePart}?): BasePart?
	if not Interactables then
		Interactables = GetInteractablesInRegion(Origin)
	end
	
	local lastDistance = math.huge
	local Closest_Interactable: BasePart = nil

	for _, Interactable: BasePart in Interactables do
		local valid = false
		for _, tag in Interactable:GetTags() do
			if table.find(Client.Immutable.VALID_INTERACTABLES, tag) then
				valid = true
				break
			end
		end
		
		if not valid then continue end
		
		local direction = Interactable.Position - Origin
		local adjustedInteractablePosition = Interactable.Position --[[math.abs(direction.X) > math.abs(direction.Z)
			and Vector3.new(math.sign(direction.X) * Interactable.Size.X, 0, 0) or Vector3.new(0, 0, math.sign(direction.Z) * Interactable.Size.Z)
		]]
		if Closest_Interactable then
			local Distance = (Origin - adjustedInteractablePosition).Magnitude
			if Distance < lastDistance then
				lastDistance = Distance
				Closest_Interactable = Interactable
			end
		else
			lastDistance = (Origin - adjustedInteractablePosition).Magnitude
			Closest_Interactable = Interactable
		end
	end

	return Closest_Interactable
end


function InteractionController._RenderInteractions(dt: number)
	local character = Players.LocalPlayer.Character
	
	if not character or not character.PrimaryPart then return end

	local InteractablesTable = GetInteractablesInRegion(character.HumanoidRootPart.Position)
	if #InteractablesTable > 0 then
		local ClosestInteractable = InteractionController.GetClosestInteractable(character.HumanoidRootPart.Position, InteractablesTable)
		if ClosestInteractable and InteractionController.ClosestInteractable ~= ClosestInteractable then
			ClosestInteractable = locateInteractableWithValidTag(ClosestInteractable) or ClosestInteractable
			
			if InteractionController.ClosestInteractable ~= ClosestInteractable then
				Packets.Interaction.NearestInteraction.send(ClosestInteractable)
			end
			
			InteractionController.ClosestInteractable = ClosestInteractable
			
			InteractionController.SetHighlight(true, ClosestInteractable)
		end
	else
		if InteractionController.ClosestInteractable ~= nil then
			Packets.Interaction.NearestInteraction.send(nil)
		end

		InteractionController.ClosestInteractable  = nil
		InteractionController.SetHighlight(false)
	end
end


function InteractionController.SetHighlight(set: boolean, object: (BasePart | Model)?)
	if set then
		InteractionController.InteractHighlight.FillTransparency = 1
		InteractionController.InteractHighlight.Adornee = object

		TweenService:Create(InteractionController.InteractHighlight,
			TweenInfo.new(.2,Enum.EasingStyle.Back, Enum.EasingDirection.Out),
			{FillTransparency = .6}
		):Play()
	else
		InteractionController.InteractHighlight.Adornee = nil
	end
end


function InteractionController.Interact()
	if not InteractionController.ClosestInteractable then return end

	Packets.Interaction.Interact.send(InteractionController.ClosestInteractable)
end


function InteractionController._Start()
	RunService.RenderStepped:Connect(InteractionController._RenderInteractions)
	
	Client.GetController'InputController'.AlternativeInputs.TapOrClick:Connect(InteractionController.Interact)
	
	UserInputService.InputBegan:Connect(function(input: InputObject)
		if UserInputService:GetFocusedTextBox() then return end
		if input.KeyCode == Enum.KeyCode.E then
			InteractionController.Interact()
		end
	end)
	
	UserInputService.JumpRequest:Connect(function()
		Packets.Interaction.CancelInteraction.send()
	end)
end


function InteractionController._Init()
	InteractionController.InteractHighlight.Parent = workspace.CurrentCamera
end


return InteractionController