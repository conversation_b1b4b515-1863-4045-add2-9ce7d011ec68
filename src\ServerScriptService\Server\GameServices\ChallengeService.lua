local ServerScriptService = game:GetService('ServerScriptService')
local ReplicatedStorage = game:GetService('ReplicatedStorage')
local TeleportService = game:GetService('TeleportService')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)
local Server = require(ServerScriptService.Server)

local Packets = require(ReplicatedStorage.Data.Packets)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local TableUtil = require(ReplicatedStorage.SharedModules.TableUtil)
local ItemsData = require(ReplicatedStorage.Data.Items)

local ChallengeService = {
	Challenge = nil :: string?,
	Modifiers = {},

	ChallengeQueues = {} :: { [string]: { TimerStart: number, Players: {Player} } }
}



local function getChallengeMap(challenge: string): string
	local challengeBytes = 0
	for c in challenge:gmatch'.' do
		challengeBytes += c:byte()
	end

	local n = tonumber(ClassExtension.time.CreateHourlyHash(os.time())) + challengeBytes
	local rngMap = Client.Immutable.ROUND_MAPS[Random.new(n):NextInteger(1, #Client.Immutable.ROUND_MAPS)]

	return rngMap
end


function ChallengeService.GetChallengeReward(challengeID: string)
	local Rewards = TableUtil.Copy(Client.Immutable.Challenges[challengeID].Rewards, true)

	local ThisMap = getChallengeMap(challengeID)

	for itemName, itemInfo in ItemsData do
		if itemInfo.MapAssociations and table.find(itemInfo.MapAssociations, ThisMap) then
			table.insert(Rewards, {
				Name = itemName,
				Count = 1,
				Reward = {
					Service = 'InventoryService',
					Function = 'AddItem',
					Args = {itemName, 1}
				}
			})
		end
	end

	return Rewards
end


function ChallengeService.TeleportPlayersInChallenge(challengeID: string)
	if not ChallengeService.ChallengeQueues[challengeID] then return end

	local queue = ChallengeService.ChallengeQueues[challengeID]
	if #queue.Players == 0 then return end

	local challengeMapName = getChallengeMap(challengeID)

	local placeId
	for pid, placeName in Client.Immutable.Servers.PLACES[game.GameId] do
		if placeName == challengeMapName then
			placeId = pid
			break
		end
	end

	local code, privateServerID = TeleportService:ReserveServer(placeId)

	local TeleportUI = ReplicatedStorage.Assets.Visuals.TeleportUI:Clone()
	TeleportUI.Frame.Container.Title.Text = `Teleporting To {'Restaurant'}...`
	TeleportUI.Frame.Background.Image = Client.Immutable.Servers.PLACE_INFO['Restaurant'].MapImage

	local success, err = pcall(TeleportService.TeleportToPrivateServer, TeleportService,
		placeId, code, queue.Players, nil, {
			ExpectedPlayers = #queue.Players,
			Challenge = challengeID
		}, TeleportUI
	)
end


function ChallengeService.IsInChallengeQueue(plr: Player): string
	for k, v in ChallengeService.ChallengeQueues do
		if table.find(v.Players, plr) then
			return k
		end
	end
end


function ChallengeService.JoinChallenge(plr: Player, challengeID: string)
	if ChallengeService.IsInChallengeQueue(plr) then return end

	local ChallengeBox = workspace.Map.ChallengeSpaces[challengeID]

	plr.Character.PrimaryPart.CFrame = ChallengeBox.Area.CFrame

	local queue = ChallengeService.ChallengeQueues[challengeID]
	table.insert(queue.Players, plr)

	queue.TimerStart = os.clock()

	Packets.Challenge.UpdateChallengeInterface.sendToList({
		Map = getChallengeMap(challengeID),
		ChallengeID = challengeID,
		Players = queue.Players,
		Rewards = ChallengeService.GetChallengeReward(challengeID),
	}, queue.Players)
end


function ChallengeService.LeaveChallenge(plr: Player)
	local playerChallengeQueue = ChallengeService.IsInChallengeQueue(plr)
	if playerChallengeQueue then
		local queue = ChallengeService.ChallengeQueues[playerChallengeQueue]
		
		local UpdatePlayersQueue = TableUtil.Copy(queue.Players)
		
		table.remove(queue.Players, table.find(queue.Players, plr))
		
		Packets.Challenge.UpdateChallengeInterface.sendToList({
			Map = getChallengeMap(playerChallengeQueue),
			ChallengeID = playerChallengeQueue,
			Players = queue.Players,
			Rewards = ChallengeService.GetChallengeReward(playerChallengeQueue),
		}, UpdatePlayersQueue)
		
		if plr.Character and plr.Character.PrimaryPart then
			plr.Character.PrimaryPart.CFrame = workspace.Map.Areas.Challenges.CFrame * CFrame.new(0, 2, 0)
		end
	end
end


function ChallengeService._PlayerAdded(plr: Player)
	local DataService = Server.GetService('DataService')

	if Client.Immutable.SERVER_TYPE == 'Lobby' then return end

	local joinData = plr:GetJoinData()
	if not joinData or not joinData.TeleportData then return end

	local challenge = joinData.TeleportData.Challenge
	ChallengeService.Challenge = challenge or nil

	print(ChallengeService.Challenge)

	if ChallengeService.Challenge then
		ChallengeService.Modifiers = Client.Immutable.Challenges[ChallengeService.Challenge].Modifiers or {}
	end
end


function ChallengeService._PlayerRemoving(plr: Player)
	ChallengeService.LeaveChallenge(plr)
end


function ChallengeService._Start()
	local RoundService = Server.GetService('RoundService')
	local DataService = Server.GetService('DataService')
	local CurrencyService = Server.GetService('CurrencyService')
	local TimeService = Server.GetService('TimeService')

	if Client.Immutable.SERVER_TYPE ~= 'Lobby' then return end

	workspace:WaitForChild'Map'

	for _, v in workspace.Map.ChallengeSpaces:GetChildren() do
		if v:IsA'Folder' then
			local SurfaceUI = v.Barrier.SurfaceGui.Container
			SurfaceUI.ActNumber.Visible = false
			SurfaceUI.Effect.Text = Client.Immutable.Challenges[v.Name].EffectName
			SurfaceUI.StageName.Text = getChallengeMap(v.Name)

			task.spawn(function()
				local ChallengeQueueTbl = ChallengeService.ChallengeQueues[v.Name]
				while true do
					if #ChallengeQueueTbl.Players > 0 then
						local elapsed = os.clock() - ChallengeQueueTbl.TimerStart

						if elapsed >= Client.Immutable.TIME_TO_START_CHALLENGE_MATCHMAKING then
							SurfaceUI.Holder.Txt.Text = 'Teleporting...'
							ChallengeService.TeleportPlayersInChallenge(v.Name)
						end

						local startingIn = math.floor(Client.Immutable.TIME_TO_START_CHALLENGE_MATCHMAKING - elapsed)

						Packets.Challenge.UpdateChallengeInterface.sendToList({
							Map = getChallengeMap(v.Name),
							ChallengeID = v.Name,
							Players = ChallengeQueueTbl.Players,
							StartingIn = startingIn,
							Rewards = ChallengeService.GetChallengeReward(v.Name),
						}, ChallengeQueueTbl.Players)

						SurfaceUI.Holder.Txt.Text = `Starting in {startingIn}s`
					else
						SurfaceUI.Holder.Txt.Text = 'Waiting For Players'
					end

					task.wait(.33)
				end
			end)

			v.Area.Touched:Connect(function(hit: BasePart)
				local Humanoid = hit.Parent:FindFirstChildOfClass'Humanoid' or hit.Parent.Parent:FindFirstChild'Humanoid'
				if Humanoid then
					local plr = Players:GetPlayerFromCharacter(Humanoid.Parent)
					if plr then
						ChallengeService.JoinChallenge(plr, v.Name)
					end
				end
			end)

			v.Area.CanTouch = true
		end
	end

	RoundService.RoundEndedSignal:Connect(function(timerRanOut: boolean?)
		if timerRanOut and ChallengeService.Challenge then
			for _, v in Players:GetPlayers() do
				CurrencyService.AddGems(v, 50, 'Challenge')
			end
		end
	end)

	TimeService.ResetHourly:Connect(function()
		for _, v in workspace.Map.ChallengeSpaces:GetChildren() do
			if v:IsA'Folder' then
				local SurfaceUI = v.Barrier.SurfaceGui.Container
				SurfaceUI.StageName.Text = getChallengeMap(v.Name)
			end
		end
	end)
	
	Packets.Challenge.LeaveChallenge.listen(function(_, plr: Player)
		ChallengeService.LeaveChallenge(plr)
	end)
end


function ChallengeService._Init()
	if Client.Immutable.SERVER_TYPE == 'Lobby' then
		for challenge in Client.Immutable.Challenges do
			ChallengeService.ChallengeQueues[challenge] = {
				TimerStart = 0,
				Players = {}
			}
		end
	end
end


return ChallengeService