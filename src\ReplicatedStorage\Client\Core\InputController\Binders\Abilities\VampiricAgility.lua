local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)
local Packets = require(ReplicatedStorage.Data.Packets)

local VampiricAgility = {
	Key = Enum.KeyCode.V,

	GamepadKey = Enum.KeyCode.ButtonR2
}


function VampiricAgility.KeyDown()
	Packets.Ability.UseAbility.send({
		AbilityName = script.Name,
		Args = {}
	})
end


function VampiricAgility.KeyUp()

end


function VampiricAgility.Click()
	VampiricAgility.KeyDown()
end


function VampiricAgility.IsOnCooldown()
	return Client.GetController'AbilityController'.IsAbilityOnCooldown(script.Name)
end


function VampiricAgility.CanUseAbilityAsCharacter()
	return Client.GetController'AbilityController'.CanUseAbilityAsCharacter(script.Name)
end


function VampiricAgility._Init()

end


return VampiricAgility
