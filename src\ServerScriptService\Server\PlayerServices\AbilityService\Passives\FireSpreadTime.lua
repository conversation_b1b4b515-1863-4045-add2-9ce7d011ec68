local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)

local PassiveModule = {}


function PassiveModule.Get(origin: Vector3?)
	local default = Server.Immutable.FIRE_SPREAD_TIME
	
	if origin then
		for _, v in Players:GetPlayers() do
			local dist = v:DistanceFromCharacter(origin)
			if v:GetAttribute('InfinityBarrier') and dist < 12 then
				default += 6
			elseif v:GetAttribute('Character') == '<PERSON><PERSON>j' and dist < 10 then
				default += 4
			end
		end
	end
	
	return default
end


function PassiveModule.Init()
	
end


return PassiveModule