--[[
	
	Boolean   Keyboard:IsDown(keyCode)
	Boolean   Keyboard:AreAllDown(keyCodes...)
	Boolean   Keyboard:AreAnyDown(keyCodes...)
	
	Keyboard.KeyDown(keyCode)
	Keyboard.KeyUp(keyCode)
	
--]]



local Keyboard = {}

local userInput = game:GetService('UserInputService')
local signal = require(game:GetService'ReplicatedStorage'.SharedModules.Signal)


function Keyboard.IsDown(keyCode)
	return userInput:IsKeyDown(keyCode)
end


function Keyboard.AreAllDown(...)
	for _, keyCode in {...} do
		if not userInput:IsKeyDown(keyCode) then
			return false
		end
	end
	return true
end


function Keyboard.AreAnyDown(...)
	for _, keyCode in {...} do
		if userInput:IsKeyDown(keyCode) then
			return true
		end
	end
	return false
end


function Keyboard.Start()

end


function Keyboard.Init()
	Keyboard.KeyDown = signal.new()
	Keyboard.KeyUp = signal.new()

	userInput.InputBegan:Connect(function(input, processed)
		if processed then return end
		if input.UserInputType == Enum.UserInputType.Keyboard then
			Keyboard.KeyDown:Fire(input.KeyCode)
		end
	end)

	userInput.InputEnded:Connect(function(input, processed)
		if input.UserInputType == Enum.UserInputType.Keyboard then
			Keyboard.KeyUp:Fire(input.KeyCode)
		end
	end)
end


return Keyboard