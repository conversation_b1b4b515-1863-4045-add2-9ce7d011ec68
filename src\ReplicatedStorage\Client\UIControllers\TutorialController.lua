local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)

local TutorialController = {}


function TutorialController._Start()
	local DialogController = Client.GetController('DialogController')
	local UIController = Client.GetController('UIController')
	
	Packets.Tutorial.TutorialCompleted.listen(function()
		print('done!')
		DialogController.SetDialog({
			Header = 'Master Chifu',
			Description = 'Congratulations on completing the tutorial! You have been awarded 50 gems!\nWould you like to redo the tutorial?',
			Yes = {
				Text = 'YES',
				Callback = function()
					Packets.Tutorial.DoTutorial.send()
				end
			},
			No = {
				Text = 'NO',
				Callback = function()
					Packets.Tutorial.ReturnToLobby.send()
				end
			}
		})
	end)
	
	if Client.Immutable.SERVER_TYPE ~= 'Lobby' then return end
	
	Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'GameplayUI'
	
	Packets.Tutorial.GetRoundsCompleted.listen(function(data: number)
		UIController.Close()
		
		print(data, 'ahaha!!')
		if data < 1 then
			DialogController.SetDialog({
				Header = 'Master Chifu',
				Description = 'Welcome to Anime Chefs! Would you like to play the tutorial?',
				Yes = {
					Text = 'YES',
					Callback = function()
						Packets.Tutorial.DoTutorial.send()
					end
				},
				No = {
					Text = 'NO',
					Callback = function()
						
					end
				}
			})
		end
	end)
	Packets.Tutorial.GetRoundsCompleted.send()
end


return TutorialController