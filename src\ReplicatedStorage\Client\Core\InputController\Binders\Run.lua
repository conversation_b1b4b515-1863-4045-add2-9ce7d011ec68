local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)

local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsClient)

local Run = {
	Key = Enum.KeyCode.LeftShift,

	GamepadKey = Enum.KeyCode.ButtonY,
	
	Running = false
}


function Run.KeyDown()
	if Client.Immutable.SERVER_TYPE ~= 'Lobby' then return end
	
	Run.Running = true
	Players.LocalPlayer.Character.Humanoid.WalkSpeed = Client.Immutable.CharSettings.LOBBY_RUN_SPEED
end


function Run.KeyUp()
	if Client.Immutable.SERVER_TYPE ~= 'Lobby' then return end
	
	Run.Running = false
	Players.LocalPlayer.Character.Humanoid.WalkSpeed = Client.Immutable.CharSettings.LOBBY_WALK_SEED
end


function Run._Init()
	if Client.Immutable.SERVER_TYPE ~= 'Lobby' then return end
	
	local function charAdded()
		Run.Running = false
		
		Players.LocalPlayer.Character:WaitForChild'Humanoid'.WalkSpeed = Client.Immutable.CharSettings.LOBBY_WALK_SEED
		
		Animations:AwaitPreloadAsyncFinished()
		
		Players.LocalPlayer.Character.Humanoid.Running:Connect(function(speed: number)
			local isTrackPlaying = Animations:GetTrack('Run') and Animations:GetTrack('Run').IsPlaying
			if speed > 16 and Run.Running and not isTrackPlaying then
				Animations:PlayTrack('Run', .2)
			elseif (not Run.Running or speed < 16) and isTrackPlaying then
				Animations:StopTrack('Run', .2)
			end
		end)
	end

	if Players.LocalPlayer.Character then
		task.defer(charAdded)
	end
	Players.LocalPlayer.CharacterAdded:Connect(charAdded)
end


return Run
