local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local ServerScriptService = game:GetService('ServerScriptService')

local Server = require(ServerScriptService.Server)

local QuestData = require(ReplicatedStorage.Data.Quests)
local Packets = require(ReplicatedStorage.Data.Packets)

local QuestService = {}


function QuestService.ClaimQuest(plr: Player, category: string, questID: string)
	local DataService = Server.GetService('DataService')

	local thisQuestData = QuestData.FindQuestFromCategoryAndID(category, questID)
	if thisQuestData and QuestService.IsQuestCompleted(plr, category, questID) and not QuestService.IsQuestClaimed(plr, category, questID) then
		local PlayerQuestData = DataService.Get(plr, 'Quests')
		
		for _, v in thisQuestData.Rewards do
			Server.GetService(v.Service)[v.Function](plr, table.unpack(v.Args))
		end
		
		PlayerQuestData.Claimed[category][questID] = true
		
		QuestService.UpdateQuestProgress(plr, questID, 0)
	end
end


function QuestService.IsQuestCompleted(plr: Player, category: string, questID: string): boolean
	local DataService = Server.GetService('DataService')
	
	local thisQuestData = QuestData.FindQuestFromCategoryAndID(category, questID)
	if thisQuestData then
		return QuestService.GetQuestProgress(plr, category, questID) >= thisQuestData.Requirement
	end
	
	return false
end


function QuestService.IsQuestClaimed(plr: Player, category: string, questID: string): boolean
	local DataService = Server.GetService('DataService')
	return DataService.Get(plr, 'Quests').Claimed[category][questID]
end


function QuestService.GetQuestProgress(plr: Player, category: string, questID: string)
	local DataService = Server.GetService('DataService')
	return DataService.Get(plr, 'Quests')[category][questID]
end


function QuestService.GetQuestRequirement(plr: Player, category: string, questID: string)
	local DataService = Server.GetService('DataService')
	local thisQuestData = QuestData.FindQuestFromCategoryAndID(category, questID)
	return thisQuestData.Requirement
end


function QuestService.UpdateQuestProgress(plr: Player, questID: string, increment: number)
	local DataService = Server.GetService('DataService')

	local PlayerQuestData = DataService.Get(plr, 'Quests')

	for category in PlayerQuestData do
		if category == 'Claimed' then continue end

		for id, value in PlayerQuestData[category] do
			if id == questID then
				PlayerQuestData[category][id] += increment

				Packets.Quest.RequestQuestData.sendTo({
					Category = category,
					QuestID = questID,
					Progress = QuestService.GetQuestProgress(plr, category, questID),
					Claimed = PlayerQuestData.Claimed[category][questID]
				}, plr)
			end
		end
	end
end


function QuestService._PlayerAdded(plr: Player)
	local DataService = Server.GetService('DataService')

	local PlayerQuestData = DataService.Get(plr, 'Quests')

	for category, v in QuestData do
		if type(v) ~= 'table' then continue end

		if not PlayerQuestData.Claimed[category] then
			PlayerQuestData.Claimed[category] = {}
		end

		for _, quest in v do
			if not PlayerQuestData.Claimed[category][quest.ID] then
				PlayerQuestData.Claimed[category][quest.ID] = false
			end

			if not PlayerQuestData[category][quest.ID] then
				PlayerQuestData[category][quest.ID] = 0
			end
		end
	end
end


function QuestService._Start()
	local TimeService = Server.GetService('TimeService')
	local DataService = Server.GetService('DataService')
	
	TimeService.ResetDaily:Connect(function(plr: Player)
		local ClaimedQuests = DataService.Get(plr, 'Quests').Claimed
		table.clear(ClaimedQuests.Daily)
	end)
	
	TimeService.ResetWeekly:Connect(function(plr: Player)
		local ClaimedQuests = DataService.Get(plr, 'Quests').Claimed
		table.clear(ClaimedQuests.Weekly)
	end)
	
	Packets.Quest.RequestQuestData.listen(function(_, plr: Player)
		for category, v in QuestData do
			if type(v) ~= 'table' then continue end

			for _, quest in v do
				QuestService.UpdateQuestProgress(plr, quest.ID, 0)
			end
		end
	end)

	Packets.Quest.ClaimQuest.listen(function(data: { Category: string, QuestID: string }, plr: Player)
		QuestService.ClaimQuest(plr, data.Category, data.QuestID)
	end)
	
	Packets.Quest.ClaimAllQuests.listen(function(_, plr: Player)
		for category, v in QuestData do
			if type(v) ~= 'table' then continue end

			for _, quest in v do
				QuestService.ClaimQuest(plr, category, quest.ID)
			end
		end
	end)
end


return QuestService