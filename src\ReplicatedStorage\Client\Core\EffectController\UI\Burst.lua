local UserInputService = game:GetService('UserInputService')
local Debris = game:GetService('Debris')
local GuiService = game:GetService('GuiService')
local RunService = game:GetService('RunService')
local StarterGui = game:GetService('StarterGui')


local function getPossibleColor(btn: GuiButton): (UIGradient, ColorSequence)
	local uigrad = btn:FindFirstChildOfClass('UIGradient')
	local gradient = uigrad and uigrad.Color or ColorSequence.new(btn.BackgroundColor3)

	if btn.Parent:IsA'GuiObject' and btn.BackgroundTransparency >= 1 and not uigrad then
		local uigrad = btn.Parent:FindFirstChildOfClass('UIGradient')
		gradient = uigrad and uigrad.Color or ColorSequence.new(btn.Parent.BackgroundColor3)
		if btn.Parent.Parent:IsA'GuiObject' and btn.Parent.BackgroundTransparency >= 1 and not uigrad then
			local uigrad = btn.Parent.Parent:FindFirstChildOfClass('UIGradient')
			gradient = uigrad and uigrad.Color or ColorSequence.new(btn.Parent.Parent.BackgroundColor3)
		end
	end
	
	local InnerImage = btn:FindFirstChildOfClass('ImageLabel')
	if InnerImage and InnerImage.Size.X.Scale > .5 and InnerImage.Size.Y.Scale > .5 and InnerImage:FindFirstChildOfClass('UIGradient') then
		local uigrad = InnerImage:FindFirstChildOfClass('UIGradient')
		gradient = uigrad and uigrad.Color or ColorSequence.new(InnerImage.ImageColor3)
	end
	
	return uigrad, gradient
end


return function(btn: GuiButton, useMousePosition: boolean?)
	local Container = RunService:IsRunning() and game:GetService'Players'.LocalPlayer.PlayerGui.Main.ScreenPopup or StarterGui.Main.ScreenPopup
	
	if btn:IsDescendantOf(workspace) then
		btn = btn:Clone()
		btn.BackgroundTransparency = 1

		if btn:IsA'TextButton' then
			btn.TextTransparency = 1
		else
			btn.ImageTransparency = 1
		end

		btn.Parent = Container

		for _, v in btn:GetChildren() do
			if v:IsA'GuiObject' then v:Destroy() end
		end

		Debris:AddItem(btn, .1)
	end

	if UserInputService.GamepadEnabled or not (btn:IsA'TextButton' or btn:IsA'ImageButton' or btn:IsA'TextLabel' or btn:IsA'Frame') then return end

	-- Find gradient most likely for it. Could've used recursion but whatever.
	local uigrad, gradient = getPossibleColor(btn)

	local mouseLoc = UserInputService:GetMouseLocation()
	local Position = useMousePosition and UDim2.new(0, mouseLoc.X, 0, mouseLoc.Y) or UDim2.fromOffset(btn.AbsolutePosition.X, btn.AbsolutePosition.Y)

	local ScreenGui = Instance.new('ScreenGui')
	ScreenGui.DisplayOrder = 9999
	ScreenGui.IgnoreGuiInset = false
	ScreenGui.Parent = Container

	for i = 1, math.random(11, 15) do
		coroutine.wrap(function()
			local c = script.Cloud:Clone()

			local randRotation = math.random(-90, 90)
			if uigrad then
				c.UIGradient.Rotation = uigrad.Rotation + randRotation
			end
			
			c.UIGradient.Color = gradient
			c.Position = Position
			c.Parent = ScreenGui
			c.Rotation = randRotation

			local TargetPosition = c.Position + UDim2.fromScale(math.random(-4, 4)/math.random(80, 100), math.random(-4, 4)/math.random(80, 100))
			c:TweenSizeAndPosition(UDim2.new(), TargetPosition, 'Out', 'Quint', .6, true)
		end)()
	end

	Debris:AddItem(ScreenGui, .35)
end