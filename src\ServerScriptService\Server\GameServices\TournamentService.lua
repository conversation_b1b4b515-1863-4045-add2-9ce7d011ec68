local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local TeleportService = game:GetService('TeleportService')
local DataStoreService = game:GetService('DataStoreService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Table = require(ReplicatedStorage.SharedModules.TableUtil)

local TournamentService = {
	FIRST_TOURNAMENT = DateTime.fromUniversalTime(2025, 08, 1, 0, 0, 0, 0),
	TournamentInfo = {
		MapName = nil,
		Modifiers = {},
		TournamentNumber = 0
	},
	
	ModifierValues = {},
	
	LeaderboardRankings = {}
}


local function loadTournamentInfo()
	local DataService = Server.GetService('DataService')
	
	local MonthlyHash = ClassExtension.time.CreateMonthlyHash(os.time())
	
	local rng = Random.new(tonumber(MonthlyHash))
	
	local TournamentsInfo = Client.Immutable.Tournament
	
	TournamentService.TournamentInfo.MapName = TournamentsInfo.POSSIBLE_MAPS[rng:NextInteger(1, #TournamentsInfo.POSSIBLE_MAPS)]
	
	local ModifiersCount = rng:NextInteger(1, #TournamentsInfo.MODIFIERS)
	while #TournamentService.TournamentInfo.Modifiers < ModifiersCount do
		local idx = rng:NextInteger(1, #TournamentsInfo.MODIFIERS)
		local Modifier = TournamentsInfo.MODIFIERS[idx].Name
		if not table.find(TournamentService.TournamentInfo.Modifiers, Modifier) then
			table.insert(TournamentService.TournamentInfo.Modifiers, Modifier)
			TournamentService.ModifierValues[TournamentsInfo.MODIFIERS[idx].ModifierKey] = TournamentsInfo.MODIFIERS[idx].Value
		end
	end
	
	do -- tournament number
		local startDate = DateTime.fromUniversalTime(2025, 8, 1, 0, 0, 0, 0)
		local currentDate = DateTime.now()

		local startY, startM = startDate:ToUniversalTime().Year, startDate:ToUniversalTime().Month
		local currY, currM = currentDate:ToUniversalTime().Year, currentDate:ToUniversalTime().Month

		local totalMonths = (currY - startY) * 12 + (currM - startM)

		if currentDate:ToUniversalTime().Day < startDate:ToUniversalTime().Day then
			totalMonths -= 1
		end

		TournamentService.TournamentInfo.TournamentNumber = math.max(totalMonths + 1, 1)
	end
	
	Packets.Tournament.GetTournamentInfo.sendToAll(TournamentService.TournamentInfo)
	
	for i = TournamentService.TournamentInfo.TournamentNumber, 1, -1 do
		local ThisDatastore = DataStoreService:GetOrderedDataStore(
			Server.Immutable.Data.DATASTORE_HASHED,
			`Tournament{i}`
		)
		
		TournamentService.LeaderboardRankings[i] = {}
		
		local pages = ThisDatastore:GetSortedAsync(false, 100, 1)
		local index = 0
		for _ = 1, 10 do
			for rank, plrData in next, pages:GetCurrentPage() do
				print('tournament', rank, plrData.value)
				
				local value = plrData.value
				local UserId = tonumber(tostring(plrData.key):match'%d+')
				
				index += 1
				
				TournamentService.LeaderboardRankings[i][tostring(UserId)] = {
					Rank = index,
					Value = value
				}
				
				local plr = Players:GetPlayerByUserId(UserId)
				if plr then
					task.spawn(function()
						local profile = DataService.GetProfile(plr)
						profile.Tournament.PreviousRankings[tostring(i)] = index
					end)
				end
			end
			
			if not pages.IsFinished then
				pages:AdvanceToNextPageAsync()
			else
				break
			end
		end
		
		if i == TournamentService.TournamentInfo.TournamentNumber then
			Packets.Tournament.GetTournamentRankings.sendToAll(TournamentService.LeaderboardRankings[i])
		end
	end
end


local function teleportToTournament(plr: Player)
	local placeId
	for pid, placeName in Client.Immutable.Servers.PLACES[game.GameId] do
		if placeName == TournamentService.TournamentInfo.MapName then
			placeId = pid
			break
		end
	end
	
	local code, privateServerID = TeleportService:ReserveServer(placeId)
	
	local TeleportUI = ReplicatedStorage.Assets.Visuals.TeleportUI:Clone()
	TeleportUI.Frame.Container.Title.Text = `Teleporting To {TournamentService.TournamentInfo.MapName}...`
	TeleportUI.Frame.Background.Image = Client.Immutable.Servers.PLACE_INFO[TournamentService.TournamentInfo.MapName].MapImage
	
	local success, err = pcall(TeleportService.TeleportToPrivateServer, TeleportService,
		placeId, code, {plr}, nil, {
			ExpectedPlayers = 1,
			Tournament = true
		}, TeleportUI
	)
	if not success then
		warn(err)


	end
end


local function claimLastTournamentReward(plr: Player)
	local DataService = Server.GetService('DataService')

	if TournamentService.CanClaim(plr) == false then return end

	local profile = DataService.GetProfile(plr)
	
	local tNum = TournamentService.TournamentInfo.TournamentNumber - 1
	local rank = TournamentService.LeaderboardRankings[tNum][tostring(plr.UserId)].Rank
	
	local RewardBracket = (rank == 1 and Client.Immutable.Tournament.TOURNAMENT_REWARDS.Top1)
		or (rank <= 5 and Client.Immutable.Tournament.TOURNAMENT_REWARDS.Top5)
		or (rank <= 10 and Client.Immutable.Tournament.TOURNAMENT_REWARDS.Top10) or {}
	
	for itemName, itemInfo in RewardBracket do
		Server.GetService(itemInfo.Reward.Service)[itemInfo.Reward.Function](plr, table.unpack(itemInfo.Reward.Args))
	end
	
	profile.Tournament.Claimed = true
end


function TournamentService.CanClaim(plr: Player)
	local DataService = Server.GetService('DataService')

	local profile = DataService.GetProfile(plr)
	
	if profile.Tournament.Claimed == true then
		return false
	end
	
	local tNum = TournamentService.TournamentInfo.TournamentNumber - 1
	if TournamentService.LeaderboardRankings[tNum] and TournamentService.LeaderboardRankings[tNum][tostring(plr.UserId)] then
		return TournamentService.LeaderboardRankings[tNum][tostring(plr.UserId)].Rank < 10
	end
	
	return false
end


function TournamentService._PlayerAdded(plr: Player)
	local DataService = Server.GetService('DataService')
	
	local profile = DataService.GetProfile(plr)
	
	if profile.Tournament.CurrentTournament ~= TournamentService.TournamentInfo.TournamentNumber then
		profile.Tournament.CurrentTournament = TournamentService.TournamentInfo.TournamentNumber
		profile.Tournament.Claimed = false
	end
	
	for i = 1, TournamentService.TournamentInfo.TournamentNumber do
		if TournamentService.LeaderboardRankings[i] and TournamentService.LeaderboardRankings[i][tostring(plr.UserId)] then
			profile.Tournament.PreviousRankings[tostring(i)] = TournamentService.LeaderboardRankings[i][tostring(plr.UserId)].Rank
		end
	end
end


function TournamentService._Start()
	local DataService = Server.GetService('DataService')
	local TimeService = Server.GetService('TimeService')
	local RoundService = Server.GetService('RoundService')
	local OrderService = Server.GetService('OrderService')
	
	loadTournamentInfo()
	
	Packets.Tournament.GetTournamentInfo.listen(function(_, plr: Player)
		Packets.Tournament.GetTournamentInfo.sendTo(TournamentService.TournamentInfo, plr)
	end)
	
	Packets.Tournament.GoToTournament.listen(function(_, plr: Player)
		teleportToTournament(plr)
	end)
	
	Packets.Tournament.GetTournamentTimeLeft.listen(function(_, plr: Player)
		Packets.Tournament.GetTournamentTimeLeft.sendTo(TimeService.GetTimeLeftUntilNextMonth(), plr)
	end)
	
	Packets.Tournament.HighScore.listen(function(_, plr: Player)
		local store = DataStoreService:GetOrderedDataStore(
			Server.Immutable.Data.DATASTORE_HASHED,
			`Tournament{TournamentService.TournamentInfo.TournamentNumber}`
		)
		
		Packets.Tournament.HighScore.sendTo(store:GetAsync(plr.UserId), plr)
	end)
	
	Packets.Tournament.GetPreviousRankings.listen(function(_, plr: Player)
		local profile = DataService.GetProfile(plr)
		
		local dataToSend = Table.Copy(profile.Tournament.PreviousRankings)
		
		if dataToSend[tostring(TournamentService.TournamentInfo.TournamentNumber)] then
			dataToSend[tostring(TournamentService.TournamentInfo.TournamentNumber)] = nil
		end
		
		Packets.Tournament.GetPreviousRankings.sendTo(dataToSend, plr)
	end)
	
	Packets.Tournament.GetTournamentRankings.listen(function(_, plr: Player)
		local dataToSend = TournamentService.LeaderboardRankings[TournamentService.TournamentInfo.TournamentNumber]
		Packets.Tournament.GetTournamentRankings.sendTo(dataToSend, plr)
	end)
	
	Packets.Tournament.CanClaim.listen(function(_, plr: Player)
		Packets.Tournament.CanClaim.sendTo(TournamentService.CanClaim(plr), plr)
	end)
	
	Packets.Tournament.Claim.listen(function(_, plr: Player)
		claimLastTournamentReward(plr)
	end)

	RoundService.RoundEndedSignal:Connect(function()
		if RoundService.IsTournament then
			local OrdersMade = OrderService.OrdersCompleted
			
			local store = DataStoreService:GetOrderedDataStore(
				Server.Immutable.Data.DATASTORE_HASHED,
				`Tournament{TournamentService.TournamentInfo.TournamentNumber}`
			)
			
			for _, v in Players:GetPlayers() do
				task.spawn(function()
					store:SetAsync(v.UserId, OrdersMade)
				end)
			end
		end
	end)
end


return TournamentService