local AnalyticsService = game:GetService('AnalyticsService')
local ServerScriptService = game:GetService('ServerScriptService')
local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local CharactersData = require(ReplicatedStorage.Data.Characters)

local Analytics = {}


function Analytics.LogEcon(plr: Player, isAdding: boolean, increment: number, currency: string, transType: string)
	--[[AnalyticsService:LogEconomyEvent(plr, Enum.AnalyticsEconomyFlowType.Source, 'Crystals',
		WaveService.Wave, DataService.Get(plr, 'Crystals'), Enum.AnalyticsEconomyTransactionType.IAP)]]
	
end


function Analytics._PlayerAdded(plr: Player)
	local DataService = Server.GetService('DataService')
	
	local profile = DataService.GetProfile(plr)
	
	for mapName, v in Client.Immutable.Servers.PLACE_INFO do
		if v.ServerType == 'Round' then
			if not profile.Analytics.MapStats[mapName] then
				profile.Analytics.MapStats[mapName] = {}
			end
			
			for i = 1, 255 do
				if not profile.Analytics.MapStats[mapName][i] then
					profile.Analytics.MapStats[mapName][i] = { Wins = 0, Losses = 0, MostDelivered = 0 }
				end
			end
		end
	end
end


function Analytics._Start()
	local RoundService = Server.GetService('RoundService')
	local OrderService = Server.GetService('OrderService')
	local DataService = Server.GetService('DataService')
	local ChallengeService = Server.GetService('ChallengeService')
	
	RoundService.RoundEndedSignal:Connect(function(timerRanOut: boolean?)
		for _, v in Players:GetPlayers() do
			local profile = DataService.GetProfile(v)
			
			local MapName = Client.Immutable.Servers.PLACES[game.GameId][game.PlaceId]
			
			task.spawn(AnalyticsService.LogCustomEvent, AnalyticsService, v, 'Round Scores', RoundService.Score, {
				[Enum.AnalyticsCustomFieldKeys.CustomField01.Name] = MapName,
				[Enum.AnalyticsCustomFieldKeys.CustomField02.Name] = #Players:GetPlayers()
			})
			
			if RoundService.Act and not RoundService.IsTournament and not ChallengeService.Challenge then
				local MapStatTbl = profile.Analytics.MapStats[MapName][RoundService.Act]
				MapStatTbl[timerRanOut and 'Wins' or 'Losses'] += 1
				MapStatTbl.MostDelivered = math.max(MapStatTbl.MostDelivered, OrderService.DeliveredDishTracker[v])
			end
		end
	end)
	
	RoundService.RoundStartedSignal:Connect(function()
		for _, v in Players:GetPlayers() do
			local PlayerCharacters = DataService.Get(v, 'Characters')
			
			local UsedCharacterID = v:GetAttribute'CharacterID'
			if UsedCharacterID and PlayerCharacters[UsedCharacterID] then
				PlayerCharacters[UsedCharacterID].Uses += 1
			end
		end
	end)
	
	OrderService.OrderDeleted:Connect(function(_, _, plr: Player)
		if plr then
			local PlayerCharacters = DataService.Get(plr, 'Characters')
			
			local UsedCharacterID = plr:GetAttribute'CharacterID'
			if UsedCharacterID and PlayerCharacters[UsedCharacterID] then
				PlayerCharacters[UsedCharacterID].Deliveries += 1
			end
			
			DataService.Increment(plr, 'PlatesSubmitted', 1)
		end
	end)
	
	Packets.Matchmaking.GetWinrates.listen(function(_, plr: Player)
		local profile = DataService.GetProfile(plr)
		Packets.Matchmaking.GetWinrates.sendTo(profile.Analytics.MapStats, plr)
	end)
end


return Analytics