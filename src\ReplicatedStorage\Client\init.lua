local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local RunService = game:GetService('RunService')

local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)

local Client = {
	Controllers = {},
	Immutable = require(script.ClientImmutable)
}


if RunService:IsClient() then
	if not workspace:GetAttribute'ServerReady' then
		workspace:GetAttributeChangedSignal'ServerReady':Wait()
	end
end


function Client.GetController(controller: string)
	return Client.Controllers[controller]
end


function Client._Init()
	local start = os.clock()

	for _, v in ReplicatedStorage.Client:GetDescendants() do
		if v:IsA'ModuleScript' then
			Client.Controllers[v.Name] = require(v)

			if type(Client.Controllers[v.Name]) ~= 'table' then
				Client.Controllers[v.Name] = nil

				if not v:FindFirstAncestorWhichIsA'ModuleScript' then
					warn(`[Client Initiation] '{v.Name}' has not been loaded. Place it as descendant of a ModuleScript or return a table to silence.`)
				end
			end
		end
	end
	
	local AnimationsClient = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsClient)
	task.spawn(AnimationsClient.Init, AnimationsClient, {
		AutoLoadAllPlayerTracks = true,
		TimeToLoadPrints = false
	})
	
	local CmdrClient = require(ReplicatedStorage:WaitForChild'CmdrClient')
	CmdrClient:SetActivationKeys({ Enum.KeyCode.F2 })
	CmdrClient:SetPlaceName(Client.Immutable.Servers.PLACES[game.GameId][game.PlaceId])
	
	task.spawn(function()
		if not Players.LocalPlayer:GetAttribute'Rank' then
			Players.LocalPlayer:GetAttributeChangedSignal'Rank':Wait()
		end
		
		if Players.LocalPlayer:GetAttribute'Rank' >= 2 then
			CmdrClient:SetEnabled(true)
		else
			CmdrClient:SetEnabled(false)
		end
	end)

	for k, v in Client.Controllers do
		if type(v._Init) == 'function' then
			v._Init()
		end
	end
	
	workspace:SetAttribute('ClientReady', true)

	print(
		string.format(
			'Client successfully initialized (V%s) [%.02f ms]',
			game.PlaceVersion,
			(os.clock() - start) * 1000
		)
	)


	for k, v in Client.Controllers do
		if type(v._Start) == 'function' then
			task.defer(function()
				debug.setmemorycategory(k)
				v._Start()
			end)
		end

		if type(v._PlayerAdded) == 'function' then
			ClassExtension.Players.PlayerAdded(v._PlayerAdded)
		end

		if type(v._PlayerRemoving) == 'function' then
			Players.PlayerRemoving:Connect(v._PlayerRemoving)
		end
	end
end


return Client