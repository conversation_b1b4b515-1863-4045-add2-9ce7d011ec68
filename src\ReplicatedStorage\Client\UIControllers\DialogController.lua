local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)

local DialogController = {
	DialogJanitor = Janitor.new()
}


type Dialog = {
	Header: string,
	Description: string,
	Yes: { Text: string, Callback: () -> () } ,
	No: { Text: string, Callback: () -> () }?
}


function DialogController.SetDialog(dialogInfo: Dialog)
	local UIController = Client.GetController('UIController')
	
	local DialogFrame = Players.LocalPlayer.PlayerGui.SharedUI.Dialog
	
	DialogController.DialogJanitor:Cleanup()
	
	UIController.Close()
	
	DialogController.DialogJanitor:Add(function()
		DialogFrame.Visible = false
	end)
	
	DialogFrame.Buttons.Okay.Txt.Text = dialogInfo.Yes.Text
	
	if dialogInfo.No then
		DialogFrame.Buttons.Cancel.Visible = true
		DialogFrame.Buttons.Cancel.Txt.Text = dialogInfo.No.Text
	else
		DialogFrame.Buttons.Cancel.Visible = false
	end
	
	local pressed = false
	DialogController.DialogJanitor:Add(DialogFrame.Buttons.Okay.MouseButton1Click:Connect(function()
		pressed = true
		DialogController.DialogJanitor:Cleanup()
		dialogInfo.Yes.Callback()
	end))
	
	if dialogInfo.No then
		DialogController.DialogJanitor:Add(DialogFrame.Buttons.Cancel.MouseButton1Click:Connect(function()
			pressed = true
			DialogController.DialogJanitor:Cleanup()
			dialogInfo.No.Callback()
		end))
		
		DialogController.DialogJanitor:Add(function()
			if pressed then return end
			dialogInfo.No.Callback()
		end)
	end

	DialogFrame.Description.Text = dialogInfo.Description
	
	DialogFrame.Header.Text = dialogInfo.Header
	DialogFrame.Header.Txt.Text = dialogInfo.Header
	
	DialogFrame.Visible = true
end


function DialogController._Start()
	
end


return DialogController