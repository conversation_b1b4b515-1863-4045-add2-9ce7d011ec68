local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local GuiService = game:GetService('GuiService')
local UserInputService = game:GetService('UserInputService')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local CharacterData = require(ReplicatedStorage.Data.Characters)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local XPPerLevel = require(ReplicatedStorage.Data.XPPerLevel)

local CharacterInventoryController = {
	CharData = {},
	IsInSellMode = false,
	
	IsInFuseMode = false,
	CharacteToFuse = nil
}

local Mouse = Players.LocalPlayer:GetMouse()


local function refreshCharacterInventory(data: { [string]: { Character: string, TimeSummoned: number, Favorite: boolean, Equipped: boolean, Locked: boolean, Deliveries: number, Uses: number, XP: number, Level: number } })
	CharacterInventoryController.CharData = data
	
	local ItemController = Client.GetController('ItemController')
	local InputController = Client.GetController('InputController')
	local FilterController = Client.GetController('FilterController')
	
	local CharacterInventoryFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Characters
	local HoverFrame = CharacterInventoryFrame.Extendes.Hover
	local ClickFrame = CharacterInventoryFrame.Extendes.Click
	
	for charID, charInfo in data do
		local charName = charInfo.Character
		
		local ThisCharacterInfo = CharacterData[charName]
		
		local ThisCharFrame = CharacterInventoryFrame.Content.Holder.Scroller:FindFirstChild(charID)
		if not ThisCharFrame then
			ThisCharFrame = ItemController.CreateItem({
				ItemName = charName,
				Rarity = ThisCharacterInfo.Rarity,
				Parent = CharacterInventoryFrame.Content.Holder.Scroller,
				HoverItem = CharacterInventoryFrame.Extendes.Hover,
			})
			
			ThisCharFrame:SetAttribute('Name', charName)
			ThisCharFrame.Name = charID
			
			ThisCharFrame:SetAttribute('TimeSummoned', charInfo.TimeSummoned)
			
			ThisCharFrame.MouseButton1Click:Connect(function()
				local ThisCharInfo = CharacterInventoryController.CharData[charID]
				
				if CharacterInventoryController.IsInSellMode then
					if ThisCharInfo.Locked == false then
						ThisCharFrame.Content.SellOverlay.Visible = not ThisCharFrame.Content.SellOverlay.Visible
						CharacterInventoryController.ToggleCharactersSelling(true)
					end
					return
				elseif CharacterInventoryController.IsInFuseMode then
					if ThisCharInfo.Locked == false then
						CharacterInventoryController.AddCharacterToFuse(charID)
					end
					return
				end
				
				if ClickFrame:GetAttribute'Selection' == charID and ClickFrame.Visible == true then
					ClickFrame.Visible = false
					return
				end
				
				local pos = ThisCharFrame.AbsolutePosition - ClickFrame.Parent.AbsolutePosition
				ClickFrame.Position = UDim2.fromOffset(pos.X + ClickFrame.AbsoluteSize.X - 10, pos.Y)
				
				ClickFrame.Holder.Header.ItemName.Text = charName:upper()
				ClickFrame.Holder.Header.Rarity.Text = ThisCharacterInfo.Rarity:upper()
				
				ClickFrame.Holder.Lvl.Bar.Holder.Loader.Size = UDim2.fromScale(ThisCharInfo.XP/XPPerLevel[ThisCharInfo.Level], 1)
				ClickFrame.Holder.Lvl.Holder.Xp.Text = `{ThisCharInfo.XP}/{XPPerLevel[ThisCharInfo.Level]}xp`
				ClickFrame.Holder.Lvl.Holder.Lvl.Text = `LVL {ThisCharInfo.Level}`
				
				ClickFrame.Holder.Buttons.UNLOCK.Txt.Text = ThisCharInfo.Locked and 'UNLOCK' or 'LOCK'
				ClickFrame.Holder.Buttons.EQUIP.Txt.Text = ThisCharInfo.Equipped and 'UNEQUIP' or 'EQUIP'
				ClickFrame.Holder.Buttons.FAVOURITE.Txt.Text = ThisCharInfo.Favorite and 'UNFAVORITE' or 'FAVORITE'
				
				for _, v in ClickFrame.Abilities:GetChildren() do
					if v:IsA'GuiObject' then v:Destroy() end
				end
				
				for _, v in ThisCharacterInfo.Abilities do
					local AbilityTemplate = ReplicatedStorage.Assets.Templates.InventoryAbilityTemplate:Clone()
					
					local AbilityBinder = InputController.Binders[v]
					local keyImage = UserInputService:GetImageForKeyCode(AbilityBinder.GamepadKey)
					if InputController.Preferred == 'Gamepad' and keyImage ~= '' then
						AbilityTemplate.Key.TextLabel.Text = ''
						AbilityTemplate.Key.ImageLabel.Visible = true
						AbilityTemplate.Key.ImageLabel.Image = keyImage
					else
						AbilityTemplate.Key.TextLabel.Text = AbilityBinder.Key.Name
						AbilityTemplate.Key.ImageLabel.Visible = false
					end
					
					AbilityTemplate.Txt.Text = v:upper()
					
					AbilityTemplate.Parent = ClickFrame.Abilities
				end
				
				ClickFrame.Abilities.Visible = #ThisCharacterInfo.Abilities > 0
				ClickFrame.Visible = true
				
				ClickFrame:SetAttribute('Selection', charID)
			end)
			
			local function hovered()
				local ThisCharInfo = CharacterInventoryController.CharData[charID]

				HoverFrame.Holder.Header.ItemName.Text = charName:upper()
				HoverFrame.Holder.Header.Rarity.Text = ThisCharacterInfo.Rarity:upper()
				HoverFrame.BottomHolder.Row1.Num.Text = ThisCharInfo.Deliveries
				HoverFrame.BottomHolder.Row2.Num.Text = ThisCharInfo.Uses

				HoverFrame.Holder.Lvl.Bar.Holder.Loader.Size = UDim2.fromScale(ThisCharInfo.XP/XPPerLevel[ThisCharInfo.Level], 1)
				HoverFrame.Holder.Lvl.Holder.Xp.Text = `{ThisCharInfo.XP}/{XPPerLevel[ThisCharInfo.Level]}xp`
				HoverFrame.Holder.Lvl.Holder.Lvl.Text = `LVL {ThisCharInfo.Level}`

				for _, v in HoverFrame.Holder.Abilities:GetChildren() do
					if v:IsA'GuiObject' then v:Destroy() end
				end

				for _, v in ThisCharacterInfo.Abilities do
					local AbilityTemplate = ReplicatedStorage.Assets.Templates.HoverInventoryAbilityTemplate:Clone()

					local AbilityBinder = InputController.Binders[v]
					local keyImage = UserInputService:GetImageForKeyCode(AbilityBinder.GamepadKey)
					if InputController.Preferred == 'Gamepad' and keyImage ~= '' then
						AbilityTemplate.Key.TextLabel.Text = ''
						AbilityTemplate.Key.ImageLabel.Visible = true
						AbilityTemplate.Key.ImageLabel.Image = keyImage
					else
						AbilityTemplate.Key.TextLabel.Text = AbilityBinder.Key.Name
						AbilityTemplate.Key.ImageLabel.Visible = false
					end

					AbilityTemplate.Txt.Text = v:upper()

					AbilityTemplate.Parent = HoverFrame.Holder.Abilities
				end
			end
			
			ThisCharFrame.MouseEnter:Connect(hovered)
			ThisCharFrame.SelectionGained:Connect(hovered)
		end
		
		ThisCharFrame.Content.SellOverlay.Coin.Counter.Text = `+{ThisCharacterInfo.SellValue}$`
		ThisCharFrame.Content.Lock.Visible = charInfo.Locked
		ThisCharFrame.Content.Favorite.Visible = charInfo.Favorite
		ThisCharFrame.LayoutOrder = charInfo.Favorite and -10 or 1
	end
	
	local seen = {}
	for _, v in CharacterInventoryFrame.Content.Holder.Scroller:GetChildren() do
		if v:IsA'ImageButton' then
			if not CharacterInventoryController.CharData[v.Name] or seen[v.Name] then
				v:Destroy()
			end
			
			seen[v.Name] = true
		end
	end
	
	ClickFrame.Visible = false
	HoverFrame.Visible = false
	
	local SearchBar = CharacterInventoryFrame.Content.TopHolder.SearchBar.TextBox
	FilterController.ApplyFilter(CharacterInventoryFrame.Content.Holder.Scroller:GetChildren(), SearchBar.Text)
end


local function setupCharacterInventory()
	local UIController = Client.GetController('UIController')
	local FilterController = Client.GetController('FilterController')
	local FeedingController = Client.GetController('FeedingController')
	local SkinController = Client.GetController('SkinController')
	
	local PassiveFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Passive
	local FilterFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Filter
	local CharacterInventoryFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Characters
	local ClickFrame = CharacterInventoryFrame.Extendes.Click
	local HoverFrame = CharacterInventoryFrame.Extendes.Hover
	local SearchBar = CharacterInventoryFrame.Content.TopHolder.SearchBar.TextBox
	
	ClickFrame:GetPropertyChangedSignal'Visible':Connect(function()
		if ClickFrame.Visible == false then
			ClickFrame:SetAttribute('Selection', nil)
		end
		
		if ClickFrame.Visible and HoverFrame.Visible then
			HoverFrame.Visible = false
		end
		HoverFrame:SetAttribute('Disabled', ClickFrame.Visible)
	end)
	
	CharacterInventoryFrame.Extendes.Buttons.Sell.MouseButton1Click:Connect(function()
		if CharacterInventoryController.IsInSellMode then
			local sellingCharacters = {}
			for _, v in CharacterInventoryFrame.Content.Holder.Scroller:GetChildren() do
				if v:IsA'ImageButton' and v.Content.SellOverlay.Visible then
					table.insert(sellingCharacters, v.Name)
				end
			end
			
			Packets.Inventory.SellCharacters.send(sellingCharacters)
			CharacterInventoryController.ToggleCharactersSelling(false)
		elseif CharacterInventoryController.IsInFuseMode then
			local FusingCharacters = {}
			for _, v in CharacterInventoryFrame.Content.Holder.Scroller:GetChildren() do
				if v:IsA'ImageButton' and v.Content.FuseOverlay.Visible == true then
					table.insert(FusingCharacters, v.Name)
				end
			end
			
			Packets.Inventory.FuseCharacter.send({
				BaseCharacter = CharacterInventoryController.CharacteToFuse,
				FusingCharacters = FusingCharacters
			})
			CharacterInventoryController.ToggleCharacterFusing(false)
		end
	end)
	
	CharacterInventoryFrame.Extendes.Buttons.Cancel.MouseButton1Click:Connect(function()
		CharacterInventoryController.ToggleCharactersSelling(false)
		CharacterInventoryController.ToggleCharacterFusing(false)
	end)
	
	CharacterInventoryFrame.Content.TopHolder.FILTER.MouseButton1Click:Connect(function()
		FilterController.OpenFilter()
	end)
	
	PassiveFrame.Contents.Close.MouseButton1Click:Connect(function()
		UIController.Open(CharacterInventoryFrame)
	end)
	
	UIController.PageToggled:Connect(function(frame: GuiObject, isOpen: boolean, wasOpened: Frame?)
		if frame == CharacterInventoryFrame and isOpen == false then
			CharacterInventoryController.ToggleCharactersSelling(false)
			CharacterInventoryController.ToggleCharacterFusing(false)
			ClickFrame.Visible = false
		elseif frame == CharacterInventoryFrame and isOpen == true then
			FilterController.ApplyFilter(CharacterInventoryFrame.Content.Holder.Scroller:GetChildren(), SearchBar.Text)
		end
		
		if frame == CharacterInventoryFrame and FilterController.CurrentFilterData.FrameName ~= CharacterInventoryFrame.Name then
			FilterController.DisableFilter()
			FilterController.ApplyFilter(CharacterInventoryFrame.Content.Holder.Scroller:GetChildren(), SearchBar.Text)
		end
		
		if wasOpened == FilterFrame and frame == CharacterInventoryFrame and isOpen == true then
			FilterController.ApplyFilter(CharacterInventoryFrame.Content.Holder.Scroller:GetChildren(), SearchBar.Text)
		end
	end)
	
	do -- Click frame
		ClickFrame.Holder.Buttons.SELL.MouseButton1Click:Connect(function()
			local SelectedCharacter = ClickFrame:GetAttribute'Selection'
			if SelectedCharacter and CharacterInventoryController.CharData[SelectedCharacter].Locked == false then
				local CharFrame = CharacterInventoryFrame.Content.Holder.Scroller[SelectedCharacter]
				CharFrame.Content.SellOverlay.Visible = true
				CharacterInventoryController.ToggleCharactersSelling(true)
				ClickFrame.Visible = false
			end
		end)
		
		ClickFrame.BottomButons.Fuse.MouseButton1Click:Connect(function()
			local SelectedCharacter = ClickFrame:GetAttribute'Selection'
			if SelectedCharacter then
				CharacterInventoryController.ToggleCharacterFusing(true, SelectedCharacter)
				ClickFrame.Visible = false
			end
		end)
		
		ClickFrame.Holder.Buttons.UNLOCK.MouseButton1Click:Connect(function()
			local SelectedCharacter = ClickFrame:GetAttribute'Selection'
			if SelectedCharacter then
				Packets.Inventory.ToggleLockCharacter.send(SelectedCharacter)
				ClickFrame.Visible = false
			end
		end)
		
		ClickFrame.Holder.Buttons.EQUIP.MouseButton1Click:Connect(function()
			local SelectedCharacter = ClickFrame:GetAttribute'Selection'
			if SelectedCharacter then
				if CharacterInventoryController.CharData[SelectedCharacter].Equipped then
					SelectedCharacter = nil
				end
				Packets.Inventory.EquipCharacter.send(SelectedCharacter)
				ClickFrame.Visible = false
			end
		end)
		
		ClickFrame.Holder.Buttons.FAVOURITE.MouseButton1Click:Connect(function()
			local SelectedCharacter = ClickFrame:GetAttribute'Selection'
			if SelectedCharacter then
				Packets.Inventory.ToggleFavoriteCharacter.send(SelectedCharacter)
				ClickFrame.Visible = false
			end
		end)
		
		ClickFrame.BottomButons.Passive.MouseButton1Click:Connect(function()
			local SelectedCharacter = ClickFrame:GetAttribute'Selection'
			if SelectedCharacter then
				CharacterInventoryController.OpenPassive(SelectedCharacter)
			end
		end)
		
		ClickFrame.BottomButons.Feed.MouseButton1Click:Connect(function()
			local SelectedCharacter = ClickFrame:GetAttribute'Selection'
			if SelectedCharacter then
				FeedingController.SetFeedFrame(SelectedCharacter)
			end
		end)
		
		ClickFrame.BottomButons.Skins.MouseButton1Click:Connect(function()
			local SelectedCharacter = ClickFrame:GetAttribute'Selection'
			if SelectedCharacter then
				SkinController.OpenSkinFrame(SelectedCharacter)
			end
		end)
	end
	
	SearchBar:GetPropertyChangedSignal'Text':Connect(function()
		FilterController.ApplyFilter(CharacterInventoryFrame.Content.Holder.Scroller:GetChildren(), SearchBar.Text)
	end)
	
	task.defer(CharacterInventoryController.ToggleCharactersSelling, false)
	task.defer(CharacterInventoryController.ToggleCharacterFusing, false)
end


function CharacterInventoryController.OpenPassive(characterID: string)
	local UIController = Client.GetController('UIController')
	
	local PassiveFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Passive
	
	for _, v in Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Passive.Contents.Contents.ScrollingFrame:GetChildren() do
		if v:IsA'GuiObject' and v.Name ~= 'BottomPadding' then v:Destroy() end
	end
	
	for _, v in CharacterData[CharacterInventoryController.CharData[characterID].Character].PassiveDesc do
		local ThisPassiveSlot = ReplicatedStorage.Assets.Templates.PassiveFrameTemplate:Clone()
		ThisPassiveSlot.Title.Text = v.Title
		ThisPassiveSlot.Description.Text = v.Desc
		ThisPassiveSlot.Parent = PassiveFrame.Contents.Contents.ScrollingFrame
	end
	
	UIController.Open(PassiveFrame)
end


function CharacterInventoryController.ToggleCharactersSelling(enabled: boolean)
	local CharacterInventoryFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Characters
	
	CharacterInventoryController.IsInSellMode = enabled
	
	local sellSum = 0
	for _, v in CharacterInventoryFrame.Content.Holder.Scroller:GetChildren() do
		if v:IsA'ImageButton' then
			if enabled == false then
				v.Content.SellOverlay.Visible = enabled
			else
				if v.Content.SellOverlay.Visible == true then
					sellSum += CharacterData[CharacterInventoryController.CharData[v.Name].Character].SellValue
				end
			end
		end
	end
	
	CharacterInventoryFrame.Extendes.Buttons.Sell.Txt.Text = 'SELL'
	CharacterInventoryFrame.Extendes.Buttons.Sell.Visible = sellSum > 0
	CharacterInventoryFrame.Extendes.Buttons.Sell.SellVal.Visible = sellSum > 0
	CharacterInventoryFrame.Extendes.Buttons.Sell.SellVal.Text = `+{sellSum}$`
	
	CharacterInventoryFrame.Extendes.Buttons.Visible = enabled
	
	if sellSum == 0 and enabled == true then
		CharacterInventoryController.ToggleCharactersSelling(false)
	end
end


function CharacterInventoryController.ToggleCharacterFusing(enabled: boolean, character: string?)
	local CharacterInventoryFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Characters
	
	CharacterInventoryController.IsInFuseMode = enabled
	CharacterInventoryController.CharacteToFuse = character
	
	CharacterInventoryFrame.Extendes.Buttons.Sell.Txt.Text = 'FUSE'
	
	CharacterInventoryFrame.Extendes.Buttons.Visible = enabled
	CharacterInventoryFrame.Extendes.Buttons.Sell.Visible = false
	
	for _, v in CharacterInventoryFrame.Content.Holder.Scroller:GetChildren() do
		if v:IsA'ImageButton' and v.Content.FuseOverlay.Visible then
			v.Content.FuseOverlay.Visible = false
		end
	end
end


function CharacterInventoryController.AddCharacterToFuse(character: string, add: boolean)
	if character == CharacterInventoryController.CharacteToFuse then return end
	
	local CharacterInventoryFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Characters

	local ThisCharFrame = CharacterInventoryFrame.Content.Holder.Scroller[character]
	ThisCharFrame.Content.FuseOverlay.Visible = not ThisCharFrame.Content.FuseOverlay.Visible
	
	local FusingCharacters = {}
	local FuseXP = 0
	for _, v in CharacterInventoryFrame.Content.Holder.Scroller:GetChildren() do
		if v:IsA'ImageButton' and v.Content.FuseOverlay.Visible == true then
			FuseXP += CharacterData[CharacterInventoryController.CharData[v.Name].Character].FuseXP
			table.insert(FusingCharacters, v.Name)
		end
	end
	
	CharacterInventoryFrame.Extendes.Buttons.Sell.Visible = #FusingCharacters > 0
	CharacterInventoryFrame.Extendes.Buttons.Sell.SellVal.Visible = FuseXP > 0
	CharacterInventoryFrame.Extendes.Buttons.Sell.SellVal.Text = `+{FuseXP} XP`
end


function CharacterInventoryController.GetCharacterNameFromID(characterID: string): string
	return CharacterInventoryController.CharData[characterID].Character
end


function CharacterInventoryController._Start()
	local CharacterInventoryFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.Characters
	
	for _, v in CharacterInventoryFrame.Content.Holder.Scroller:GetChildren() do
		if v:IsA'GuiObject' then v:Destroy() end
	end
	
	Packets.Inventory.UpdateCharacterInventory.listen(refreshCharacterInventory)
	Packets.Inventory.UpdateCharacterInventory.send()
	
	task.defer(setupCharacterInventory)
end


return CharacterInventoryController