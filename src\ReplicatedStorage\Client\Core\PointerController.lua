local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local RunService = game:GetService('RunService')
local TweenService = game:GetService('TweenService')

local Client = require(ReplicatedStorage.Client)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local Packets = require(ReplicatedStorage.Data.Packets)

local PointerController = {
	ActiveWaypoints = {}
}


function PointerController.SetPointer(object: BasePart | Model)
	if PointerController.ActiveWaypoints[object] then return end
	
	local ThisPointerJanitor = Janitor.new()
	
	PointerController.ActiveWaypoints[object] = ThisPointerJanitor
	
	local ThisPointer = ThisPointerJanitor:Add(script.Pointer:Clone())
	ThisPointer.Parent = object
	
	ThisPointer.ImageLabel.Position = UDim2.fromScale(.5, .6)
	TweenService:Create(
		ThisPointer.ImageLabel,
		TweenInfo.new(.4, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, math.huge, true),
		{Position = UDim2.fromScale(.5, .4)}
	):Play()
end


function PointerController.RemoveWaypoint(object: BasePart | Model)
	if PointerController.ActiveWaypoints[object] then
		PointerController.ActiveWaypoints[object]:Destroy()
		PointerController.ActiveWaypoints[object] = nil
	end
end


function PointerController.RemoveAllWaypoints()
	for object in PointerController.ActiveWaypoints do
		PointerController.RemoveWaypoint(object)
	end
end


function PointerController._Start()
	Packets.Waypoint.CreateWaypoint.listen(PointerController.SetPointer)
	Packets.Waypoint.RemoveWaypoint.listen(PointerController.RemoveWaypoint)
	Packets.Waypoint.RemoveAllWaypoints.listen(PointerController.RemoveAllWaypoints)
	
	Packets.Waypoint.StrictSetWaypoints.listen(function(data: {unknown})
		for _, v in data do
			PointerController.SetPointer(v)
		end
		
		for object in PointerController.ActiveWaypoints do
			if not table.find(data, object) then
				PointerController.RemoveWaypoint(object)
			end
		end
	end)
end


return PointerController