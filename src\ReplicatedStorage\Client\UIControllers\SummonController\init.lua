local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local TweenService = game:GetService('TweenService')
local RunService = game:GetService('RunService')

local Client = require(ReplicatedStorage.Client)

local Characters = require(ReplicatedStorage.Data.Characters)
local Packets = require(ReplicatedStorage.Data.Packets)
local Banners = require(ReplicatedStorage.Data.Banners)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local NumberController = require(ReplicatedStorage.SharedModules.NumberController)
local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local spr = require(ReplicatedStorage.SharedModules.spr)

local SummonController = {
	BannerUnits = {},
	
	SelectedBanner = nil :: string?,
	
	TimeLeftUntilNextHour = 0,
	
	IsDoingSummonAnim = false,
	SummonAnimationJanitor = Janitor.new(),
	
	SummonAnimationModules = {
		Normal = require(script.SummonAnim.Normal),
		Mythical = require(script.SummonAnim.Mythical)
	}
}

local Mouse = Players.LocalPlayer:GetMouse()


local function doTransition()
	local TransitionScreen = Players.LocalPlayer.PlayerGui.TransitionScreen
	
	TransitionScreen.Enabled = true
	TransitionScreen.TransitionScreen.BackgroundTransparency = 1
	
	local c = TransitionScreen:GetPropertyChangedSignal'Enabled':Connect(function()
		TransitionScreen.Enabled = true
	end)
	
	local t = TweenService:Create(
		TransitionScreen.TransitionScreen,
		TweenInfo.new(.2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
		{BackgroundTransparency = 0}
	)t:Play();t.Completed:Wait();
	
	task.delay(.4, function()
		local t = TweenService:Create(
			TransitionScreen.TransitionScreen,
			TweenInfo.new(.2, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
			{BackgroundTransparency = 1}
		);t:Play();t.Completed:Wait();
		
		c:Disconnect()
		
		TransitionScreen.Enabled = false
	end)
end


local function doSummonAnim(summoned: {string})
	local ViewportController = Client.GetController('ViewportController')
	local UIController = Client.GetController('UIController')
	
	if SummonController.IsDoingSummonAnim then return end
	
	local SummonScreen = Players.LocalPlayer.PlayerGui.Summon
	
	SummonController.IsDoingSummonAnim = true
	
	SummonController.SummonAnimationJanitor:Cleanup()
	
	doTransition()
	UIController.HidePrompts(true)
	
	SummonController.SummonAnimationJanitor:Add(function()
		SummonController.IsDoingSummonAnim = false
		
		Packets.Summon.TotalSummons.send()
		
		for _, v in Players.LocalPlayer.PlayerGui:GetChildren() do
			if v:IsA'ScreenGui' then
				v.Enabled = false
			end
		end
		
		Players.LocalPlayer.PlayerGui.LobbyUI.Enabled = true
	end)
	
	for _, v in Players.LocalPlayer.PlayerGui:GetChildren() do
		if v:IsA'ScreenGui' then
			v.Enabled = false
		end
	end
	
	--[[table.sort(summoned, function(a: string, b: string)
		local rarityidxa = table.find(Client.Immutable.RARITIES, Characters[a].Rarity)
		local rarityidxb = table.find(Client.Immutable.RARITIES, Characters[b].Rarity)
		return rarityidxa < rarityidxb
	end)]]
	
	local currentSummonIdx = 1
	local function setNextCharacter()
		local charName = summoned[currentSummonIdx]

		SummonScreen.Content.UIScale.Scale = .1

		SummonScreen.Content.CharName.Text = charName
		SummonScreen.Content.Rarity.Text = Characters[charName].Rarity

		ViewportController.SetCharacterHeadshot(SummonScreen.Content.ViewportFrame, charName, 'UpperBody')

		spr.stop(SummonScreen.Content.UIScale)
		spr.target(SummonScreen.Content.UIScale, .5, 2, {Scale = 1})

		currentSummonIdx += 1
	end
	
	local hasMythical = false
	for _, v in summoned do
		if Characters[v].Rarity == 'Mythical' then
			hasMythical = true
			break
		end
	end
	
	local SummonAnimModule = SummonController.SummonAnimationModules[if hasMythical then 'Mythical' else 'Normal']
	SummonController.SummonAnimationJanitor:Add(SummonAnimModule(Characters[summoned[1]].Rarity))
	
	for _, v in Players.LocalPlayer.PlayerGui:GetChildren() do
		if v:IsA'ScreenGui' then
			v.Enabled = v.Name == 'Summon'
		end
	end
	
	setNextCharacter()
	
	SummonController.SummonAnimationJanitor:Add(Mouse.Button1Down:Connect(function()
		if summoned[currentSummonIdx] then
			setNextCharacter()
			return
		end
		
		UIController.HidePrompts(false)
		doTransition()
		
		SummonController.SummonAnimationJanitor:Cleanup()
	end))
end


local function setupSummonFrame()
	local UIController = Client.GetController('UIController')
	
	local SummonFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Summon
	
	for _, v in SummonFrame.SideTab.Slots.Scroller:GetChildren() do
		if v:IsA'GuiObject' then v:Destroy() end
	end
	
	for bannerName, bannerInfo in Banners do
		local BannerSlot = ReplicatedStorage.Assets.Templates.BannerSlotTemplate:Clone()
		BannerSlot.Content.Txt.Text = bannerName:upper()
		BannerSlot.Parent = SummonFrame.SideTab.Slots.Scroller
		
		BannerSlot.MouseButton1Click:Connect(function()
			SummonController.SelectBanner(bannerName)
		end)
		
		RunService.RenderStepped:Connect(function()
			BannerSlot.Content.Timer.Txt.Text = NumberController.ToMS(SummonController.TimeLeftUntilNextHour)
			SummonFrame.Content.Header.Banner.Timer.Txt.Text = NumberController.ToMS(SummonController.TimeLeftUntilNextHour) -- TODO: Redo logic once more info on banners is given
		end)
		
		if not SummonController.SelectedBanner then
			BannerSlot.LayoutOrder -= 1
			SummonController.SelectBanner(bannerName)
		end
	end
	
	SummonFrame.Content.Buttons.SummonButtons.One.MouseButton1Click:Connect(function()
		if not SummonController.SelectedBanner then return end
		Packets.Summon.Summon.send({BannerName = SummonController.SelectedBanner, TenTimes = false})
	end)
	
	SummonFrame.Content.Buttons.SummonButtons.Ten.MouseButton1Click:Connect(function()
		if not SummonController.SelectedBanner then return end
		Packets.Summon.Summon.send({BannerName = SummonController.SelectedBanner, TenTimes = true})
	end)
	
	SummonFrame.Content.Buttons.Left.Index.MouseButton1Click:Connect(function()
		UIController.Open(Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Index)
	end)
	
	SummonFrame.Content.Buttons.Left.Store.MouseButton1Click:Connect(function()
		UIController.Open(Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Store)
	end)
end


local function setupTimers()
	local SummonFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Summon
	
	
end


local function setPityBars(data: { LegendaryPity: number, MythicalPity: number })
	local SummonFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Summon
	
	local pityMaxL = Client.Immutable.SUMMON_LEGENDARY_PITY
	local pityMaxM = Client.Immutable.SUMMON_MYTHICAL_PITY

	local legendaryProgress = data.LegendaryPity % pityMaxL
	local mythicalProgress = data.MythicalPity % pityMaxM

	if legendaryProgress == 0 and data.LegendaryPity > 0 then
		legendaryProgress = pityMaxL
	end
	if mythicalProgress == 0 and data.MythicalPity > 0 then
		mythicalProgress = pityMaxM
	end

	SummonFrame.Content.Header.Bar1.Holder.Lvl.Text = `EVERY <font color="#fff001">{Client.Immutable.SUMMON_MYTHICAL_PITY}</font> SUMMONS GURANTEES A MYTHIC`
	SummonFrame.Content.Header.Bar2.Holder.Lvl.Text = `EVERY <font color="#fff001">{Client.Immutable.SUMMON_LEGENDARY_PITY}</font> SUMMONS GURANTEES A LEGENDARY`

	SummonFrame.Content.Header.Bar1.Bar.Xp.Text = `{mythicalProgress}/{pityMaxM}`
	SummonFrame.Content.Header.Bar2.Bar.Xp.Text = `{legendaryProgress}/{pityMaxL}`

	local progressRatioLegendary = legendaryProgress/Client.Immutable.SUMMON_LEGENDARY_PITY
	SummonFrame.Content.Header.Bar2.Bar.Holder.Loader.UIGradient.Transparency = NumberSequence.new{
		NumberSequenceKeypoint.new(0, 0),
		NumberSequenceKeypoint.new(math.clamp(progressRatioLegendary, .001, .998), 0),
		NumberSequenceKeypoint.new(math.clamp(progressRatioLegendary + .001, .002, .999), 1),
		NumberSequenceKeypoint.new(1, 1)
	}

	local progressRatioMythical = mythicalProgress/Client.Immutable.SUMMON_MYTHICAL_PITY
	SummonFrame.Content.Header.Bar1.Bar.Holder.Loader.UIGradient.Transparency = NumberSequence.new{
		NumberSequenceKeypoint.new(0, 0),
		NumberSequenceKeypoint.new(math.clamp(progressRatioMythical, .001, .998), 0),
		NumberSequenceKeypoint.new(math.clamp(progressRatioMythical + .001, .002, .999), 1),
		NumberSequenceKeypoint.new(1, 1)
	}
end


function SummonController.SelectBanner(banner: string)
	if not SummonController.BannerUnits[banner] then return end
	
	local ItemController = Client.GetController('ItemController')
	local ViewportController = Client.GetController('ViewportController')
	
	local SummonFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Summon
	
	SummonController.SelectedBanner = banner
	
	SummonFrame.Content.Header.Banner.UnderTxt.Text = banner
	SummonFrame.Content.Header.Banner.UnderTxt.Txt.Text = banner
	
	for _, v in SummonFrame.Content.Prizes.Holder:GetChildren() do
		if v:IsA'GuiObject' then v:Destroy() end
	end
	
	SummonFrame.Content.Upgrade.Details.ItemName.Text = SummonController.BannerUnits[banner][1]:upper()
	ViewportController.SetCharacterHeadshot(SummonFrame.Content.Profile.Viewport, SummonController.BannerUnits[banner][1], 'UpperBody')
	
	for i = 2, #SummonController.BannerUnits[banner] do
		local ThisCharacter = SummonController.BannerUnits[banner][i]
		
		local ThisCharFrame = ItemController.CreateItem({
			ItemName = ThisCharacter,
			Rarity = 'Legendary',
			Parent = SummonFrame.Content.Prizes.Holder,
		})
	end
end


function SummonController._Start()
	local SummonFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.Summon
	
	Packets.Summon.GetBannerUnits.listen(function(data: { BannerName: string, Units: {string} })
		SummonController.BannerUnits[data.BannerName] = data.Units
		
		if SummonController.SelectedBanner == data.BannerName or SummonController.SelectedBanner == nil then
			SummonController.SelectBanner(data.BannerName)
		end
	end)
	Packets.Summon.GetBannerUnits.send({BannerName = 'Banner 1'})
	
	Packets.Summon.GetBannerTimeLeft.listen(function(data: number)
		SummonController.TimeLeftUntilNextHour = data
	end)
	Packets.Summon.GetBannerTimeLeft.send()
	
	Packets.Summon.SummonedCharacters.listen(doSummonAnim)
	
	Packets.Summon.TotalSummons.listen(setPityBars)
	Packets.Summon.TotalSummons.send()
	
	task.defer(setupSummonFrame)
	task.defer(setupTimers)
	
	task.spawn(function()
		while true do
			task.wait(1)
			SummonController.TimeLeftUntilNextHour -= 1
		end
	end)
end


return SummonController