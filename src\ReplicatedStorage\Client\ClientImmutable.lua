local ReplicatedStorage = game:GetService('ReplicatedStorage')

--local SimpleMarketplace = require(ReplicatedStorage.Data.SimpleMarketplace)
local ActsInfo = require(ReplicatedStorage.Data.ActsInfo)

local RET = {
	Tags = {
		DISHES = 'Dishes',
		COUNTERS = 'Counter',
		INGREDIENT_BOX = 'IngredientBox',
		CUTTING_BOARD = 'CuttingBoard',
		SINK = 'Sink',
		GARBAGE = 'Garbage',
		SUBMIT_ORDER = 'SubmitOrder',
		STOVE = 'Stove',
		THROWN_ITEMS = 'ThrownItem',
		FIRE_EXTINGUISHER = 'FireExtinguisher',
		MAP_WALLS = 'MAP_WALL',
		MOVING_SURFACE = 'MovingSurface',
		INGREDIENT = 'Ingredient'
	},

	FOOD_SURFACES = {'Plate', 'Mug', 'Bowl', 'FryingPan', 'Pot'},
	SUBMITTABLE_FOOD_SURFACES = {'Plate', 'Mug', 'Bowl'},
	KITCHENWARES = {},

	DIRTY_DISH_TEXTURES = {'rbxassetid://14694908244'},

	Servers = {
		PLACE_INFO = {
			['Lobby'] = {
				ServerType = 'Lobby'
			},
			
			['Sky'] = ActsInfo['Sky'],
			
			['Restaurant'] = ActsInfo['Restaurant'],
			
			['Ghoul Cafe'] = ActsInfo['Ghoul Cafe'],
			
			['Infinite Castle'] = ActsInfo['Infinite Castle'],
			
			['AFK'] = {
				ServerType = 'AFK'
			}
		} :: { [string]: { Map: string?, ServerType: 'Round' | 'Lobby' | 'AFK' } },
		
		PLACES = {
			[7589583722] = {
				--[125719459715611] = 'Lobby',
				[125719459715611] = 'Restaurant',
				[130953051714179] = 'Restaurant',
				[87819941365678] = 'Sky',
				[98431103296045] = 'Ghoul Cafe',
				[77962022008132] = 'Infinite Castle',
				[80308679194259] = 'AFK'
			}
		}
	},

	CollisionGroups = {
		PLAYERS = 'Player',
		MAP_WALLS = 'MapWall',
		HOLDABLE_ITEMS = 'Holdable',
		NULL = 'Null'
	},
	
	Round = {
		ROUND_LEN = 60*5,

		ORDER_INIT = 5,
		
		MAX_ORDERS = 3,

		WaveOrderSpeeds = {
			[1] = {
				WAIT_UNTIL_NEXT_ORDER = 20,
				SMALL_ORDER_COMPLETION_TIME = 60,
				BIG_ORDER_COMPLETION_TIME = 90
			},
			
			[2] = {
				WAIT_UNTIL_NEXT_ORDER = 15,
				SMALL_ORDER_COMPLETION_TIME = 50,
				BIG_ORDER_COMPLETION_TIME = 70
			},
		},

		Scoring = {
			EXPIRED_ORDER = -5,
			ORDER_COMPLETED = 50,
			COMBO_SEQUENCE_MULTIPLIER = 1.1, -- increases by 1.1 every time
			TIP_BONUS_RANGE = NumberRange.new(1, 10)
		}
	},
	
	ACTS = 4,
	ACT_CONFIG = {
		[1] = {
			InfiniteCastleSwitchTime = 25,
			FailableOrders = 5,
			ScoreMult = 1,
			CookTime = 5,
			StoveBurnTime = 13
		},
		
		[2] = {
			InfiniteCastleSwitchTime = 18,
			FailableOrders = 4,
			ScoreMult = 1.25,
			CookTime = 5,
			StoveBurnTime = 11
		},
		
		[3] = {
			InfiniteCastleSwitchTime = 13,
			FailableOrders = 3,
			ScoreMult = 1.5,
			CookTime = 5,
			StoveBurnTime = 9
		},
		
		[4] = {
			InfiniteCastleSwitchTime = 10,
			FailableOrders = 3,
			ScoreMult = 1.75,
			CookTime = 5,
			StoveBurnTime = 7
		},
	},
	
	PLAYER_COUNT_CONFIG = {
		[1] = {
			CuttingSpeedMult = 1.3,
			StoveBurnTimeIncrement = 3,
			WalkSpeedMult = 1.2
		},
		
		[2] = {
			CuttingSpeedMult = 1.1,
			StoveBurnTimeIncrement = 1,
			WalkSpeedMult = 1.1
		}
	},
	
	Challenges = {
		['MovementSpeed'] = {
			Modifiers = {
				['MovementSpeed'] = 14
			},
			
			EffectName = '-80% Move Speed',
			
			Rewards = {
				{
					Name = 'Gems',
					Count = 50,
					Reward = {
						Service = 'CurrencyService',
						Function = 'AddGems',
						Args = {50, 'Challenge'}
					}
				}
			}
		},
		
		['FastBurnTime'] = {
			Modifiers = {
				['BurnTime'] = 5
			},

			EffectName = 'Faster burn time',
			
			Rewards = {
				{
					Name = 'Gems',
					Count = 25,
					Reward = {
						Service = 'CurrencyService',
						Function = 'AddGems',
						Args = {25, 'Challenge'}
					}
				}
			}
		},
		
		['FastOrders'] = {
			Modifiers = {
				['OrderLen'] = 50
			},

			EffectName = 'Faster orders',

			Rewards = {
				{
					Name = 'Gems',
					Count = 50,
					Reward = {
						Service = 'CurrencyService',
						Function = 'AddGems',
						Args = {50, 'Challenge'}
					}
				}
			}
		},
		
		['LongerCooldown'] = {
			Modifiers = {
				['CooldownAdjust'] = 15
			},

			EffectName = 'Longer cooldowns',

			Rewards = {
				{
					Name = 'Gems',
					Count = 25,
					Reward = {
						Service = 'CurrencyService',
						Function = 'AddGems',
						Args = {25, 'Challenge'}
					}
				}
			}
		},
		
		['NoThrowing'] = {
			Modifiers = {
				['CanThrow'] = false
			},

			EffectName = 'No throwing',

			Rewards = {
				{
					Name = 'Gems',
					Count = 25,
					Reward = {
						Service = 'CurrencyService',
						Function = 'AddGems',
						Args = {25, 'Challenge'}
					}
				}
			}
		},
	},
	
	TIME_TO_START_CHALLENGE_MATCHMAKING = 30,
	
	CharSettings = {
		WALK_SPEED = 25,
		
		LOBBY_RUN_SPEED = 36,
		LOBBY_WALK_SEED = 21,
		
		JUMP_POWER = 0
	},
	
	Areas = {
		['Challenges'] = {
			Description = 'yes no yes no',
			Image = 'rbxassetid://85096781716853',
			Order = 2
		},
		
		['Play'] = {
			Description = 'yes no yes no',
			Image = 'rbxassetid://85096781716853',
			Order = 1
		},
		
		['Tournaments'] = {
			Description = 'yes no yes no',
			Image = 'rbxassetid://85096781716853',
			Order = 3
		},
		
		['Codes'] = {
			Description = 'yes no yes no',
			Image = 'rbxassetid://85096781716853',
			Order = 4
		},
		
		['Quests'] = {
			Description = 'yes no yes no',
			Image = 'rbxassetid://85096781716853',
			Order = 5
		},
		
		['Crates'] = {
			Description = 'crateeessssss',
			Image = 'rbxassetid://85096781716853',
			Order = 6
		},
		
		['Summon'] = {
			Description = 'crateeessssss',
			Image = 'rbxassetid://85096781716853',
			Order = 7
		}
	},
	
	RARITIES = { 'Mythical', 'Legendary', 'Epic', 'Rare', 'Common' },
	
	CRATE_LEGENDARY_PITY = 100,
	CRATE_MYTHICAL_PITY = 500,
	
	SUMMON_LEGENDARY_PITY = 25,
	SUMMON_MYTHICAL_PITY = 100,
	
	Tournament = {
		TOURNAMENT_REWARDS = {
			Top1 = {
				['Hamburger'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Hamburger', 1}
					}
				},
				['Coffee'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Coffee', 1}
					}
				},
				['Onigiri'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Onigiri', 1}
					}
				},
				['Luffy\'s Meat'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Hamburger', 1}
					}
				},
				['Premium Pass'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Premium Pass', 1}
					}
				}
			},
			
			Top5 = {
				['Hamburger'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Hamburger', 1}
					}
				},
				['Coffee'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Coffee', 1}
					}
				},
				['Onigiri'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Onigiri', 1}
					}
				},
				['Luffy\'s Meat'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Hamburger', 1}
					}
				},
				['Premium Pass'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Premium Pass', 1}
					}
				}
			},
			
			Top10 = {
				['Hamburger'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Hamburger', 1}
					}
				},
				['Coffee'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Coffee', 1}
					}
				},
				['Onigiri'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Onigiri', 1}
					}
				},
				['Luffy\'s Meat'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Hamburger', 1}
					}
				},
				['Premium Pass'] = {
					Count = 1,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Premium Pass', 1}
					}
				}
			}
		},
		
		MODIFIERS = {
			{
				Name = '-80% Move Speed',
				ModifierKey = 'MovementSpeed',
				Value = 14
			}
		},
		
		POSSIBLE_MAPS = {'Restaurant', 'Ghoul Cafe', 'Infinite Castle', 'Sky'}
	},
	
	AFK = {
		COINS_PER_30_SEC = 5,
		GEMS_PER_30_SEC = 5
	},
	
	Dialogs = {
		AFK = {
			Header = '@topraksahin02',
			Description = 'Do want to teleport to AFK World?'
		},
		Tournaments = {
			Header = '@NoorVFX',
			Description = 'Tournaments?'
		}
	},
	
	Vectors = {
		COINS = 'rbxassetid://75280237000138',
		GEM = 'rbxassetid://122220601991100'
	},
	
	Packs = {
		['Pack 1'] = {
			Description = 'pack description',
			ID = 3364542997,
			Items = {
				{
					Name = 'Hamburger',
					Count = 7,
					Reward = {
						Service = 'InventoryService',
						Function = 'AddItem',
						Args = {'Hamburger', 7}
					}
				}
			}
		}
	}
}


RET.VALID_INTERACTABLES = {
	RET.Tags.SUBMIT_ORDER,
	RET.Tags.DISHES,
	RET.Tags.COUNTERS,
	RET.Tags.INGREDIENT_BOX,
	RET.Tags.GARBAGE,
	RET.Tags.STOVE,
	RET.Tags.SINK,
	RET.Tags.THROWN_ITEMS,
	RET.Tags.FIRE_EXTINGUISHER
}

RET.BURNABLE_INTERACTABLES = {
	RET.Tags.COUNTERS
}

RET.KITCHENWARES = {RET.Tags.CUTTING_BOARD, RET.Tags.STOVE, RET.Tags.SINK}

RET.SERVER_TYPE = RET.Servers.PLACE_INFO[RET.Servers.PLACES[game.GameId][game.PlaceId]].ServerType

RET.ROUND_MAPS = {}
for placeName, placeInfo in RET.Servers.PLACE_INFO do
	if placeInfo.ServerType == 'Round' then
		table.insert(RET.ROUND_MAPS, placeName)
	end
end

return table.freeze(RET)