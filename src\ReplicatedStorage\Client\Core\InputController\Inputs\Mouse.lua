-- Mouse
-- <PERSON>
-- December 28, 2017

--[[
	
	Vector2   Mouse:GetPosition()
	Vector2   Mouse:GetDel<PERSON>()
	Void      Mouse:Lock()
	Void      Mouse:LockCenter()
	Void      Mouse:Unlock()
	Ray       Mouse:GetRay(distance)
	Ray       Mouse:GetRayFromXY(x, y)
	Void      Mouse:SetMouseIcon(iconId)
	Void      Mouse:SetMouseIconEnabled(isEnabled)
	Boolean   Mouse:IsMouseIconEnabled()
	Booleam   Mouse:IsButtonPressed(mouseButton)
	Many      Mouse:Cast(ignoreDescendantsInstance, terrainCellsAreCubes, ignoreWater)
	Many      Mouse:CastWithIgnoreList(ignoreDescendantsTable, terrainCellsAreCubes, ignoreWater)
	Many      Mouse:CastWithWhitelist(whitelistDescendantsTable, ignoreWater)
	
	Mouse.LeftDown()
	Mouse.LeftUp()
	Mouse.RightDown()
	Mouse.RightUp()
	Mouse.MiddleDown()
	Mouse.MiddleUp()
	Mouse.Moved()
	Mouse.Scrolled(amount)
	
--]]



local Mouse = {}

local playerMouse = game:GetService'Players'.LocalPlayer:GetMouse()
local userInput = game:GetService('UserInputService')

local signal = require(game:GetService'ReplicatedStorage'.SharedModules.Signal)

local cam = workspace.CurrentCamera

local workspace = workspace
local RAY = Ray.new

local RAY_DISTANCE = 999


function Mouse.GetPosition()
	return userInput:GetMouseLocation()
end


function Mouse.GetDelta()
	return userInput:GetMouseDelta()
end


function Mouse.Lock()
	userInput.MouseBehavior = Enum.MouseBehavior.LockCurrentPosition
end


function Mouse.LockCenter()
	userInput.MouseBehavior = Enum.MouseBehavior.LockCenter
end


function Mouse.Unlock()
	userInput.MouseBehavior = Enum.MouseBehavior.Default
end


function Mouse.SetMouseIcon(iconId)
	playerMouse.Icon = (iconId and ('rbxassetid://' .. iconId) or '')
end


function Mouse.SetMouseIconEnabled(enabled)
	userInput.MouseIconEnabled = enabled
end


function Mouse.IsMouseIconEnabled()
	return userInput.MouseIconEnabled
end


function Mouse.IsButtonPressed(mouseButton)
	return userInput:IsMouseButtonPressed(mouseButton)
end


function Mouse.GetRay(distance)
	local mousePos = userInput:GetMouseLocation()
	local viewportMouseRay = cam:ViewportPointToRay(mousePos.X, mousePos.Y)
	return RAY(viewportMouseRay.Origin, viewportMouseRay.Direction * distance)
end


function Mouse.GetRayFromXY(x, y)
	local viewportMouseRay = cam:ViewportPointToRay(x, y)
	return RAY(viewportMouseRay.Origin, viewportMouseRay.Direction)
end


function Mouse.Cast(ignoreDescendantsInstance, terrainCellsAreCubes, ignoreWater)
	return workspace:FindPartOnRay(Mouse.GetRay(RAY_DISTANCE), ignoreDescendantsInstance, terrainCellsAreCubes, ignoreWater)
end


function Mouse.CastWithIgnoreList(ignoreDescendantsTable, terrainCellsAreCubes, ignoreWater)
	return workspace:FindPartOnRayWithIgnoreList(Mouse.GetRay(RAY_DISTANCE), ignoreDescendantsTable, terrainCellsAreCubes, ignoreWater)
end


function Mouse.CastWithWhitelist(whitelistDescendantsTable, ignoreWater)
	return workspace:FindPartOnRayWithWhitelist(Mouse.GetRay(RAY_DISTANCE), whitelistDescendantsTable, ignoreWater)
end


function Mouse.Hit(rayParams: RaycastParams): RaycastResult
	local unitRay = workspace.CurrentCamera:ScreenPointToRay(playerMouse.X, playerMouse.Y)
	return workspace:Raycast(unitRay.Origin, unitRay.Direction * 500, rayParams)
end


function Mouse.Start()
	-- Mouse.SetMouseIcon('4784799668')
end


function Mouse.Init()
	Mouse.LeftDown   = signal.new()
	Mouse.LeftUp     = signal.new()
	Mouse.RightDown  = signal.new()
	Mouse.RightUp    = signal.new()
	Mouse.MiddleDown = signal.new()
	Mouse.MiddleUp   = signal.new()
	Mouse.Moved      = signal.new()
	Mouse.Scrolled   = signal.new()

	userInput.InputBegan:Connect(function(input, processed)
		if processed then return end
		if input.UserInputType == Enum.UserInputType.MouseButton1 then
			Mouse.LeftDown:Fire()
		elseif input.UserInputType == Enum.UserInputType.MouseButton2 then
			Mouse.RightDown:Fire()
		elseif input.UserInputType == Enum.UserInputType.MouseButton3 then
			Mouse.MiddleDown:Fire()
		end
	end)

	userInput.InputEnded:Connect(function(input, processed)
		if input.UserInputType == Enum.UserInputType.MouseButton1 then
			Mouse.LeftUp:Fire()
		elseif input.UserInputType == Enum.UserInputType.MouseButton2 then
			Mouse.RightUp:Fire()
		elseif input.UserInputType == Enum.UserInputType.MouseButton3 then
			Mouse.MiddleUp:Fire()
		end
	end)

	userInput.InputChanged:Connect(function(input, processed)
		if input.UserInputType == Enum.UserInputType.MouseMovement then
			Mouse.Moved:Fire()
		elseif input.UserInputType == Enum.UserInputType.MouseWheel then
			if not processed then
				Mouse.Scrolled:Fire(input.Position.Z)
			end
		end
	end)
end


return Mouse