local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local ServerScriptService = game:GetService('ServerScriptService')
local HttpService = game:GetService('HttpService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local TableUtil = require(ReplicatedStorage.SharedModules.TableUtil)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Packets = require(ReplicatedStorage.Data.Packets)
local Characters = require(ReplicatedStorage.Data.Characters)
local XPPerLevel = require(ReplicatedStorage.Data.XPPerLevel)
local Items = require(ReplicatedStorage.Data.Items)
local SetCharacterAttachments = require(ReplicatedStorage.SharedCoreSystems.SetCharacterAttachments)
local SetCharacterCosmetic = require(ReplicatedStorage.SharedCoreSystems.SetCharacterCosmetic)

local CharacterService = {
	SessionXPGains = {}
}


local function dashFX(_, plr: Player)
	Packets.Visuals.DashFX.sendToAll(plr.Name)
end


local function characterSet(plr: Player)
	local thisCharacterInfo = Characters[plr:GetAttribute'Character']
	
	plr.Character:WaitForChild'Animate'
	
	if thisCharacterInfo and thisCharacterInfo.Animations and thisCharacterInfo.Animations.Idle then
		plr.Character.Animate.idle.Animation1.AnimationId = thisCharacterInfo.Animations.Idle
		plr.Character.Animate.idle.Animation2.AnimationId = thisCharacterInfo.Animations.Idle
	else
		plr.Character.Animate.idle.Animation1.AnimationId = 'rbxassetid://84707419155609'
		plr.Character.Animate.idle.Animation2.AnimationId = 'rbxassetid://84707419155609'
	end
	
	if thisCharacterInfo and thisCharacterInfo.Animations and thisCharacterInfo.Animations.Walk then
		plr.Character.Animate.run.RunAnim.AnimationId = thisCharacterInfo.Animations.Walk
		plr.Character.Animate.walk.WalkAnim.AnimationId = thisCharacterInfo.Animations.Walk
	else
		plr.Character.Animate.walk.WalkAnim.AnimationId = 'rbxassetid://92906309423334'
		plr.Character.Animate.run.RunAnim.AnimationId = 'rbxassetid://92906309423334'
	end
	
	SetCharacterAttachments(plr.Character, plr:GetAttribute'Character')
	CharacterService.EquipEquippedCosmetics(plr)
end


function CharacterService.FeedCharacter(plr: Player, foods: { Character: string, Foods: { [string]: number } })
	local InventoryService = Server.GetService('InventoryService')
	local DataService = Server.GetService('DataService')
	
	local PlayerCharacters = DataService.Get(plr, 'Characters')
	
	for itemName, count in foods.Foods do
		CharacterService.AddXPToCharacter(plr, Items[itemName].XPReward * count, foods.Character)
		InventoryService.AddItem(plr, itemName, -count)
	end
	
	Packets.Inventory.GetCharacterLevel.sendTo({
		Level = PlayerCharacters[foods.Character].Level,
		XP = PlayerCharacters[foods.Character].XP
	}, plr)
	
	Packets.Inventory.CharacterFed.sendTo(nil, plr)
end


function CharacterService.GetCharacterNameFromID(plr: Player, charID: string): string
	local DataService = Server.GetService('DataService')
	
	local PlayerCharacters = DataService.Get(plr, 'Characters')
	local characterName = PlayerCharacters[charID] and PlayerCharacters[charID].Character
	
	return characterName
end


function CharacterService.FuseCharacter(plr: Player, data: { BaseCharacter: string, FusingCharacters: {string} })
	local xpToAdd = 0
	for _, v in data.FusingCharacters do
		xpToAdd += Characters[CharacterService.GetCharacterNameFromID(plr, v)].FuseXP
		CharacterService.RemoveCharacter(plr, v)
	end
	
	CharacterService.AddXPToCharacter(plr, xpToAdd, data.BaseCharacter)
end


function CharacterService.EquipEquippedCharacter(plr: Player)
	local DataService = Server.GetService('DataService')
	local equippedCharacter = DataService.Get(plr, 'EquippedCharacter')
	CharacterService.EquipCharacter(plr, equippedCharacter)
end


function CharacterService.EquipEquippedCosmetics(plr: Player)
	local DataService = Server.GetService('DataService')
	
	if plr.Character and plr.Character.PrimaryPart then
		for _, v in plr.Character:GetChildren() do
			if v:IsA'Model' then
				local itemInfo = Items[v.Name]
				if itemInfo and itemInfo.Type == 'Cosmetic' then
					v:Destroy()
				end
			end
		end
		
		for _, v in DataService.Get(plr, 'EquippedCosmetics') do
			CharacterService.EquipCosmetic(plr, v)
		end
	end
end


function CharacterService.EquipCosmetic(plr: Player, cosmetic: string, toggle: boolean?)
	local DataService = Server.GetService('DataService')
	
	local equippedCosmetics = DataService.Get(plr, 'EquippedCosmetics')
	
	local cosmeticInfo = Items[cosmetic]
	if cosmeticInfo and cosmeticInfo.Type == 'Cosmetic' then
		local isEquipped = table.find(equippedCosmetics, cosmetic) ~= nil
		
		if toggle and isEquipped == true then
			table.remove(equippedCosmetics, table.find(equippedCosmetics, cosmetic))
			CharacterService.EquipEquippedCosmetics(plr)
		else
			if not isEquipped then
				for idx, v in equippedCosmetics do
					if Items[v].CosmeticType == cosmeticInfo.Type then
						table.remove(equippedCosmetics, idx)
						break
					end
				end

				table.insert(equippedCosmetics, cosmetic)
			end
			
			SetCharacterCosmetic(plr.Character, cosmetic)
		end
		
		Packets.Inventory.GetEquippedCosmetics.sendTo(DataService.Get(plr, 'EquippedCosmetics'), plr)
	end
end


function CharacterService.EquipCharacter(plr: Player, characterID: string?)
	local DataService = Server.GetService('DataService')
	
	local profile = DataService.GetProfile(plr)
	
	profile.EquippedCharacter = characterID
	
	plr:SetAttribute('CharacterID', characterID)
	plr:SetAttribute('Character', profile.Characters[characterID] and profile.Characters[characterID].Character)
	characterSet(plr)
	
	local PlayerCharacters = profile.Characters
	
	for k, v in PlayerCharacters do
		v.Equipped = k == characterID
	end
	
	Packets.Inventory.UpdateCharacterInventory.sendTo(PlayerCharacters, plr)
end


function CharacterService.SellCharacter(plr: Player, characterID: string)
	local DataService = Server.GetService('DataService')
	local CurrencyService = Server.GetService('CurrencyService')
	
	local PlayerCharacters = DataService.Get(plr, 'Characters')
	
	if PlayerCharacters[characterID] then
		local ThisCharacterInfo = Characters[PlayerCharacters[characterID].Character]
		CurrencyService.AddCoins(plr, ThisCharacterInfo.SellValue, 'Selling')
		CharacterService.RemoveCharacter(plr, characterID)
	end
end


local DEFAULT_CHARACTER_DATA = { Character = nil, EquippedSkin = nil, TimeSummoned = 0, Favorite = false, Equipped = false, Locked = false, Uses = 0, Deliveries = 0, XP = 0, Level = 1 }
function CharacterService.AddCharacter(plr: Player, characterIDorName: string)
	local DataService = Server.GetService('DataService')
	
	local PlayerCharacters = DataService.Get(plr, 'Characters')
	
	local charAdded = false
	if not PlayerCharacters[characterIDorName] then
		local ThisCharacterID = HttpService:GenerateGUID(false)
		PlayerCharacters[ThisCharacterID] = TableUtil.Copy(DEFAULT_CHARACTER_DATA)
		
		PlayerCharacters[ThisCharacterID].Character = characterIDorName
		PlayerCharacters[ThisCharacterID].TimeSummoned = os.time()
		
		characterIDorName = ThisCharacterID
		charAdded = true
	end
	
	for k, v in DEFAULT_CHARACTER_DATA do
		if PlayerCharacters[characterIDorName][k] == nil then
			if k =='TimeSummoned' then
				v = os.time()
			end

			PlayerCharacters[characterIDorName][k] = v
		end
	end
	
	if charAdded then
		Packets.Inventory.UpdateCharacterInventory.sendTo(PlayerCharacters, plr)

		local res = {}
		for k in PlayerCharacters do
			table.insert(res, k)
		end
		Packets.Inventory.OwnedCharacters.sendTo(res, plr)
	end
end


function CharacterService.RemoveCharacter(plr: Player, characterID: string)
	local DataService = Server.GetService('DataService')

	local PlayerCharacters = DataService.Get(plr, 'Characters')
	
	if PlayerCharacters[characterID] then
		PlayerCharacters[characterID] = nil
		
		Packets.Inventory.UpdateCharacterInventory.sendTo(PlayerCharacters, plr)
		
		local res = {}
		for k in PlayerCharacters do
			table.insert(res, k)
		end
		Packets.Inventory.OwnedCharacters.sendTo(res, plr)
	end
end


function CharacterService.ToggleLockCharacter(plr: Player, character: string)
	local DataService = Server.GetService('DataService')
	
	local PlayerCharacters = DataService.Get(plr, 'Characters')
	PlayerCharacters[character].Locked = not PlayerCharacters[character].Locked
	Packets.Inventory.UpdateCharacterInventory.sendTo(PlayerCharacters, plr)
end


function CharacterService.ToggleFavoriteCharacter(plr: Player, character: string)
	local DataService = Server.GetService('DataService')
	local PlayerCharacters = DataService.Get(plr, 'Characters')
	PlayerCharacters[character].Favorite = not PlayerCharacters[character].Favorite
	Packets.Inventory.UpdateCharacterInventory.sendTo(PlayerCharacters, plr)
end


function CharacterService.AddXPToCharacter(plr: Player, xp: number, character: string?)
	local DataService = Server.GetService('DataService')
	
	if not character then
		character = DataService.Get(plr, 'EquippedCharacter')
		if not character then return end
	end
	
	local PlayerCharacters = DataService.Get(plr, 'Characters')
	local ThisCharacterInfo = PlayerCharacters[character]
	
	if not ThisCharacterInfo then return end
	
	ThisCharacterInfo.XP += xp
	
	CharacterService.SessionXPGains[plr] = (CharacterService.SessionXPGains[plr] or 0) + xp
	
	while ThisCharacterInfo.XP >= XPPerLevel[ThisCharacterInfo.Level] do
		ThisCharacterInfo.XP -= XPPerLevel[ThisCharacterInfo.Level]
		ThisCharacterInfo.Level += 1
	end
	
	Packets.Inventory.UpdateCharacterInventory.sendTo(PlayerCharacters, plr)
end


function CharacterService.EquipSkin(plr: Player, skinName: string)
	local DataService = Server.GetService('DataService')

	local PlayerCharacters = DataService.Get(plr, 'Characters')
	
	for k, characterInfo in PlayerCharacters do
		local characterName = characterInfo.Character
		if Characters[characterName].Skins and Characters[characterName].Skins[skinName] then
			characterInfo.EquippedSkin = skinName
			-- TODO: Set equipped skin if equipped (backend)
			break
		end
	end
	
	local res = {}
	for k, characterInfo in PlayerCharacters do
		local characterName = characterInfo.Character
		if characterInfo.EquippedSkin then
			res[characterName] = characterInfo.EquippedSkin
		end
	end
	Packets.Skins.GetEquipedSkins.sendTo(res, plr)
end


function CharacterService.SellSkin(plr: Player, skinName: string)
	local DataService = Server.GetService('DataService')
	local CurrencyService = Server.GetService('CurrencyService')
	
	local PlayerCharacters = DataService.Get(plr, 'Characters')
	local PlayerSkins = DataService.Get(plr, 'Skins')
	
	for k, characterInfo in PlayerCharacters do
		local characterName = characterInfo.Character
		if Characters[characterName].Skins and Characters[characterName].Skins[skinName] then
			CurrencyService.AddCoins(plr, Characters[characterName].Skins[skinName].SellValue, 'Selling')
			
			if characterInfo.EquippedSkin == skinName then
				-- TODO: Remove skin if equipped (backend)
				characterInfo.EquippedSkin = nil
			end
			
			PlayerSkins[skinName] = nil
		end
	end
end


function CharacterService.AddSkin(plr: Player, skinName: string)
	local DataService = Server.GetService('DataService')
	
	local profile = DataService.GetProfile(plr)
	
	if profile.Skins[skinName] then return end
	
	profile.Skins[skinName] = {}
	
	Packets.Skins.GetSkins.sendTo(profile.Skins, plr)
end


function CharacterService._PlayerAdded(plr: Player)
	local DataService = Server.GetService('DataService')
	
	local PlayerCharacters = DataService.Get(plr, 'Characters')
	
	for k in PlayerCharacters do
		CharacterService.AddCharacter(plr, k)
	end
	
	plr:GetAttributeChangedSignal'Character':Connect(function()characterSet(plr)end)
	
	ClassExtension.Player.CharacterAdded(plr, function()
		plr.Character:WaitForChild'Animate'
		
		plr.Character.Animate.walk.WalkAnim.AnimationId = 'rbxassetid://82701113924148'
		plr.Character.Animate.run.RunAnim.AnimationId = 'rbxassetid://82701113924148'
		
		plr.Character.Animate.idle.Animation1.AnimationId = 'rbxassetid://84707419155609'
		plr.Character.Animate.idle.Animation2.AnimationId = 'rbxassetid://84707419155609'
		
		CharacterService.EquipEquippedCharacter(plr)
		CharacterService.EquipEquippedCosmetics(plr)

		plr.Character:WaitForChild'Humanoid'

		plr.Character.Humanoid.JumpPower = Client.Immutable.CharSettings.JUMP_POWER

		local desc = Players:GetHumanoidDescriptionFromUserId(plr.UserId)
		plr.Character:WaitForChild'Humanoid'
		plr.Character.Humanoid:ApplyDescriptionReset(desc)
		
		if Client.Immutable.SERVER_TYPE == 'Lobby' then
			plr.Character.Humanoid.JumpPower = 50
		end
	end)
end


function CharacterService._Start()
	local DataService = Server.GetService('DataService')
	
	Packets.Inventory.EquipCharacter.listen(function(data: string?, plr: Player)
		CharacterService.EquipCharacter(plr, data)
	end)
	
	Packets.Inventory.UpdateCharacterInventory.listen(function(_, plr: Player)
		Packets.Inventory.UpdateCharacterInventory.sendTo(DataService.Get(plr, 'Characters'), plr)
	end)
	
	Packets.Inventory.SellCharacters.listen(function(data: {string}, plr: Player)
		for _, v in data do
			CharacterService.SellCharacter(plr, v)
		end
	end)
	
	Packets.Inventory.ToggleLockCharacter.listen(function(data: string, plr: Player)
		CharacterService.ToggleLockCharacter(plr, data)
	end)
	
	Packets.Inventory.ToggleFavoriteCharacter.listen(function(data: string, plr: Player)
		CharacterService.ToggleFavoriteCharacter(plr, data)
	end)
	
	Packets.Inventory.OwnedCharacters.listen(function(_, plr: Player)
		local res = {}
		for charId, charInfo in DataService.Get(plr, 'Characters') do
			if not table.find(res, charInfo.Character) then
				table.insert(res, charInfo.Character)
			end
		end
		Packets.Inventory.OwnedCharacters.sendTo(res, plr)
	end)
	
	Packets.Inventory.GetCharacterLevel.listen(function(data: { Character: string }, plr: Player)
		local PlayerCharacters = DataService.Get(plr, 'Characters')
		
		Packets.Inventory.GetCharacterLevel.sendTo({
			Level = PlayerCharacters[data.Character].Level,
			XP = PlayerCharacters[data.Character].XP
		}, plr)
	end)
	
	Packets.Inventory.FeedCharacter.listen(function(data: { Character: string, Foods: { [string]: number } }, plr: Player)
		CharacterService.FeedCharacter(plr, data)
	end)
	
	Packets.Inventory.FuseCharacter.listen(function(data: { BaseCharacter: string, FusingCharacters: {string} }, plr: Player)
		CharacterService.FuseCharacter(plr, data)
	end)
	
	Packets.Skins.GetSkins.listen(function(_, plr: Player)
		Packets.Skins.GetSkins.sendTo(DataService.Get(plr, 'Skins'), plr)
	end)
	
	Packets.Skins.GetEquipedSkins.listen(function(_, plr: Player)
		local PlayerCharacters = DataService.Get(plr, 'Characters')
		
		local res = {}
		for k, characterInfo in PlayerCharacters do
			if characterInfo.EquippedSkin then
				res[characterInfo.Character] = characterInfo.EquippedSkin
			end
		end
		Packets.Skins.GetEquipedSkins.sendTo(res, plr)
	end)
	
	Packets.Skins.SellSkins.listen(function(data: {string}, plr: Player)
		for _, v in data do
			CharacterService.SellSkin(plr, v)
		end
	end)
	
	Packets.Visuals.DashFX.listen(dashFX)
end


return CharacterService