local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Announce = {}


function Announce.Announce(txt: string)
	local AnnouncementUI = Players.LocalPlayer.PlayerGui.SharedUI.Announcement
	
	AnnouncementUI.Text = txt
	AnnouncementUI.Visible = true
	
	task.delay(math.max(3, #txt / 10), function()
		if AnnouncementUI.Text == txt then
			AnnouncementUI.Visible = false
		end
	end)
end


function Announce._Start()
	local CmdrClient = require(ReplicatedStorage:WaitForChild'CmdrClient')
	
	CmdrClient:HandleEvent('Message', function(text, player)
		Announce.Announce(text)
	end)
end


return Announce