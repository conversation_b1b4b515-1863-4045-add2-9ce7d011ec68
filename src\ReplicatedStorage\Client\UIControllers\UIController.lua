local ReplicatedStorage = game:GetService('ReplicatedStorage')
local TweenService = game:GetService('TweenService')
local Players = game:GetService('Players')
local SoundService = game:GetService('SoundService')
local UserInputService = game:GetService('UserInputService')
local CollectionService = game:GetService('CollectionService')
local GamepadService = game:GetService('GamepadService')
local ProximityPromptService = game:GetService('ProximityPromptService')

local Client = require(ReplicatedStorage.Client)

local Signal = require(ReplicatedStorage.SharedModules.Signal)
local spr = require(ReplicatedStorage.SharedModules.spr)

local UIController = {
	PageToggled = Signal.new() -- frame: GuiObject, status: boolean
}

local MOBILE_TOUCH, PC_CLICK = Enum.UserInputType.Touch, Enum.UserInputType.MouseButton1


do
	local CLOSE_POS = UDim2.fromScale(.5, 1.6)

	local openedFrame: GuiObject?
	local stayOpened: boolean?


	function UIController.Close(expectedFrame: GuiObject?): boolean?
		if expectedFrame and openedFrame ~= expectedFrame then
			expectedFrame.Visible = false
			return false
		end

		if openedFrame then
			local wasOpenedFrame = openedFrame
			openedFrame = nil

			spr.stop(wasOpenedFrame)

			Client.GetController'EffectController'.Do('CameraZoomOut', true)
			spr.target(wasOpenedFrame, .75, 2, { Position = CLOSE_POS })

			UIController.PageToggled:Fire(wasOpenedFrame, false)

			task.delay(.5, function()
				if wasOpenedFrame ~= openedFrame then
					wasOpenedFrame.Visible = false
				end
			end)
		end
		
		GamepadService:DisableGamepadCursor()

		stayOpened = nil
	end


	function UIController.Open(frame: GuiObject, closeSameFrame: boolean?, forceOpen: boolean?): boolean?
		if stayOpened or (openedFrame == frame and not closeSameFrame) then return false end

		if openedFrame == frame and closeSameFrame then
			UIController.Close()
			return false
		end

		local wasOpenedFrame
		if openedFrame then
			wasOpenedFrame = openedFrame
			UIController.Close(openedFrame)
		end

		if forceOpen then
			stayOpened = true
		end

		openedFrame = frame

		openedFrame.Visible = true
		openedFrame.Position = CLOSE_POS

		GamepadService:EnableGamepadCursor(openedFrame)

		Client.GetController'EffectController'.Do('CameraZoomOut', false)
		spr.target(openedFrame, 1.45, 5, { Position = UDim2.fromScale(.5, .5), Rotation = 0 })

		UIController.PageToggled:Fire(openedFrame, true, wasOpenedFrame)
	end

	local rotateTweenInfo = TweenInfo.new(.1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
	function UIController.BindCloseButton(frame: GuiObject, customCloseButton: (TextButton | ImageButton)?, callback: (() -> ())?)
		if not customCloseButton and not frame:FindFirstChild'Close' then return end
		
		local Button: TextButton | ImageButton = customCloseButton and customCloseButton or frame.Close

		local UIScale = Button:FindFirstChild'UIScale' or Instance.new('UIScale')
		UIScale.Parent = Button

		Button.SelectionGained:Connect(function()
			TweenService:Create(Button, rotateTweenInfo, {Rotation = 4}):Play()
		end)

		Button.MouseEnter:Connect(function()
			TweenService:Create(Button, rotateTweenInfo, {Rotation = 4}):Play()
		end)

		Button.MouseLeave:Connect(function()
			TweenService:Create(Button, rotateTweenInfo, {Rotation = 0}):Play()
			TweenService:Create(UIScale, TweenInfo.new(.1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {Scale = 1}):Play()
		end)
		
		Button.SelectionLost:Connect(function()
			TweenService:Create(Button, rotateTweenInfo, {Rotation = 0}):Play()
			TweenService:Create(UIScale, TweenInfo.new(.1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {Scale = 1}):Play()
		end)

		Button.MouseButton1Down:Connect(function()
			TweenService:Create(UIScale, TweenInfo.new(.1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {Scale = .75}):Play()
		end)

		Button.MouseButton1Up:Connect(function()
			TweenService:Create(UIScale, TweenInfo.new(.1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {Scale = 1}):Play()
		end)

		Button.MouseButton1Click:Connect(function()
			UIController.Close(frame)
		end)
	end

	function UIController.GetOpenedFrame(): GuiObject?
		return openedFrame
	end
end


local function setupClickSound()
	local function setButton(button: ImageButton | TextButton)
		if button:IsA'ImageButton' or button:IsA'TextButton' then
			button.MouseButton1Click:Connect(function()
				SoundService:PlayLocalSound(SoundService.SFX.Click)
			end)
		end
	end
	
	Players.LocalPlayer.PlayerGui.DescendantAdded:Connect(setButton)
	for _, v in Players.LocalPlayer.PlayerGui:GetDescendants() do
		setButton(v)
	end
end


function UIController.SetButtonAnim(button: ImageButton | TextButton)
	local UIScale = button:FindFirstChild'UIScale'
	if not UIScale then
		UIScale = Instance.new('UIScale')
		UIScale.Parent = button
	end
	
	button.MouseButton1Down:Connect(function()
		spr.target(UIScale, .6, 5, {Scale = .8})
	end)

	button.MouseButton1Up:Connect(function()
		spr.target(UIScale, .6, 5, {Scale = 1})
	end)

	button.MouseEnter:Connect(function()
		spr.target(UIScale, .9, 5, {Scale = 1.1})
	end)
	
	button.SelectionGained:Connect(function()
		spr.target(UIScale, .9, 5, {Scale = 1.1})
	end)

	button.MouseLeave:Connect(function()
		spr.target(UIScale, .6, 5, {Scale = 1})
	end)
	
	button.SelectionLost:Connect(function()
		spr.target(UIScale, .6, 5, {Scale = 1})
	end)
end


function UIController.HidePrompts(hide: boolean)
	ProximityPromptService.Enabled = not hide
end


function UIController._Start()
	local PlayerGui = Players.LocalPlayer:WaitForChild'PlayerGui'
	local LobbyUI = PlayerGui:WaitForChild'LobbyUI'
	
	local openedFrame = UIController.GetOpenedFrame()
	for _, v in LobbyUI.Pages:GetChildren() do
		UIController.BindCloseButton(v)
		
		if openedFrame == nil or openedFrame ~= v then
			UIController.Close(v)
		end
	end
	
	task.defer(setupClickSound)
end


return UIController
