local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local CharactersData = require(ReplicatedStorage.Data.Characters)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local SetCharacterAttachments = require(ReplicatedStorage.SharedCoreSystems.SetCharacterAttachments)

local ViewportController = {}

local HEADSHOT_OFFSET = CFrame.new(0, 1.6, -2.45) * CFrame.Angles(math.rad(10), math.rad(180), 0)
local UPPER_BODY_OFFSET = CFrame.new(-.5, 1.4, -2.9) * CFrame.Angles(math.rad(15), math.rad(190), 0)


function ViewportController.SetIdleAnimationOnViewportRig(viewport: ViewportFrame, character: string?)
	local rig = viewport.WorldModel:FindFirstChildOfClass('Model')
	
	if not rig then return end
	
	for _, v in rig.Humanoid.Animator:GetPlayingAnimationTracks() do
		v:Stop()
		v:Destroy()
	end
	
	local animId = 'rbxassetid://84707419155609'
	if character and CharactersData[character].Animations then
		animId = CharactersData[character].Animations.Idle
	end
	
	local animObj = Instance.new('Animation')
	animObj.AnimationId = animId
	
	task.spawn(function()
		local track: AnimationTrack = rig.Humanoid.Animator:LoadAnimation(animObj)
		track.Looped = true
		track:Play()
	end)
end


function ViewportController.SetCharacterHeadshot(viewport: ViewportFrame, character: string?, shot: string?): Model
	viewport:ClearAllChildren()
	
	local camera: Camera = viewport:FindFirstChildWhichIsA('Camera')
	if not camera then
		camera = Instance.new('Camera')
		camera.Parent = viewport
	end
	
	viewport.CurrentCamera = camera
	
	local worldModel = viewport:FindFirstChild('WorldModel')
	if not worldModel then
		worldModel = Instance.new('WorldModel')
		worldModel.Parent = viewport
	end
	
	local rig = ClassExtension.Misc.CreateLocalPlayerDummy()
	rig.Parent = worldModel
	
	if character then
		SetCharacterAttachments(rig, character)
	end
	
	shot = shot or 'Headshot'
	
	local offset
	if shot == 'Headshot' then
		offset = CharactersData[character] and CharactersData[character].HeadshotOffset or HEADSHOT_OFFSET
	elseif shot == 'UpperBody' then
		offset = CharactersData[character] and CharactersData[character].UpperBodyOffset or UPPER_BODY_OFFSET
	end
	
	camera.CFrame = rig:GetPivot() * offset
	
	viewport.LightDirection = Vector3.new(0, 0, 1)
	
	viewport.Visible = true
	
	ViewportController.SetIdleAnimationOnViewportRig(viewport, character)
	
	return rig
end


function ViewportController._Start()
	
end


return ViewportController