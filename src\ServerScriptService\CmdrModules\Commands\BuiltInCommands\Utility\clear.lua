local Players = game:GetService("Players")

return {
	Name = "clear",
	Aliases = {},
	Description = "Clear all lines above the entry line of the Cmdr window.",
	Group = 0,
	Args = {},
	ClientRun = function()
		local player = Players.LocalPlayer
		local gui = player:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("PlayerGui"):Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("Cmdr")
		local frame = gui:Wait<PERSON>or<PERSON>hil<PERSON>("Frame")

		if gui and frame then
			for _, child in pairs(frame:GetChildren()) do
				if child.Name == "Line" and child:<PERSON><PERSON>("TextBox") then
					child:<PERSON><PERSON>y()
				end
			end
		end
		return ""
	end
}
