local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)

local ChallengeController = {}


local function updateChallengeInterface(data: { StartingIn: number?, ChallengeID: string, Map: string, Players: {Player}, Rewards: {{ Count: number, Name: string }} })
	local UIController = Client.GetController('UIController')
	local HUDController = Client.GetController('HUDController')
	local ItemController = Client.GetController('ItemController')
	
	local PartyPopup = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.PartyPopup
	
	if table.find(data.Players, Players.LocalPlayer) then
		UIController.Open(PartyPopup)
		HUDController.HideHUDPortion('Left')
		HUDController.HideHUDPortion('Right')
	else
		UIController.Close()
		HUDController.ShowHUDPortion('Left')
		HUDController.ShowHUDPortion('Right')
		return
	end
	
	for _, v in PartyPopup.Players.Container.Contents:GetChildren() do
		if v:IsA'GuiObject' then v:Destroy() end
	end
	
	for _, v in data.Players do
		local PlayerContainer = ReplicatedStorage.Assets.Templates.PlayerContainer:Clone()
		PlayerContainer.Image = `rbxthumb://type=AvatarHeadShot&id={v.UserId}&w=180&h=180`
		PlayerContainer.ItemName.Text = v.DisplayName
		PlayerContainer.Parent = PartyPopup.Players.Container.Contents
	end
	
	for _, v in PartyPopup.Info.Rewards.Contents:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	for _, v in data.Rewards do
		local ItemFrame = ItemController.CreateItem({
			ItemName = v.Name,
			Count = v.Count,
			Size = UDim2.fromScale(.161, .778),
			Parent = PartyPopup.Info.Rewards.Contents
		})
	end
	
	if data.StartingIn then
		PartyPopup.Info.Banner.Timer.Text = data.StartingIn .. 's'
	end
	
	PartyPopup.Info.Banner.LevelName.Text = 'CHALLENGE'
	PartyPopup.Info.Banner.ActNumber.Text = Client.Immutable.Challenges[data.ChallengeID].EffectName
	PartyPopup.Info.Banner.Diffiulcty.Text = 'MAP: ' .. data.Map
	
	PartyPopup.Buttons.Invite.Visible = false
	PartyPopup.Buttons.Start.Visible = false
	PartyPopup.Info.Banner.Timer.Visible = true
end


function ChallengeController._Start()
	Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'
	
	local PartyPopup = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.PartyPopup
	
	PartyPopup.Buttons.Leave.MouseButton1Click:Connect(function()
		Packets.Challenge.LeaveChallenge.send()
	end)
	
	Packets.Challenge.UpdateChallengeInterface.listen(updateChallengeInterface)
end


return ChallengeController