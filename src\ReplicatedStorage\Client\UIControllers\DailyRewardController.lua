local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)

local DailyRewardsData = require(ReplicatedStorage.Data.DailyRewards)
local Packets = require(ReplicatedStorage.Data.Packets)
local Table = require(ReplicatedStorage.SharedModules.TableUtil)
local NumberController = require(ReplicatedStorage.SharedModules.NumberController)

local DailyRewardController = {}


local function loadDailyRewardFrame(data: { Claimed: {boolean}, Streak: number, TimeUntilNextDay: number })
	local UIController = Client.GetController('UIController')
	
	local DailyRewardsFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.DailyRewards
	
	for _, v in Table.Extend(DailyRewardsFrame.Content.Holder.Row:GetChildren(), DailyRewardsFrame.Content.Holder.Row2:GetChildren()) do
		if v:IsA'ImageButton' then
			local dayNum = tonumber(v.Name:match'%d+')
			local thisRewardInfo = DailyRewardsData[dayNum]
			
			v.Content.PlaceHOlder.Image = thisRewardInfo.Image
			v.Content.Counter.Text = thisRewardInfo.ImageFooter
			v.Content.ItemName.Text = thisRewardInfo.Name
			
			if data.Claimed[dayNum] then
				v.BlackLayer.Visible = true
				v.BlackLayer.Claimed.Visible = true
				v.BlackLayer.Locked.Visible = false
			elseif dayNum > data.Streak then
				v.BlackLayer.Visible = true
				v.BlackLayer.Claimed.Visible = false
				v.BlackLayer.Locked.Visible = true
				
				if dayNum == data.Streak + 1 then
					v.BlackLayer.Locked.Visible = false
					v.BlackLayer.Claimed.Visible = true
					
					task.spawn(function()
						while true do
							v.BlackLayer.Claimed.Text = NumberController.HMS(data.TimeUntilNextDay)
							data.TimeUntilNextDay -= 1
							task.wait(1)
						end
					end)
				end
			else
				v.BlackLayer.Visible = false
				UIController.Open(DailyRewardsFrame)
			end
			
			v.MouseButton1Click:Connect(function()
				if v.BlackLayer.Visible then return end
				
				v.BlackLayer.Visible = true
				v.BlackLayer.Claimed.Visible = true
				v.BlackLayer.Locked.Visible = false
				
				Packets.Time.ClaimDailyReward.send(dayNum)
			end)
		end
	end
end


function DailyRewardController._Start()
	if Client.Immutable.SERVER_TYPE ~= 'Lobby' then return end
	
	Packets.Time.GetDailyRewardInfo.listen(loadDailyRewardFrame)
	Packets.Time.GetDailyRewardInfo.send()
end


return DailyRewardController