--[=[
	Author: <PERSON><PERSON><PERSON> (S<PERSON>veric)
	Achievements data
]=]

local ReplicatedStorage = game:GetService('ReplicatedStorage')

local Client = require(ReplicatedStorage.Client)

return table.freeze{
	['Play Restaurant'] = {
		BgImage = Client.Immutable.Servers.PLACE_INFO['Restaurant'].MapImage,
		Secondary = {
			['Restaurant Act 1'] = {
				Description = 'Complete Restaurant Act 1',
				Order = 1,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},

			['Restaurant Act 2'] = {
				Description = 'Complete Restaurant Act 2',
				Order = 2,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},

			['Restaurant Act 3'] = {
				Description = 'Complete Restaurant Act 3',
				Order = 3,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},

			['Restaurant Act 4'] = {
				Description = 'Complete Restaurant Act 4',
				Order = 4,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},
		}
	},

	['Play Sky'] = {
		BgImage = Client.Immutable.Servers.PLACE_INFO['Sky'].MapImage,
		Secondary = {
			['Sky Act 1'] = {
				Description = 'Complete Sky Act 1',
				Order = 1,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},

			['Sky Act 2'] = {
				Description = 'Complete Sky Act 2',
				Order = 2,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},

			['Sky Act 3'] = {
				Description = 'Complete Sky Act 3',
				Order = 3,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},

			['Sky Act 4'] = {
				Description = 'Complete Sky Act 4',
				Order = 4,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},
		}
	},

	['Play Ghoul Cafe'] = {
		BgImage = Client.Immutable.Servers.PLACE_INFO['Ghoul Cafe'].MapImage,
		Secondary = {
			['Ghoul Cafe Act 1'] = {
				Description = 'Complete Ghoul Cafe Act 1',
				Order = 1,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},

			['Ghoul Cafe Act 2'] = {
				Description = 'Complete Ghoul Cafe Act 2',
				Order = 2,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},

			['Ghoul Cafe Act 3'] = {
				Description = 'Complete Ghoul Cafe Act 3',
				Order = 3,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},

			['Ghoul Cafe Act 4'] = {
				Description = 'Complete Ghoul Cafe Act 4',
				Order = 4,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},
		}
	},

	['Play Infinite Castle'] = {
		BgImage = Client.Immutable.Servers.PLACE_INFO['Infinite Castle'].MapImage,
		Secondary = {
			['Infinite Castle Act 1'] = {
				Description = 'Complete Infinite Castle Act 1',
				Order = 1,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},

			['Infinite Castle Act 2'] = {
				Description = 'Complete Infinite Castle Act 2',
				Order = 2,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},

			['Infinite Castle Act 3'] = {
				Description = 'Complete Infinite Castle Act 3',
				Order = 3,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},

			['Infinite Castle Act 4'] = {
				Description = 'Complete Infinite Castle Act 4',
				Order = 4,
				Requirement = 1,

				Reward = {
					Name = 'Gems',
					Count = 50,

					Service = 'CurrencyService',
					Function = 'AddGems',
					Args = {50, 'Achievements'}
				}
			},
		}
	},
}