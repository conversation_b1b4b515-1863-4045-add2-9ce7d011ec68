local ServerScriptService = game:GetService('ServerScriptService')
local RunService = game:GetService('RunService')

local Server = if RunService:IsServer() then require(ServerScriptService.Server) else nil

return function(registry)
	local RankService = Server and Server.GetService('RankService')
	
	registry:RegisterHook('BeforeRun', function(context)
		if RunService:IsClient() and not context.Executor:GetAttribute'Rank' then
			context.Executor:GetAttributeChangedSignal'Rank':Wait()
		end
		
		local UserRank = RunService:IsClient() and context.Executor:GetAttribute'Rank' or RankService.GetRank(context.Executor)
		if UserRank == nil or UserRank < context.Group then
			return `You don't have permission to run this command`
		end
	end)
end