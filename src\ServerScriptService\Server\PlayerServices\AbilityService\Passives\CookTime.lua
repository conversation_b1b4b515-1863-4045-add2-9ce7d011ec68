local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local PassiveModule = {}


function PassiveModule.Get()
	local default = Server.Immutable.COOK_TIME
	
	local act = Server.GetService('RoundService').Act
	if act and Client.Immutable.ACT_CONFIG[act] then
		default = Client.Immutable.ACT_CONFIG[act].CookTime
	end
	
	return default
end


function PassiveModule.Init()
	
end


return PassiveModule