local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')

local Server = require(ServerScriptService.Server)

local Items = require(ReplicatedStorage.Data.Items)
local Packets = require(ReplicatedStorage.Data.Packets)

local InventoryService = {}


local DEFAULT_INVENTORY_DATA = { TimeCreated = 0, Count = 0 }
function InventoryService.AddItem(plr: Player, item: string, count: number)
	local DataService = Server.GetService('DataService')
	
	if not Items[item] then
		warn(`[InventoryService] Item '{item}' does not exist.`)
		return
	end
	
	local Inventory = DataService.Get(plr, 'Inventory')
	
	if not Inventory[item] then
		Inventory[item] = {}
	end
	
	for k, v in DEFAULT_INVENTORY_DATA do
		if Inventory[item][k] == nil then
			if k =='TimeCreated' then
				v = os.time()
			end

			Inventory[item][k] = v
		end
	end
	
	Inventory[item].Count += count or 1
	
	Packets.Inventory.UpdateItemsInventory.sendTo(Inventory, plr)
end


function InventoryService.UseItem(plr: Player, item: string)
	local DataService = Server.GetService('DataService')
	local MonetizationService = Server.GetService('MonetizationService')
	local BoostService = Server.GetService('BoostService')
	local CharacterService = Server.GetService('CharacterService')

	local Inventory = DataService.Get(plr, 'Inventory')
	
	if not Inventory[item] then return end
	
	local ItemData = Items[item]
	if ItemData.Type == 'Devproduct' or ItemData.Type == 'Gamepass' then
		local f = MonetizationService[`{ItemData.Type}Functions`][ItemData.MarketplaceKey]
		if type(f) == 'function' then
			f(plr, true)
		elseif type(f) == 'table' and f.OnPurchase then
			f.OnPurchase(plr, true)
		end
	elseif ItemData.Type == 'Boost' then
		BoostService.UseBoost(plr, item)
	elseif ItemData.Type == 'Cosmetic' then
		Inventory[item].Count += 1
		
		CharacterService.EquipCosmetic(plr, item, true)
	end
	
	Inventory[item].Count -= 1
	
	if Inventory[item].Count <= 0 then
		Inventory[item] = nil
	end
	
	Packets.Inventory.UpdateItemsInventory.sendTo(Inventory, plr)
end


function InventoryService._PlayerAdded(plr: Player)
	local DataService = Server.GetService('DataService')

	local Inventory = DataService.Get(plr, 'Inventory')

	for k in Inventory do
		if not Items[k] then
			Inventory[k] = nil
		else
			InventoryService.AddItem(plr, k, 0)
		end
	end
	
	--[[for itemName in Items do
		InventoryService.AddItem(plr, itemName, 100)
	end]]
end


function InventoryService._Start()
	local DataService = Server.GetService('DataService')
	
	Packets.Inventory.UpdateItemsInventory.listen(function(_, plr: Player)
		Packets.Inventory.UpdateItemsInventory.sendTo(DataService.Get(plr, 'Inventory'), plr)
	end)
	
	Packets.Inventory.UseItem.listen(function(data: string, plr: Player)
		InventoryService.UseItem(plr, data)
	end)
	
	Packets.Inventory.GetEquippedCosmetics.listen(function(_, plr: Player)
		Packets.Inventory.GetEquippedCosmetics.sendTo(DataService.Get(plr, 'EquippedCosmetics'), plr)
	end)
end


return InventoryService