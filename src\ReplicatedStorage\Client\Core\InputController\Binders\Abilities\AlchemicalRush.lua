local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)
local Packets = require(ReplicatedStorage.Data.Packets)

local AlchemicalRush = {
	Key = Enum.KeyCode.C,

	GamepadKey = Enum.KeyCode.ButtonL2
}


function AlchemicalRush.KeyDown()
	Packets.Ability.UseAbility.send({
		AbilityName = script.Name,
		Args = {}
	})
end


function AlchemicalRush.KeyUp()

end


function AlchemicalRush.Click()
	AlchemicalRush.KeyDown()
end


function AlchemicalRush.IsOnCooldown()
	return Client.GetController'AbilityController'.IsAbilityOnCooldown(script.Name)
end


function AlchemicalRush.CanUseAbilityAsCharacter()
	return Client.GetController'AbilityController'.CanUseAbilityAsCharacter(script.Name)
end


function AlchemicalRush._Init()

end


return AlchemicalRush
