local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)

local PassiveModule = {}


function PassiveModule.Get()
	local ChallengeService = Server.GetService('ChallengeService')
	
	local default = Server.Immutable.STOVE_BURN_TIME
	
	local act = Server.GetService('RoundService').Act
	if act and Client.Immutable.ACT_CONFIG[act] then
		default = Client.Immutable.ACT_CONFIG[act].StoveBurnTime
	end
	
	local playerCountConfig = Client.Immutable.PLAYER_COUNT_CONFIG[#Players:GetPlayers()]
	if playerCountConfig and playerCountConfig.StoveBurnTimeIncrement then
		default += playerCountConfig.StoveBurnTimeIncrement
	end
	
	if ChallengeService.Modifiers[script.Name] then
		default = ChallengeService.Modifiers[script.Name]
	end
	
	for _, v in Players:GetPlayers() do
		if v:GetAttribute('InfinityBarrier') then
			default += 6
		end
		
		if v:GetAttribute('SpatialDistortion') then
			default *= 1.2
		end
		
		if v:GetAttribute('Stop') then
			default += 4
		end
	end
	
	if script:GetAttribute('SenkuStack') then
		default += script:GetAttribute('SenkuStack')
	end
	
	if script:GetAttribute('RengokuStack') then
		default += script:GetAttribute('RengokuStack')
	end
	
	if script:GetAttribute('LeviStack') then
		default += script:GetAttribute('LeviStack')
	end
	
	if script:GetAttribute('TogeStack') then
		default += script:GetAttribute('TogeStack')
	end
	
	return default
end


function PassiveModule.Init()
	local OrderService = Server.GetService('OrderService')
	
	OrderService.OrderDeleted:Connect(function(_, _, plr: Player?)
		if plr then
			if plr:GetAttribute('Character') == 'Senku Ishigami' then
				script:SetAttribute('SenkuStack', (script:GetAttribute'SenkuStack' or 0) + 5)
				task.delay(8, function()
					script:SetAttribute('SenkuStack', script:GetAttribute'SenkuStack' - 5)
				end)
			elseif plr:GetAttribute('Character') == 'Rengoku Kyojuro' then
				script:SetAttribute('RengokuStack', (script:GetAttribute'RengokuStack' or 0) + 4)
				task.delay(8, function()
					script:SetAttribute('RengokuStack', script:GetAttribute'RengokuStack' - 4)
				end)
			elseif plr:GetAttribute('Character') == 'Levi Ackerman' then
				script:SetAttribute('LeviStack', (script:GetAttribute'LeviStack' or 0) + 3)
				task.delay(6, function()
					script:SetAttribute('LeviStack', script:GetAttribute'LeviStack' - 3)
				end)
			elseif plr:GetAttribute('Character') == 'Toge Inumaki' then
				script:SetAttribute('TogeStack', (script:GetAttribute'TogeStack' or 0) + 3)
				task.delay(6, function()
					script:SetAttribute('TogeStack', script:GetAttribute'TogeStack' - 3)
				end)
			end
		end
	end)
end


return PassiveModule