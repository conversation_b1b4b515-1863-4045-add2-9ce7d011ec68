local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local CharactersData = require(ReplicatedStorage.Data.Characters)

local SkinController = {
	CurrentCharacter = nil,
	SellMode = false,
	
	PlayerSkins = {},
	EquippedSkins = {},
	
	SelectedSkin = nil
}


local function setupSkinsFrame()
	local FilterController = Client.GetController('FilterController')
	
	local SkinsFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Skins
	local SearchBox = SkinsFrame.Content.Search.TextBox
	
	SearchBox:GetPropertyChangedSignal'Text':Connect(function()
		FilterController.ApplyFilter(SkinsFrame.Content.ScrollingFrame:GetChildren(), SearchBox.Text)
	end)
	
	SkinsFrame.Content.SellMode.Cancel.MouseButton1Click:Connect(function()
		SkinController.ToggleSellMode(false)
	end)
	
	SkinsFrame.Content.SellMode.Sell.MouseButton1Click:Connect(function()
		local sellingSkins = {}
		for _, v in SkinsFrame.Content.ScrollingFrame:GetChildren() do
			if v:IsA'ImageButton' and v.Content.SellOverlay.Visible then
				table.insert(sellingSkins, v.Name)
			end
		end
		
		Packets.Skins.SellSkins.send(sellingSkins)
		SkinController.ToggleSellMode(false)
	end)
	
	SkinsFrame.Content.Buttons.SellMode.MouseButton1Click:Connect(function()
		SkinController.ToggleSellMode(true)
	end)
end


function SkinController.OpenSkinFrame(characterID: string)
	local UIController = Client.GetController('UIController')
	local ItemController = Client.GetController('ItemController')
	local CharacterInventoryController = Client.GetController('CharacterInventoryController')
	
	local SkinsFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Skins
	
	local CharacterName = CharacterInventoryController.GetCharacterNameFromID(characterID)
	
	SkinController.SelectedSkin = nil
	SkinController.CurrentCharacter = characterID
	
	local CharacterFrame = ItemController.CreateItem({
		ItemName = CharacterName,
		Size = SkinsFrame.Content.Unit.Size,
		Position = SkinsFrame.Content.Unit.Position,
		Parent = SkinsFrame.Content
	})
	
	SkinsFrame.Content.Unit.Visible = false
	
	for _, v in SkinsFrame.Content.ScrollingFrame:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	if CharactersData[CharacterName].Skins then
		for skinName in SkinController.PlayerSkins do
			local skinInfo = CharactersData[CharacterName].Skins[skinName]
			if skinInfo then
				local SkinItem = ItemController.CreateItem({
					ItemName = skinName,
					Parent = SkinsFrame.Content.ScrollingFrame
				})
				
				SkinItem.Content.SellOverlay.Coin.Counter.Text = `+{skinInfo.SellValue}$`
				
				SkinItem.MouseButton1Click:Connect(function()
					if SkinController.SellMode then
						SkinItem.Content.SellOverlay.Visible = not SkinItem.Content.SellOverlay.Visible
						SkinController.ToggleSellMode(true)
						return
					end
					
					SkinController.SetSelectedSkin(skinName)
				end)
				
				if not SkinController.SelectedSkin then
					SkinController.SetSelectedSkin(skinName)
				end
			end
		end
	end
	
	UIController.Open(SkinsFrame)
end


function SkinController.SetSelectedSkin(skinName: string)
	SkinController.SelectedSkin = skinName
	
end


function SkinController.ToggleSellMode(enabled: boolean)
	local CharacterInventoryController = Client.GetController('CharacterInventoryController')
	
	local SkinsFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Skins
	
	local CharacterName = SkinController.CurrentCharacter and CharacterInventoryController.GetCharacterNameFromID(SkinController.CurrentCharacter)
	
	SkinController.SellMode = enabled
	
	SkinsFrame.Content.Buttons.SellMode.Visible = not enabled
	SkinsFrame.Content.Buttons.Equip.Visible = (SkinController.SelectedSkin ~= nil) and (not enabled)
	SkinsFrame.Content.SellMode.Visible = enabled
	
	local sellSum = 0
	for _, v in SkinsFrame.Content.ScrollingFrame:GetChildren() do
		if v:IsA'ImageButton' then
			if enabled == false then
				v.Content.SellOverlay.Visible = enabled
			else
				if v.Content.SellOverlay.Visible == true then
					sellSum += CharactersData[CharacterName].Skins[v.Name].SellValue
				end
			end
		end
	end
	
	SkinsFrame.Content.SellMode.Sell.Visible = sellSum > 0
	SkinsFrame.Content.SellMode.Sell.SellVal.Visible = sellSum > 0
	SkinsFrame.Content.SellMode.Sell.SellVal.Text = `+{sellSum}$`
end


function SkinController._Start()
	local UIController = Client.GetController('UIController')
	
	Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'
	
	local SkinsFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Skins
	
	for _, v in SkinsFrame.Content.ScrollingFrame:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	Packets.Skins.GetSkins.listen(function(data: { [string]: {  } })
		SkinController.PlayerSkins = data
		
		if SkinController.CurrentCharacter then
			SkinController.OpenSkinFrame(SkinController.CurrentCharacter)
		end
	end)
	Packets.Skins.GetSkins.send()
	
	Packets.Skins.GetEquipedSkins.listen(function(data: { [string]: string })
		SkinController.EquippedSkins = data
	end)
	
	UIController.PageToggled:Connect(function(frame: GuiObject, isOpen: boolean, wasOpened: Frame?)
		if frame == SkinsFrame and isOpen == false then
			SkinController.ToggleSellMode(false)
		end
	end)
	
	task.defer(setupSkinsFrame)
	task.defer(SkinController.ToggleSellMode, false)
end


return SkinController