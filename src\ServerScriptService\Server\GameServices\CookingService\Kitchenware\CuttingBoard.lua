local ServerScriptService = game:GetService('ServerScriptService')
local ReplicatedStorage = game:GetService('ReplicatedStorage')
local TweenService = game:GetService('TweenService')
local SoundService = game:GetService('SoundService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)

local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local CookingService = require(script.Parent.Parent)

local CuttingBoard = {
	CuttingJanitors = {},
	ClaimedCuttingBoards = {}
}


function CuttingBoard.Interact(plr: Player, interactable: BasePart, ingredient: BasePart?)
	if not CookingService.IsIngredient(ingredient) or CuttingBoard.ClaimedCuttingBoards[interactable] then return end

	local AbilityService = Server.GetService('AbilityService')

	local HeldItem = CookingService.GetHeldItem(plr)
	local IngredientOnCuttingBoard = CookingService.GetIngredientOnInteractable(interactable)

	if HeldItem and IngredientOnCuttingBoard then return end

	if CuttingBoard.IsActionComplete(ingredient) then
		CookingService.PickupItem(plr, ingredient)
		return
	end

	if not IngredientOnCuttingBoard then
		local placed = CookingService.PlaceItem(ingredient, interactable, plr)
		if not placed then return end
	end

	CuttingBoard.CuttingJanitors[plr] = Janitor.new()
	CuttingBoard.ClaimedCuttingBoards[interactable] = plr

	interactable.CuttingBoard.Cutting.Transparency = 1

	do -- anims and vfx
		local att = CuttingBoard.CuttingJanitors[plr]:Add(ReplicatedStorage.Assets.Visuals.Chopping.Attachment:Clone())
		att.Parent = interactable.CuttingBoard.Cutting_Board

		Animations:PlayTrack(plr, 'Cut')
		CuttingBoard.CuttingJanitors[plr]:Add(task.spawn(function()
			while task.wait(.2) do
				for _, v in att:GetChildren() do
					v:Emit(v:GetAttribute'EmitCount')
				end
			end
		end))
	end

	do
		local targetLimbName: string = ReplicatedStorage.Assets.AnimationObjects.Knife.Limb.Value

		local Knife = CuttingBoard.CuttingJanitors[plr]:Add(ReplicatedStorage.Assets.Props.Knife:Clone())

		Knife.Massless = true
		Knife.Anchored = false

		Knife.Parent = plr.Character

		local motor = CuttingBoard.CuttingJanitors[plr]:Add(ReplicatedStorage.Assets.AnimationObjects.Knife.Knife:Clone())
		motor.Parent = plr.Character[targetLimbName]

		motor.Part0 = plr.Character[targetLimbName]
		motor.Part1 = Knife
	end

	do
		local offset = CFrame.new(0, 0, -(interactable.Size.Z/2 + plr.Character.PrimaryPart.Size.Z/2)) * CFrame.Angles(0, math.pi, 0)
		local appliedOffset = interactable.CFrame * offset

		plr.Character.PrimaryPart.CFrame = CFrame.new(appliedOffset.X, plr.Character.PrimaryPart.CFrame.Y, appliedOffset.Z)
			* appliedOffset.Rotation

		plr.Character.PrimaryPart.Anchored = true
		plr.Character.Humanoid.AutoRotate = false

		plr.Character.Humanoid:MoveTo(plr.Character.PrimaryPart.Position)
	end

	do
		local ProgressBar = CuttingBoard.CuttingJanitors[plr]:Add(ReplicatedStorage.Assets.Templates.ProgressBar:Clone())
		ProgressBar.Parent = interactable.CuttingBoard

		local progressNV = CuttingBoard.CuttingJanitors[plr]:Add(Instance.new('NumberValue'))
		progressNV.Value = 0

		local t = TweenService:Create(progressNV,
			TweenInfo.new(AbilityService.GetPassiveValue(plr, 'CuttingSpeed'), Enum.EasingStyle.Linear),
			{Value = 1}
		);t:Play();

		CuttingBoard.CuttingJanitors[plr]:Add(progressNV:GetPropertyChangedSignal'Value':Connect(function()
			ProgressBar.Bar.Progress.UIGradient.Transparency = NumberSequence.new{
				NumberSequenceKeypoint.new(0, 0),
				NumberSequenceKeypoint.new(math.clamp(progressNV.Value, .001, .998), 0),
				NumberSequenceKeypoint.new(math.clamp(progressNV.Value + .001, .002, .999), 1),
				NumberSequenceKeypoint.new(1, 1)
			}
		end))

		CuttingBoard.CuttingJanitors[plr]:Add(t.Completed:Once(function(playbackState: Enum.PlaybackState)
			if playbackState == Enum.PlaybackState.Completed then
				ingredient = CuttingBoard.SetIngredientAsCut(ingredient)
				CuttingBoard.StopInteract(plr)
			end
		end))
	end

	CuttingBoard.CuttingJanitors[plr]:Add(function()
		interactable.CuttingBoard.Cutting.Transparency = 0
		CuttingBoard.ClaimedCuttingBoards[interactable] = nil
		
		if plr:IsDescendantOf(Players) and plr.Character then
			plr.Character.Humanoid.AutoRotate = true
			plr.Character.PrimaryPart.Anchored = false
			Animations:StopTrack(plr, 'Cut')
		end
	end)
	
	SoundService.SFX.Chopping:Play()
end


function CuttingBoard.SetIngredientAsCut(ingredient: BasePart): BasePart
	if CuttingBoard.IsActionComplete(ingredient) then return ingredient end
	
	local newIngredient = ReplicatedStorage.Assets.Ingredients:FindFirstChild('Chopped' .. ingredient.Name)

	if newIngredient then
		newIngredient = newIngredient:Clone()
		
		CookingService.ReplaceIngredientModel(ingredient, newIngredient)
		newIngredient:SetAttribute('Cut', true)
		
		CookingService.FinishedChopping:Fire()

		return newIngredient
	end
	
	return ingredient
end


function CuttingBoard.IsActionComplete(ingredient: BasePart)
	return ingredient:GetAttribute('Cut')
end


function CuttingBoard.CanInteract(plr: Player, interactable: BasePart): boolean
	return false
end


function CuttingBoard.IsPlayerCutting(plr: Player)
	return CuttingBoard.CuttingJanitors[plr] ~= nil
end


function CuttingBoard.StopInteract(plr: Player)
	if not CuttingBoard.CuttingJanitors[plr] then return end
	CuttingBoard.CuttingJanitors[plr]:Cleanup()
	CuttingBoard.CuttingJanitors[plr] = nil
	
	local isSomeoneChopping = false
	for _, v in Players:GetPlayers() do
		if CuttingBoard.IsPlayerCutting(v) then
			isSomeoneChopping = true
			break
		end
	end

	if not isSomeoneChopping then
		SoundService.SFX.Chopping:Stop()
	end
end


return CuttingBoard