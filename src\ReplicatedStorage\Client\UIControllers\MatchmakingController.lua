local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local TweenService = game:GetService('TweenService')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local Janitor = require(ReplicatedStorage.SharedModules.Janitor)

local MatchmakingController = {
	SelectedMap = nil :: string?,
	SelectedAct = 1 :: number,
	FriendsOnly = false,
	Difficulty = 'Easy',
	
	LobbyData = nil,
	
	Winrates = nil
}


local function getMapCompletionPercentage(map: string, plr: Player): (number, number)
	local actsCompleted = 0
	local total = Client.Immutable.ACTS

	if not plr then
		for k, v in MatchmakingController.Winrates[map] do
			actsCompleted += v.Wins >= 1 and 1 or 0
		end
	else
		local ActItem = plr:FindFirstChild(`Act{map}`)
		if ActItem then
			actsCompleted = ActItem.Value - 1
		end
	end
	
	return math.ceil(actsCompleted / total * 100), actsCompleted
end


function isMapLocked(map: string, plr: Player?): boolean
	local MapIndex = Client.Immutable.Servers.PLACE_INFO[map].Order
	
	if MapIndex == 1 then return false end
	
	local previousMap
	for mapName, mapInfo in Client.Immutable.Servers.PLACE_INFO do
		if mapInfo.Order == MapIndex - 1 then
			previousMap = mapName
			break
		end
	end
	
	if previousMap then
		local _, actsCompleted = getMapCompletionPercentage(previousMap, plr)
		return actsCompleted < Client.Immutable.ACTS
	end
	
	return true
end


local function updateStorySelection()
	local ItemController = Client.GetController('ItemController')
	
	if not MatchmakingController.Winrates or not MatchmakingController.SelectedMap then return end
	
	local StoryMatchmakingUI = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Play
	
	StoryMatchmakingUI.Content.Tab.LevelName.Text = MatchmakingController.SelectedMap:upper()
	StoryMatchmakingUI.Content.Tab.ActNumber.Text = 'ACT ' .. (MatchmakingController.SelectedAct == 255 and '∞' or MatchmakingController.SelectedAct)
	
	StoryMatchmakingUI.Content.Tab.Stats.Stat1.Number.Text = MatchmakingController.Winrates[MatchmakingController.SelectedMap][MatchmakingController.SelectedAct].Wins
	StoryMatchmakingUI.Content.Tab.Stats.Stat2.Number.Text = MatchmakingController.Winrates[MatchmakingController.SelectedMap][MatchmakingController.SelectedAct].MostDelivered
	
	local _, actsCompleted = getMapCompletionPercentage(MatchmakingController.SelectedMap)
	for _, v in StoryMatchmakingUI.Content.Acts.Slots.Scroller:GetChildren() do
		if v:IsA'ImageButton' then
			local ActNum = tonumber(v.Name)
			if ActNum ~= 255 and ActNum > actsCompleted + 1 then
				v.Locked.Visible = true
				
				if MatchmakingController.SelectedAct == ActNum then
					MatchmakingController.SelectedAct = 1
					updateStorySelection()
				end
			else
				v.Locked.Visible = false
			end
			
			v.Stroke.IsSelected.Enabled = v.Name == tostring(MatchmakingController.SelectedAct)
			v.Stroke.NotSelected.Enabled = v.Name ~= tostring(MatchmakingController.SelectedAct)
		end
	end
	
	for _, v in StoryMatchmakingUI.Content.Maps.Slots.Scroller:GetChildren() do
		if v:IsA'ImageButton' then
			local MapName = v.Name
			
			v.Locked.Visible = isMapLocked(MapName)
			
			v.Stroke.IsSelected.Enabled = MapName == MatchmakingController.SelectedMap
			v.Stroke.NotSelected.Enabled = MapName ~= MatchmakingController.SelectedMap
		end
	end
	
	do -- Rewards
		for _, v in StoryMatchmakingUI.Content.Tab.Rewards.Contents:GetChildren() do
			if v:IsA'ImageButton' then v:Destroy() end
		end
		
		local RewardsInfo = Client.Immutable.Servers.PLACE_INFO[MatchmakingController.SelectedMap].ActsInfo[MatchmakingController.SelectedAct][MatchmakingController.Difficulty]
		if RewardsInfo and RewardsInfo.Rewards then
			RewardsInfo = RewardsInfo.Rewards
		else
			warn('aaaaa', MatchmakingController.SelectedAct)
		end
		
		for _, v in RewardsInfo do
			local ItemFrame = ItemController.CreateItem({
				ItemName = v.Name,
				Count = v.Count,
				Size = UDim2.fromScale(.141, .778),
				Parent = StoryMatchmakingUI.Content.Tab.Rewards.Contents
			})
		end
	end
end


local function setupStoryMatchmakingUI()
	local StoryMatchmakingUI = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Play
	
	for k, v in Client.Immutable.Servers.PLACE_INFO do
		if v.ServerType == 'Round' then
			local MapButton = ReplicatedStorage.Assets.Templates.StoryMapTemplate:Clone()
			MapButton.Locked.Visible = false
			MapButton.Details.Txt.Text = k
			MapButton.Name = k
			MapButton.Background.Image = v.MapImage
			MapButton.LayoutOrder = v.Order
			MapButton.Parent = StoryMatchmakingUI.Content.Maps.Slots.Scroller
			
			MapButton.MouseButton1Click:Connect(function()
				if MapButton.Locked then return end
				
				MatchmakingController.SelectedMap = k
				updateStorySelection()
			end)

			if v.Order == 1 then
				MatchmakingController.SelectedMap = k
			end
		end
	end
	
	for i = 1, #Client.Immutable.ACT_CONFIG + 1 do
		if i == #Client.Immutable.ACT_CONFIG + 1 then
			i = 255
		end
		
		local ActButton = ReplicatedStorage.Assets.Templates.ActTemplate:Clone()
		ActButton.Name = i
		ActButton.Details.Number.Text = i == 255 and '∞' or i
		ActButton.LayoutOrder = i
		ActButton.Parent = StoryMatchmakingUI.Content.Acts.Slots.Scroller
		
		ActButton.MouseButton1Click:Connect(function()
			MatchmakingController.SelectedAct = i
			updateStorySelection()
		end)
		
		if not MatchmakingController.SelectedAct then
			MatchmakingController.SelectedAct = i
			task.defer(updateStorySelection)
		end
	end
	
	StoryMatchmakingUI.BottomBar.Buttons.Play.MouseButton1Click:Connect(function()
		Packets.Matchmaking.CreateLobby.send({
			Map = MatchmakingController.SelectedMap,
			Act = MatchmakingController.SelectedAct,
			FriendsOnly = MatchmakingController.FriendsOnly,
			Difficulty = MatchmakingController.Difficulty
		})
	end)
end


local function updateLobby(data: { Act: number, Host: string, Map: string, Players: {Player}, Difficulty: string, FriendsOnly: boolean, UserAdded: string?, UserRemoved: string? })
	local UIController = Client.GetController('UIController')
	local HUDController = Client.GetController('HUDController')
	local ItemController = Client.GetController('ItemController')
	
	if data.UserRemoved == Players.LocalPlayer.Name then
		UIController.Close()
		MatchmakingController.LobbyData = nil
		
		HUDController.ShowHUDPortion('Left')
		HUDController.ShowHUDPortion('Right')
	end
	
	if not table.find(data.Players, Players.LocalPlayer) then return end
	
	local PartyPopup = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.PartyPopup
	
	if data.UserAdded == Players.LocalPlayer.Name then
		UIController.Open(PartyPopup)
		
		HUDController.HideHUDPortion('Left')
		HUDController.HideHUDPortion('Right')
	end
	
	PartyPopup.Buttons.Start.Visible = data.Host == Players.LocalPlayer.Name
	PartyPopup.Buttons.Invite.Visible = true
	PartyPopup.Buttons.Leave.Visible = true
	
	PartyPopup.Info.Banner.Timer.Visible = false
	
	PartyPopup.Info.Banner.Background.Image = Client.Immutable.Servers.PLACE_INFO[data.Map].MapImage
	PartyPopup.Info.Banner.LevelName.Text = data.Map:upper()
	PartyPopup.Info.Banner.ActNumber.Text = 'Act ' .. (data.Act == 255 and '∞' or data.Act)
	PartyPopup.Info.Banner.Diffiulcty.Text = 'DIFFICULTY: ' .. data.Difficulty:upper()
	
	for _, v in PartyPopup.Players.Container.Contents:GetChildren() do
		if v:IsA'GuiObject' then v:Destroy() end
	end
	
	do
		for _, v in PartyPopup.Info.Rewards.Contents:GetChildren() do
			if v:IsA'ImageButton' then v:Destroy() end
		end
		
		local RewardsInfo = Client.Immutable.Servers.PLACE_INFO[data.Map].ActsInfo[data.Act][data.Difficulty]
		if RewardsInfo and RewardsInfo.Rewards then
			RewardsInfo = RewardsInfo.Rewards
		else
			warn('aaaaa', MatchmakingController.SelectedAct)
		end
		
		for _, v in RewardsInfo do
			local ItemFrame = ItemController.CreateItem({
				ItemName = v.Name,
				Count = v.Count,
				Size = UDim2.fromScale(.161, .778),
				Parent = PartyPopup.Info.Rewards.Contents
			})
		end
	end
	
	for _, v in data.Players do
		local PlayerContainer = ReplicatedStorage.Assets.Templates.PlayerContainer:Clone()
		PlayerContainer.Image = `rbxthumb://type=AvatarHeadShot&id={v.UserId}&w=180&h=180`
		PlayerContainer.ItemName.Text = v.DisplayName
		PlayerContainer.Parent = PartyPopup.Players.Container.Contents
	end
	
	MatchmakingController.LobbyData = data
	
	MatchmakingController.UpdateInviteUI()
end


local function bindToggleFriends()
	local StoryMatchmakingUI = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Play
	
	local function toggleFriends(on: boolean?)
		if on == nil then
			on = not MatchmakingController.FriendsOnly
		end
		
		MatchmakingController.FriendsOnly = on
		
		StoryMatchmakingUI.BottomBar.Toggle.On.Visible = on
		StoryMatchmakingUI.BottomBar.Toggle.Off.Visible = not on
	end
	
	StoryMatchmakingUI.BottomBar.Toggle.MouseButton1Click:Connect(function()
		toggleFriends()
	end)
	
	task.defer(toggleFriends, false)
end


local function bindDifficultyToggle()
	local StoryMatchmakingUI = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Play
	
	local SelectedImage = 'rbxassetid://97414875499122'
	local UnselectedImage = 'rbxassetid://110248151954800'
	local function switchDifficulty(difficulty: string)
		MatchmakingController.Difficulty = difficulty
		
		StoryMatchmakingUI.Content.Tab.Difficulty.Difficulty.Easy.Image = difficulty == 'Easy' and SelectedImage or UnselectedImage
		StoryMatchmakingUI.Content.Tab.Difficulty.Difficulty.Hard.Image = difficulty == 'Hard' and SelectedImage or UnselectedImage
		
		updateStorySelection()
	end
	
	StoryMatchmakingUI.Content.Tab.Difficulty.Difficulty.Easy.MouseButton1Click:Connect(function()
		switchDifficulty('Easy')
	end)
	
	StoryMatchmakingUI.Content.Tab.Difficulty.Difficulty.Hard.MouseButton1Click:Connect(function()
		switchDifficulty('Hard')
	end)
	
	task.defer(switchDifficulty, 'Easy')
end


local function setupInviteUI()
	local UIController = Client.GetController('UIController')
	
	local InviteUI = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Invite
	
	function MatchmakingController.UpdateInviteUI()
		if not MatchmakingController.LobbyData then return end
		
		for _, v in InviteUI.Players.Slots.Scroller:GetChildren() do
			if v:IsA'ImageLabel' then v:Destroy() end
		end
		
		for _, v: Player in Players:GetPlayers() do
			if v:GetAttribute'InALobby' then continue end
			
			if MatchmakingController.LobbyData.FriendsOnly then
				local HostPlayer = Players:FindFirstChild(MatchmakingController.LobbyData.Host)
				if not HostPlayer or not v:IsFriendsWith(HostPlayer.UserId) then continue end
			end
			
			local ActItem = v:FindFirstChild(`Act{MatchmakingController.LobbyData.Map}`)
			if not ActItem then continue end
			
			if MatchmakingController.LobbyData.Act ~= 255
				and ActItem.Value < MatchmakingController.LobbyData.Act then
				continue
			elseif isMapLocked(MatchmakingController.LobbyData.Map, v) then
				continue
			end
			
			local UserTemplate = ReplicatedStorage.Assets.Templates.InviteUserTemplate:Clone()
			UserTemplate.Name = v.Name
			UserTemplate.PlayerName.Text = `@{v.DisplayName}`
			UserTemplate.Parent = InviteUI.Players.Slots.Scroller
			
			UserTemplate.Invite.MouseButton1Click:Connect(function()
				Packets.Matchmaking.InvitePlayer.send({PlayerName = v.Name})
				UserTemplate.Invite.Txt.Text = 'INVITED!'
			end)
		end
		
		InviteUI.Banner.Background.Image = Client.Immutable.Servers.PLACE_INFO[MatchmakingController.LobbyData.Map].MapImage
		InviteUI.Banner.LevelName.Text = MatchmakingController.LobbyData.Map:upper()
		InviteUI.Banner.ActNumber.Text = 'ACT ' .. (MatchmakingController.LobbyData.Act == 255 and '∞' or MatchmakingController.LobbyData.Act)
		InviteUI.Banner.Diffiulcty.Text = 'DIFFICULTY: ' .. MatchmakingController.LobbyData.Difficulty:upper()
	end
	
	MatchmakingController._PlayerAdded = MatchmakingController.UpdateInviteUI
	MatchmakingController._PlayerRemoving = MatchmakingController.UpdateInviteUI
	
	UIController.PageToggled:Connect(function(frame: GuiObject, isOpen: boolean)
		if frame == InviteUI and isOpen then
			MatchmakingController.UpdateInviteUI()
		end
	end)
	
	local ReciveInvitePopup = Players.LocalPlayer.PlayerGui.LobbyUI.Popups.ReciveInvite
	Packets.Matchmaking.InvitePlayer.listen(function(data: { LobbyData: { Act: number, Difficulty: string, MapName: string, Host: string }, PlayerName: string })
		if ReciveInvitePopup.Visible then return end
		
		ReciveInvitePopup.Visible = true
		
		ReciveInvitePopup.Banner.Background.Image = Client.Immutable.Servers.PLACE_INFO[data.LobbyData.MapName].MapImage
		ReciveInvitePopup.Banner.LevelName.Text = data.LobbyData.MapName:upper()
		ReciveInvitePopup.Banner.Diffiulcty.Text = 'DIFFICULTY: ' .. data.LobbyData.Difficulty:upper()
		ReciveInvitePopup.Banner.ActNumber.Text = 'ACT ' .. (data.LobbyData.Act == 255 and '∞' or data.LobbyData.Act)
		
		ReciveInvitePopup.Username.Text = `INVITED BY: <font color="#ffc9cf">@{data.PlayerName}</font>`
		
		local InviteJanitor = Janitor.new()
		
		InviteJanitor:Add(ReciveInvitePopup.Buttons.Accept.MouseButton1Click:Connect(function()
			Packets.Matchmaking.JoinLobby.send(data.LobbyData.Host)
			InviteJanitor:Destroy()
		end))
		
		InviteJanitor:Add(ReciveInvitePopup.Buttons.Decline.MouseButton1Click:Connect(function()
			InviteJanitor:Destroy()
		end))
		
		local start = os.clock()
		InviteJanitor:Add(task.spawn(function()
			while true do
				local tElapsed = os.clock() - start
				ReciveInvitePopup.Bar.Timer.Text = math.ceil(20 - (os.clock() - start)) .. 's'
				task.wait(1)
			end
			
			InviteJanitor:Destroy()
		end))
		
		ReciveInvitePopup.Bar.Holder.Loader.Size = UDim2.fromScale(1, 1)
		local t = InviteJanitor:Add(TweenService:Create(ReciveInvitePopup.Bar.Holder.Loader, TweenInfo.new(20), {
			Size = UDim2.fromScale(0, 1)
		}), 'Cancel')
		t:Play()
		
		InviteJanitor:Add(function()
			ReciveInvitePopup.Visible = false
		end)
	end)
end


function MatchmakingController._Start()
	local UIController = Client.GetController('UIController')
	
	Packets.Matchmaking.LobbyUpdated.listen(updateLobby)
	
	local LobbyUI = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'
	local StoryMatchmakingUI = LobbyUI.Pages.Play
	local PartyPopup = LobbyUI.Pages.PartyPopup
	
	for _, v in StoryMatchmakingUI.Content.Acts.Slots.Scroller:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	for _, v in StoryMatchmakingUI.Content.Maps.Slots.Scroller:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	Packets.Matchmaking.GetWinrates.listen(function(data: { [string]: {{ Losses: number, Wins: number, MostDelivered: number }} })
		MatchmakingController.Winrates = data
		updateStorySelection()
		
		for map in data do
			local MapFrame = StoryMatchmakingUI.Content.Maps.Slots.Scroller:WaitForChild(map)
			MapFrame.Details.Description.Text = `Completion: {getMapCompletionPercentage(map)}%`
		end
	end)
	Packets.Matchmaking.GetWinrates.send()
	
	PartyPopup.Buttons.Start.MouseButton1Click:Connect(function()
		Packets.Matchmaking.Start.send()
	end)
	
	PartyPopup.Buttons.Leave.MouseButton1Click:Connect(function()
		Packets.Matchmaking.LeaveLobby.send()
	end)
	
	PartyPopup.Buttons.Invite.MouseButton1Click:Connect(function()
		UIController.Open(LobbyUI.Pages.Invite)
	end)
	
	Packets.Matchmaking.PromptLobbyUI.listen(function()
		UIController.Open(StoryMatchmakingUI)
	end)
	
	UIController.BindCloseButton(StoryMatchmakingUI, StoryMatchmakingUI.BottomBar.Buttons.Close)
	
	UIController.PageToggled:Connect(function(frame: GuiObject, isOpen: boolean)
		if frame == LobbyUI.Pages.Invite and isOpen == false then
			UIController.Open(PartyPopup)
		end
	end)
	
	task.defer(bindToggleFriends)
	task.defer(bindDifficultyToggle)
	task.defer(setupInviteUI)
	task.defer(setupStoryMatchmakingUI)
end


return MatchmakingController