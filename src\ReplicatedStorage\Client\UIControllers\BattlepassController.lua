local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local MarketplaceService = game:GetService('MarketplaceService')

local Client = require(ReplicatedStorage.Client)

local BattlepassData = require(ReplicatedStorage.Data.Battlepass)
local Packets = require(ReplicatedStorage.Data.Packets)
local SimpleMarketplace = require(ReplicatedStorage.Data.SimpleMarketplace)

local BattlepassController = {}


local function setupBattlepassFrame()
	local ItemController = Client.GetController('ItemController')

	local BattlepassFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Battlepass

	for _, v in BattlepassFrame.Content.Holder.Slots.Scroller:GetChildren() do
		if v:IsA'GuiObject' then v:Destroy() end
	end

	for tierNum, v in BattlepassData.Tiers do
		local TierSlot = ReplicatedStorage.Assets.Templates.BattlepassSlot:Clone()
		TierSlot.Name = tierNum
		TierSlot.Header.Txt.Text = `LEVEL {tierNum}`

		local function setupItemSlot(toCopy: GuiObject, free: boolean)
			local ItemSlot = ItemController.CreateItem({
				ItemName = v[free and 'Free' or 'Paid'].Name,
				Image = v[toCopy.Name].Image,
				Size = toCopy.Size,
				AnchorPoint = Vector2.new(.5, .5),
				Parent = TierSlot
			})

			ItemSlot.Name = toCopy.Name
			ItemSlot.LayoutOrder = toCopy.LayoutOrder
			toCopy:Destroy()

			ItemSlot.MouseButton1Click:Connect(function()
				if not free and not SimpleMarketplace.UserOwnsGamePassAsync(Players.LocalPlayer.UserId, SimpleMarketplace.Gamepasses['Premium Pass'].ID) then
					MarketplaceService:PromptGamePassPurchase(Players.LocalPlayer, SimpleMarketplace.Gamepasses['Premium Pass'].ID)
					return
				end

				Packets.Battlepass.ClaimTier.send({
					Free = free,
					Tier = tierNum
				})
			end)

			script.Layer:Clone().Parent = ItemSlot
		end

		setupItemSlot(TierSlot.Free, true)
		setupItemSlot(TierSlot.Paid, false)

		TierSlot.Parent = BattlepassFrame.Content.Holder.Slots.Scroller
	end

	BattlepassFrame.Content.Tail.Skip.MouseButton1Click:Connect(function()
		MarketplaceService:PromptProductPurchase(Players.LocalPlayer, SimpleMarketplace.DevProducts['Skip1BattlepassLevel'].ID)
	end)

	BattlepassFrame.SideButtons.Skip.MouseButton1Click:Connect(function()
		MarketplaceService:PromptProductPurchase(Players.LocalPlayer, SimpleMarketplace.DevProducts['Skip10BattlepassLevels'].ID)
	end)

	BattlepassFrame.Content.Tail.Claim.MouseButton1Click:Connect(function()
		Packets.Battlepass.ClaimAll.send()
	end)

	BattlepassFrame.SideButtons.Premuim.MouseButton1Click:Connect(function()
		MarketplaceService:PromptGamePassPurchase(Players.LocalPlayer, SimpleMarketplace.Gamepasses['Premium Pass'].ID)
	end)

	Packets.Battlepass.GetPlayerBattlepassInfo.send()
end


local function updateState(data: { ClaimedPaid: {boolean}, ClaimedFree: {boolean}, Level: number, XP: number })
	local BattlepassFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Battlepass

	local NextTierInfo = BattlepassData.Tiers[data.Level + 1]
	if NextTierInfo then
		BattlepassFrame.Content.Progress.Lvl.Text = `LVL: {data.Level}/{#BattlepassData.Tiers}`
		BattlepassFrame.Content.Progress.Xp.Text = `{data.XP} / {NextTierInfo.XPReq} XP`

		local progress = data.XP / NextTierInfo.XPReq
		BattlepassFrame.Content.Progress.Bar.Holder.Loader.UIGradient.Transparency = NumberSequence.new{
			NumberSequenceKeypoint.new(0, 0),
			NumberSequenceKeypoint.new(math.clamp(progress, .001, .998), 0),
			NumberSequenceKeypoint.new(math.clamp(progress + .001, .002, .999), 1),
			NumberSequenceKeypoint.new(1, 1)
		}
	end

	local OwnsPremiumPass = SimpleMarketplace.UserOwnsGamePassAsync(Players.LocalPlayer.UserId, SimpleMarketplace.Gamepasses['Premium Pass'].ID)
	for _, v in BattlepassFrame.Content.Holder.Slots.Scroller:GetChildren() do
		if not v:IsA'GuiObject' then continue end

		local tierNum = tonumber(v.Name)

		local isFreeClaimed = data.ClaimedFree[tierNum]
		local isPaidClaimed = data.ClaimedPaid[tierNum]

		if isFreeClaimed then
			v.Free.Layer.Visible = true
			v.Free.Layer.Locked.Visible = false
			v.Free.Layer.Claimed.Visible = true
		else
			v.Free.Layer.Visible = false
		end

		if isPaidClaimed then
			v.Paid.Layer.Visible = true
			v.Paid.Layer.Locked.Visible = false
			v.Paid.Layer.Claimed.Visible = true
		else
			v.Paid.Layer.Visible = false
		end

		if data.Level < tierNum then
			if not isFreeClaimed then
				v.Free.Layer.Visible = true
				v.Free.Layer.Locked.Visible = true
				v.Free.Layer.Claimed.Visible = false
			end

			if not isPaidClaimed then
				v.Paid.Layer.Visible = true
				v.Paid.Layer.Locked.Visible = true
				v.Paid.Layer.Claimed.Visible = false
			end
		end
	end
end


function BattlepassController._Start()
	local BattlepassFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.Battlepass

	Packets.Battlepass.GetPlayerBattlepassInfo.listen(updateState)

	if SimpleMarketplace.UserOwnsGamePassAsync(Players.LocalPlayer.UserId, SimpleMarketplace.Gamepasses['Premium Pass'].ID) then
		BattlepassFrame.SideButtons.Premuim.Visible = false
	end

	Packets.Store.OwnedGamepasses.listen(function()
		if SimpleMarketplace.UserOwnsGamePassAsync(Players.LocalPlayer.UserId, SimpleMarketplace.Gamepasses['Premium Pass'].ID) then
			BattlepassFrame.SideButtons.Premuim.Visible = false
		end
	end)

	task.defer(function()
		setupBattlepassFrame()
		Packets.Battlepass.GetPlayerBattlepassInfo.send()
	end)
end


return BattlepassController