local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local RunService = game:GetService('RunService')

local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsClient)

local SummonFolder = workspace.Summon

local OldRig = SummonFolder.CrateAnim.MythiclChar
local Rig = SummonFolder.CrateAnim.MythiclChar:Clone()
Rig.Name = 'NormalChar'
Rig:PivotTo(SummonFolder.CrateAnim.MythiclChar:GetPivot())
Rig.Parent = SummonFolder
OldRig:Destroy()

local Butler = SummonFolder.CrateAnim.Butler

local OpenAnim = Instance.new('Animation')
OpenAnim.AnimationId = 'rbxassetid://140342567811113'

local initTopCf = SummonFolder.CrateAnim.MythicalTable.Top.CFrame
local initBottomCf = SummonFolder.CrateAnim.MythicalTable.Bottom.CFrame

local PLACE_CF = CFrame.new(-0.7, -0.8, 0.4, 0.451651514, -0.848972559, 0.274329215, 0.18424125, 0.389598876, 0.90236789, -0.872963965, -0.357013047, 0.332378656)

local track = Butler.Humanoid.Animator:LoadAnimation(OpenAnim)

return function(firstCharRarity: string)
	local charDesc = ClassExtension.Humanoid.GetHumanoidDescriptionFromUserId(Players.LocalPlayer.UserId)
	
	Rig.Humanoid:ApplyDescription(charDesc)
	
	for _, v in Rig:GetChildren() do
		if v:IsA'Accessory' then
			v:Destroy()
		end
	end
	
	Rig.Torso.Transparency = 1
	Rig.Head.Transparency = 1
	
	for _, v in SummonFolder.CrateAnim.MythicalTable.Bottom:GetChildren() do
		if v:IsA'Attachment' then v:Destroy() end
	end
	
	local camera = workspace.CurrentCamera
	local oldCamCf = camera.CFrame
	
	camera.CameraType = Enum.CameraType.Scriptable
	
	local rsc = RunService.PreRender:Connect(function(dtr: number)
		camera.CFrame = Rig.Head.CFrame
	end)
	
	local plateWeld = Instance.new('WeldConstraint')
	plateWeld.Part0 = SummonFolder.CrateAnim.MythicalTable.Bottom
	plateWeld.Part1 = SummonFolder.CrateAnim.MythicalTable.Top
	plateWeld.Parent = SummonFolder.CrateAnim.MythicalTable.Bottom
	
	SummonFolder.CrateAnim.MythicalTable.Bottom.Anchored = false
	SummonFolder.CrateAnim.MythicalTable.Top.Anchored = false
	
	local armWeld = Instance.new('Weld')
	armWeld.Part0 = Butler['Right Arm']
	armWeld.Part1 = SummonFolder.CrateAnim.MythicalTable.Top
	armWeld.C0 = PLACE_CF
	armWeld.Parent = Butler['Right Arm']
	
	track:GetMarkerReachedSignal'Place':Once(function()
		armWeld:Destroy()
		plateWeld:Destroy()
		
		SummonFolder.CrateAnim.MythicalTable.Bottom.Anchored = true
		SummonFolder.CrateAnim.MythicalTable.Top.Anchored = true
	end)
	
	local weld = Instance.new('WeldConstraint')
	track:GetMarkerReachedSignal'Grab':Once(function()
		local targetObject = SummonFolder.CrateAnim.MythicalTable.Top
		
		targetObject.Anchored = false
		weld.Part0 = Butler['Right Arm']
		weld.Part1 = targetObject
		weld.Parent = targetObject
		weld.Name = 'GrabWeld'
		
		for _, v in ReplicatedStorage.Assets.Visuals.SummonVFX[firstCharRarity]:GetChildren() do
			v:Clone().Parent = SummonFolder.CrateAnim.MythicalTable.Bottom
		end
	end)
	
	track.TimePosition = 0
	track:Play()
	track:AdjustSpeed(1.3)
	
	track.Stopped:Wait()
	track:Play(0)
	track.TimePosition = track.Length * .99
	track:AdjustSpeed(0)
	
	return function()
		weld:Destroy()
		rsc:Disconnect()
		
		SummonFolder.CrateAnim.MythicalTable.Top.CFrame = initTopCf
		SummonFolder.CrateAnim.MythicalTable.Bottom.CFrame = initBottomCf

		SummonFolder.CrateAnim.MythicalTable.Bottom.Anchored = true
		SummonFolder.CrateAnim.MythicalTable.Top.Anchored = true
		
		camera.CameraType = Enum.CameraType.Custom
		camera.CameraSubject = Players.LocalPlayer.Character.Humanoid
		camera.CFrame = oldCamCf
		
		track:AdjustSpeed(1)
	end
end