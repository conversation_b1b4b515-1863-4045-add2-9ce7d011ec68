local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')


local ShiftLock = {
	Key = Enum.KeyCode.LeftControl,

	GamepadKey = Enum.KeyCode.ButtonL2
}


function ShiftLock.KeyDown()

end


function ShiftLock.KeyUp()

end


function ShiftLock._Init()
	local MouseLockController = Players.LocalPlayer:WaitForChild'PlayerScripts':WaitForChild'PlayerModule':WaitForChild'CameraModule':WaitForChild'MouseLockController'
	
	local obj = MouseLockController:FindFirstChild('BoundKeys')
	if obj then
		obj.Value = `{ShiftLock.Key.Name},{ShiftLock.GamepadKey.Name}`
	else
		local obj = Instance.new('StringValue')
		obj.Name = 'BoundKeys'
		obj.Value = `{ShiftLock.Key.Name},{ShiftLock.GamepadKey.Name}`
		obj.Parent = MouseLockController
	end
end


return ShiftLock
