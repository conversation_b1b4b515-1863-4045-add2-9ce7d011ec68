local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local RunService = game:GetService('RunService')
local UserService  = game:GetService('UserService')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local Math = require(ReplicatedStorage.SharedModules.Math)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)

local TournamentController = {
	TournamentTimeLeft = 0
}


local function updateTournamentInfo(data: { MapName: string, Modifiers: {string}, TournamentNumber: number })
	local TournamentsFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Tournaments
	
	TournamentsFrame.Contents.Left.Stage.Container.StageName.Text = data.MapName:upper()
	TournamentsFrame.Contents.Left.Stage.Container.PlaceHolder.Image = Client.Immutable.Servers.PLACE_INFO[data.MapName].MapImage
	TournamentsFrame.Contents.Left.Info.Content.TournamentNumber.Text = `MONTHLY TOURNAMENT: {data.TournamentNumber}`
	
	for _, v in TournamentsFrame.Contents.Right.Modifiers.Contents:GetChildren() do
		if v:IsA'ImageLabel' then v:Destroy() end
	end
	
	for _, v in data.Modifiers do
		local ThisModifier = script.ModifierContainer:Clone()
		ThisModifier.Txt.Text = v
		ThisModifier.Parent = TournamentsFrame.Contents.Right.Modifiers.Contents
	end
end


local function setupTimer()
	local TournamentsFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Tournaments
	
	local tournamentStartTime = os.date('!*t', os.time())
	tournamentStartTime.day = 1
	tournamentStartTime.hour = 0
	tournamentStartTime.min = 0
	tournamentStartTime.sec = 0
	
	local tournamentEndTime = table.clone(tournamentStartTime)
	if tournamentEndTime.month == 12 then
		tournamentEndTime.month = 1
		tournamentEndTime.year += 1
	else
		tournamentEndTime.month += 1
	end
	
	local totalDuration = os.time(tournamentEndTime) - os.time(tournamentStartTime)
	
	RunService.RenderStepped:Connect(function(dt: number)
		TournamentController.TournamentTimeLeft -= dt
		
		local days, hours, minutes, seconds = Math.GetDHMS(TournamentController.TournamentTimeLeft, true)
		
		TournamentsFrame.LoadingBar.Txt.Text = `Time Remaining: {days} days, {hours} hours, {minutes} minutes, {seconds} seconds`
		
		local elapsed = os.time() - os.time(tournamentStartTime)
		local progressRatio = math.clamp(elapsed / totalDuration, 0, 1)
		TournamentsFrame.LoadingBar.Holder.Loader.UIGradient.Transparency = NumberSequence.new{
			NumberSequenceKeypoint.new(0, 1),
			NumberSequenceKeypoint.new(progressRatio, 1),
			NumberSequenceKeypoint.new(progressRatio + 0.001, 0),
			NumberSequenceKeypoint.new(1, 0),
		}
	end)
	
	TournamentsFrame.LoadingBar.Holder.Loader.UIGradient.Rotation += 180
end


local function loadPreviousRankings(data: { [string]: number })
	local TournamentsFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Tournaments
	
	for _, v in TournamentsFrame.Contents.Right.PreviousRankings.Contents:GetChildren() do
		if v:IsA'ImageLabel' then v:Destroy() end
	end
	
	for tournamentNum, rank in data do
		local ThisRankingContainer = script.PreviousRanking:Clone()
		ThisRankingContainer.Title.Text = `TOURNAMENT {tournamentNum}: #{rank}`
		ThisRankingContainer.LayoutOrder = -tonumber(tournamentNum)
		ThisRankingContainer.Parent = TournamentsFrame.Contents.Right.PreviousRankings.Contents
	end
end


local function loadRankings(data: { [string]: { Rank: number, Value: number } })
	local TournamentsFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Tournaments
	local TournamentsLeaderboardFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.TournamentLeaderboard
	
	for _, v in TournamentsLeaderboardFrame.Contents.Right.ScrollingFrame:GetChildren() do
		if v:IsA'ImageLabel' then v:Destroy() end
	end
	
	for userid, userInfo in data do
		userid = tonumber(userid)
		
		if userid == Players.LocalPlayer.UserId then
			TournamentsFrame.Contents.Left.Info.Bracket.Text = `YOUR RANKING: #{userInfo.Rank}`
		end
		
		if userInfo.Rank > 100 then continue end
		
		local ThisPlayerRankingContainer = script.PlayerRankingContainer:Clone()
		ThisPlayerRankingContainer.LayoutOrder = userInfo.Rank
		ThisPlayerRankingContainer.Contents.Rank.Text = `#{userInfo.Rank}`
		ThisPlayerRankingContainer.Score.Text = userInfo.Value
		
		task.spawn(function()
			local Username = ClassExtension.Players.GetUsernameFromUserId(userid)
			ThisPlayerRankingContainer.Contents.Info.User.Text = `@{Username}`
			
			local s, UserInfos = pcall(function()
				return UserService:GetUserInfosByUserIdsAsync({userid})[1]
			end)
			
			ThisPlayerRankingContainer.Contents.Info.Display.Text = s and UserInfos.DisplayName or Username
			
			if UserInfos and UserInfos.HasVerifiedBadge then
				ThisPlayerRankingContainer.Contents.Info.Display.Text ..= ' ' .. utf8.char(0xE000)
			end
			
			local img = ClassExtension.Players.GetUserThumbnailAsync(userid, Enum.ThumbnailType.HeadShot, Enum.ThumbnailSize.Size60x60)
			ThisPlayerRankingContainer.Contents.PlayerIcon.Icon.Image = img
		end)
		
		ThisPlayerRankingContainer.Parent = TournamentsLeaderboardFrame.Contents.Right.ScrollingFrame
	end
end


local function loadRewards()
	local ItemController = Client.GetController('ItemController')
	
	local TournamentsFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Tournaments
	local TournamentsLeaderboardFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.TournamentLeaderboard
	
	for _, v in TournamentsFrame.Contents.Right.Rewards.Contents:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	for _, v in TournamentsLeaderboardFrame.Contents.Left.Rewards.Contents.Top1.Contents:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	for _, v in TournamentsLeaderboardFrame.Contents.Left.Rewards.Contents.Top5.Contents:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	for _, v in TournamentsLeaderboardFrame.Contents.Left.Rewards.Contents.Top10.Contents:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	for itemName, itemInfo in Client.Immutable.Tournament.TOURNAMENT_REWARDS.Top1 do
		local ItemFrame = ItemController.CreateItem({
			Count = itemInfo.Count,
			Size = script.Reward.Size,
			ItemName = itemName,
			Parent = TournamentsFrame.Contents.Right.Rewards.Contents
		})
	end
	
	for itemName, itemInfo in Client.Immutable.Tournament.TOURNAMENT_REWARDS.Top1 do
		local ItemFrame = ItemController.CreateItem({
			Count = itemInfo.Count,
			Size = script.Reward.Size,
			ItemName = itemName,
			Parent = TournamentsLeaderboardFrame.Contents.Left.Rewards.Contents.Top1.Contents
		})
	end
	
	for itemName, itemInfo in Client.Immutable.Tournament.TOURNAMENT_REWARDS.Top5 do
		local ItemFrame = ItemController.CreateItem({
			Count = itemInfo.Count,
			Size = script.Reward.Size,
			ItemName = itemName,
			Parent = TournamentsLeaderboardFrame.Contents.Left.Rewards.Contents.Top5.Contents
		})
	end
	
	for itemName, itemInfo in Client.Immutable.Tournament.TOURNAMENT_REWARDS.Top10 do
		local ItemFrame = ItemController.CreateItem({
			Count = itemInfo.Count,
			Size = script.Reward.Size,
			ItemName = itemName,
			Parent = TournamentsLeaderboardFrame.Contents.Left.Rewards.Contents.Top10.Contents
		})
	end
end


function TournamentController._Start()
	local UIController = Client.GetController('UIController')
	
	local TournamentsFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.Tournaments
	local TournamentsLeaderboardFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.TournamentLeaderboard
	
	TournamentsFrame.Contents.Left.Buttons.Start.MouseButton1Click:Connect(function()
		Packets.Tournament.GoToTournament.send()
	end)
	
	Packets.Tournament.GetTournamentInfo.listen(updateTournamentInfo)
	Packets.Tournament.GetTournamentInfo.send()
	
	Packets.Tournament.GetTournamentTimeLeft.listen(function(data: number)
		TournamentController.TournamentTimeLeft = data
	end)
	Packets.Tournament.GetTournamentTimeLeft.send()
	
	Packets.Tournament.HighScore.listen(function(data: number)
		TournamentsFrame.Contents.Right.Top.Highscore.Text = `HIGH SCORE: {data or 'N/A'}`
	end)
	Packets.Tournament.HighScore.send()
	
	Packets.Tournament.GetPreviousRankings.listen(loadPreviousRankings)
	Packets.Tournament.GetPreviousRankings.send()
	
	Packets.Tournament.GetTournamentRankings.listen(loadRankings)
	Packets.Tournament.GetTournamentRankings.send()
	
	Packets.Tournament.CanClaim.listen(function(data: boolean)
		TournamentsLeaderboardFrame.Contents.Left.Buttons.Claim.Visible = data
	end)
	Packets.Tournament.CanClaim.send()
	
	TournamentsFrame.Leaderboards.MouseButton1Click:Connect(function()
		UIController.Open(Players.LocalPlayer.PlayerGui.LobbyUI.Pages.TournamentLeaderboard)
	end)
	
	TournamentsLeaderboardFrame.Contents.Left.Buttons.Cancel.MouseButton1Click:Connect(function()
		UIController.Open(TournamentsFrame)
	end)
	
	TournamentsLeaderboardFrame.Contents.Left.Buttons.Claim.MouseButton1Click:Connect(function()
		Packets.Tournament.Claim.send()
		TournamentsLeaderboardFrame.Contents.Left.Buttons.Claim.Visible = false
	end)
	
	task.defer(loadRewards)
	task.defer(setupTimer)
end


return TournamentController