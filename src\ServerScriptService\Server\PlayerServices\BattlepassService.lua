local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')

local Client = require(ReplicatedStorage.Client)
local Server = require(ServerScriptService.Server)

local Packets = require(ReplicatedStorage.Data.Packets)
local BattlepassData = require(ReplicatedStorage.Data.Battlepass)
local SimpleMarketplace = require(ReplicatedStorage.Data.SimpleMarketplace)

local BattlepassService = {}


function BattlepassService.Claim(plr: Player, data: { Free: boolean, Tier: number })
	local DataService = Server.GetService('DataService')

	local profile = DataService.GetProfile(plr)
	
	local claimKey = data.Free and 'ClaimedFree' or 'ClaimedPaid'
	local isClaimed = profile.Battlepass[claimKey][data.Tier]
	
	if BattlepassService.CanClaim(plr, data.Tier) and not isClaimed then
		if not data.Free and not SimpleMarketplace.UserOwnsGamePassAsync(plr.UserId, SimpleMarketplace.Gamepasses['Premium Pass'].ID) then
			return
		end
		
		profile.Battlepass[claimKey][data.Tier] = true
		
		local rewardKey = data.Free and 'Free' or 'Paid'
		local rewardInfo = BattlepassData.Tiers[data.Tier][rewardKey].Reward
		
		Server.GetService(rewardInfo.Service)[rewardInfo.Function](plr, table.unpack(rewardInfo.Args))
		
		Packets.Battlepass.GetPlayerBattlepassInfo.sendTo({
			Level = profile.Battlepass.Level,
			XP = profile.Battlepass.XP,
			ClaimedFree = profile.Battlepass.ClaimedFree,
			ClaimedPaid = profile.Battlepass.ClaimedPaid
		}, plr)
	end
end


function BattlepassService.CanClaim(plr: Player, tier: number)
	local DataService = Server.GetService('DataService')
	local profile = DataService.GetProfile(plr)
	return profile.Battlepass.Level >= tier
end


function BattlepassService.AddXP(plr: Player, xp: number)
	local DataService = Server.GetService('DataService')

	local profile = DataService.GetProfile(plr)

	profile.Battlepass.XP += xp

	while true do
		local NextTierInfo = BattlepassData.Tiers[profile.Battlepass.Level + 1]
		if NextTierInfo then
			if profile.Battlepass.XP >= NextTierInfo.XPReq then
				profile.Battlepass.Level += 1
				profile.Battlepass.XP -= NextTierInfo.XPReq
			else
				break
			end
		else
			break
		end
	end
	
	Packets.Battlepass.GetPlayerBattlepassInfo.sendTo({
		Level = profile.Battlepass.Level,
		XP = profile.Battlepass.XP,
		ClaimedFree = profile.Battlepass.ClaimedFree,
		ClaimedPaid = profile.Battlepass.ClaimedPaid
	}, plr)
end


function BattlepassService._PlayerAdded(plr: Player)
	local DataService = Server.GetService('DataService')

	local profile = DataService.GetProfile(plr)

	if profile.Battlepass.CurrentBattlepass ~= BattlepassData.ID then
		profile.Battlepass.Level = 1
		profile.Battlepass.XP = 0
		profile.Battlepass.CurrentBattlepass = BattlepassData.ID
		
		local dataTemplate = DataService.GetTemplate()
		profile.Battlepass.ClaimedFree = dataTemplate.Battlepass.ClaimedFree
		profile.Battlepass.ClaimedPaid = dataTemplate.Battlepass.ClaimedPaid
	end
end


function BattlepassService._Start()
	local DataService = Server.GetService('DataService')

	Packets.Battlepass.GetPlayerBattlepassInfo.listen(function(_, plr: Player)
		local profile = DataService.GetProfile(plr)
		Packets.Battlepass.GetPlayerBattlepassInfo.sendTo({
			Level = profile.Battlepass.Level,
			XP = profile.Battlepass.XP,
			ClaimedFree = profile.Battlepass.ClaimedFree,
			ClaimedPaid = profile.Battlepass.ClaimedPaid
		}, plr)
	end)
	
	Packets.Battlepass.ClaimTier.listen(function(data: { Free: boolean, Tier: number }, plr: Player)
		BattlepassService.Claim(plr, data)
	end)
	
	Packets.Battlepass.ClaimAll.listen(function(_, plr: Player)
		for tierNum, v in BattlepassData.Tiers do
			if BattlepassService.CanClaim(plr, tierNum) then
				BattlepassService.Claim(plr, { Free = true, Tier = tierNum })
				BattlepassService.Claim(plr, { Free = false, Tier = tierNum })
			end
		end
	end)
end


return BattlepassService