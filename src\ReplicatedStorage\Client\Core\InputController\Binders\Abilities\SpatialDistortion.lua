local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)
local Packets = require(ReplicatedStorage.Data.Packets)

local SpatialDistortion = {
	Key = Enum.KeyCode.V,

	GamepadKey = Enum.KeyCode.ButtonR2
}


function SpatialDistortion.KeyDown()
	Packets.Ability.UseAbility.send({
		AbilityName = script.Name,
		Args = {}
	})
end


function SpatialDistortion.KeyUp()

end


function SpatialDistortion.Click()
	SpatialDistortion.KeyDown()
end


function SpatialDistortion.IsOnCooldown()
	return Client.GetController'AbilityController'.IsAbilityOnCooldown(script.Name)
end


function SpatialDistortion.CanUseAbilityAsCharacter()
	return Client.GetController'AbilityController'.CanUseAbilityAsCharacter(script.Name)
end


function SpatialDistortion._Init()

end


return SpatialDistortion
