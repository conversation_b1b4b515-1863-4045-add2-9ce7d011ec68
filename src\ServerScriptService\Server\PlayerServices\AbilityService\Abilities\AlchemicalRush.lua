local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local TweenService = game:GetService('TweenService')
local Debris = game:GetService('Debris')
local CollectionService = game:GetService('CollectionService')
local SoundService = game:GetService('SoundService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local Table = require(ReplicatedStorage.SharedModules.TableUtil)
local CookingService: typeof(require(ServerScriptService.Server.GameServices.CookingService)) = Server.GetService('CookingService')

local AlchemicalRush = {
	COOLDOWN = 5,
	EFFECT_TIME = 6,
	
	Cooldowns = {} :: { [Player]: number },
	AbilityJanitors = {} :: { [Player]: Janitor.Janitor }
}


function AlchemicalRush.GetCooldownTimeLeft(plr: Player): number
	if AlchemicalRush.Cooldowns[plr] then
		return AlchemicalRush.COOLDOWN - (os.clock() - AlchemicalRush.Cooldowns[plr])
	end
	return 0
end


function AlchemicalRush.CanUse(plr: Player): boolean
	if AlchemicalRush.Cooldowns[plr] then
		return os.clock() - AlchemicalRush.Cooldowns[plr] > AlchemicalRush.COOLDOWN
	end
	
	return true
end


function AlchemicalRush.IsInUse(plr: Player): boolean
	if AlchemicalRush.AbilityJanitors[plr] and AlchemicalRush.AbilityJanitors[plr]:Get('Active') then
		return true
	end

	return false
end


function AlchemicalRush.UseAbility(plr: Player): boolean
	if not AlchemicalRush.CanUse(plr) or AlchemicalRush.IsInUse(plr) then return false end

	AlchemicalRush.Cooldowns[plr] = os.clock()

	task.delay(AlchemicalRush.COOLDOWN, function()
		AlchemicalRush.Cooldowns[plr] = nil
	end)

	if not AlchemicalRush.AbilityJanitors[plr] then
		AlchemicalRush.AbilityJanitors[plr] = Janitor.new()
	end
	
	AlchemicalRush.AbilityJanitors[plr]:Add(math.random, nil, 'Active')
	
	ClassExtension.Player.DisableMovement(plr, true)
	plr:SetAttribute(script.Name, true)
	
	local AlchemTrack: AnimationTrack = Animations:PlayTrack(plr, 'AlchemicalRush')
	
	SoundService.SFX.Abilities.AlchemicalRush:Play()
	
	do
		local CharFX = {} :: { ParticleEmitter }
		for _, v in script.HumanoidRootPart:GetChildren() do
			local ThisFX = v:Clone()
			ThisFX.Parent = plr.Character.HumanoidRootPart
			table.insert(CharFX, ThisFX)
		end

		AlchemicalRush.AbilityJanitors[plr]:Add(function()
			for _, v in CharFX do
				VFXFunctions.DisableDescendants(v)
				Debris:AddItem(v, VFXFunctions.GetHighestWaitTime(v))
			end
		end)
	end
	
	AlchemicalRush.AbilityJanitors[plr]:Add(task.delay(AlchemicalRush.EFFECT_TIME, function()
		AlchemicalRush.AbilityJanitors[plr]:Cleanup()
	end))
	
	AlchemicalRush.AbilityJanitors[plr]:Add(AlchemTrack.Ended:Connect(function()
		ClassExtension.Player.DisableMovement(plr, false)
		AlchemicalRush.AbilityJanitors[plr]:Remove('Active')
	end))
	
	AlchemicalRush.AbilityJanitors[plr]:Add(function()
		plr:SetAttribute(script.Name, nil)
		Animations:StopTrack(plr, 'AlchemicalRush')
		ClassExtension.Player.DisableMovement(plr, false)
		
		Server.GetService('AbilityService').UpdatePassives(plr)
	end)
	
	return true
end


function AlchemicalRush.CancelAbility(plr: Player)
	if not AlchemicalRush.IsInUse(plr) then return end

	if AlchemicalRush.AbilityJanitors[plr] then
		AlchemicalRush.AbilityJanitors[plr]:Cleanup()
	end
end


return AlchemicalRush