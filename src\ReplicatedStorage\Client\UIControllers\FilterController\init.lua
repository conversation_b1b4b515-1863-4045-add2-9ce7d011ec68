local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)

local FilterController = {
	CustomFilters = {},
	
	Filters = {} :: { [string]: { [string]: { [string]: { Order: number, Func: (GuiObject) -> boolean } } } },
	CurrentFilterData = {
		FrameName = nil,
		Filter = {}
	},
	
	Apply = true
}



local function setupFilterFrame()
	local UIController = Client.GetController('UIController')

	local FilterFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Filter

	FilterFrame.Buttons.CANCEL.MouseButton1Click:Connect(function()
		FilterController.Apply = false
		UIController.Open(Players.LocalPlayer.PlayerGui.LobbyUI.Pages[FilterController.CurrentFilterData.FrameName])
	end)

	FilterFrame.Buttons.RESET.MouseButton1Click:Connect(function()
		local frameToOpen = FilterController.CurrentFilterData.FrameName
		FilterController.DisableFilter()
		FilterController.OpenFilter({Name = frameToOpen})
	end)

	FilterFrame.Buttons.APPLY.MouseButton1Click:Connect(function()
		FilterController.Apply = true
		UIController.Open(Players.LocalPlayer.PlayerGui.LobbyUI.Pages[FilterController.CurrentFilterData.FrameName])
	end)
end


function FilterController.AddCustomFilter(filterKey: string, filterFunc: (GuiObject) -> boolean?)
	FilterController.CustomFilters[filterKey] = filterFunc
end


function FilterController.RemoveCustomFilter(filterKey: string)
	FilterController.CustomFilters[filterKey] = nil
end


function FilterController.HasFilter(filterKey: string)
	return FilterController.CustomFilters[filterKey] ~= nil
end


function FilterController.OpenFilter(openedFrame: GuiObject?)
	local UIController = Client.GetController('UIController')
	
	local FilterFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Filter
	
	local openedFrame = openedFrame or UIController.GetOpenedFrame()
	if openedFrame and FilterController.Filters[openedFrame.Name] then
		UIController.Open(FilterFrame)
		
		if FilterController.CurrentFilterData.FrameName == openedFrame.Name then return end
		
		for _, v in FilterFrame.Holder.Scroller:GetChildren() do
			if v:IsA'GuiObject' then v:Destroy() end
		end
		
		local order = 1
		for categoryName, categoryModule in FilterController.Filters[openedFrame.Name] do
			local categoryFrame = ReplicatedStorage.Assets.Templates.FilterHeader:Clone()
			categoryFrame.ItemName.Text = categoryName:upper()
			categoryFrame.LayoutOrder = order
			categoryFrame.Parent = FilterFrame.Holder.Scroller
			
			for filterName, filterInfo in categoryModule do
				local filterSlot = ReplicatedStorage.Assets.Templates.FilterSlot:Clone()
				filterSlot.Txt.Text = filterName:upper()
				filterSlot.LayoutOrder = order + filterInfo.Order
				filterSlot:SetAttribute('Category', categoryName)
				filterSlot.Parent = FilterFrame.Holder.Scroller
				
				filterSlot.MouseButton1Click:Connect(function()
					for _, v in FilterFrame.Holder.Scroller:GetChildren() do
						if v:GetAttribute'Category' == categoryName then
							v.IsSelected.Enabled = v == filterSlot
							v.NotSelected.Enabled = v ~= filterSlot
						end
					end
					FilterController.CurrentFilterData.Filter[categoryName] = filterName
				end)
				
				if filterInfo.Order == 1 then
					filterSlot.IsSelected.Enabled = true
					filterSlot.NotSelected.Enabled = false
					FilterController.CurrentFilterData.Filter[categoryName] = filterName
				end
			end
			
			order += 1_000
		end
		
		FilterController.CurrentFilterData.FrameName = openedFrame.Name
	end
end


function FilterController.DisableFilter()
	table.clear(FilterController.CustomFilters)
	FilterController.CurrentFilterData.FrameName = nil
	FilterController.CurrentFilterData.Filter = {}
end


function FilterController.ApplyFilter(frames: { GuiObject }, textFilter: string?)
	if FilterController.Apply == false then
		FilterController.Apply = true
		return
	end
	
	if FilterController.CurrentFilterData.FrameName then
		for _, frame in frames do
			if not frame:IsA'GuiObject' then continue end
			
			local isEnabled = true
			
			for categoryName, categoryModule in FilterController.Filters[FilterController.CurrentFilterData.FrameName] do
				isEnabled = not not categoryModule[FilterController.CurrentFilterData.Filter[categoryName]].Func(frame)
				
				if isEnabled == false then break end
			end
			
			frame:SetAttribute('FilterVisibility', isEnabled)
			frame.Visible = isEnabled
		end
	else
		for _, frame in frames do
			if not frame:IsA'GuiObject' then continue end
			frame:SetAttribute('FilterVisibility', nil)
		end
	end
	
	if textFilter then
		local arrangedFilteredText = textFilter:lower():gsub('^%s*(.-)%s*$', '%1'):gsub('%s+', ' ')
		
		for _, frame in frames do
			if not frame:IsA'GuiObject' or frame:GetAttribute'FilterVisibility' == false then continue end
			local ThisFrameName = (frame:GetAttribute'Name' or frame.Name):lower()
			frame.Visible = ThisFrameName:lower():find(arrangedFilteredText) ~= nil
		end
	end
	
	for k, filterFunc in FilterController.CustomFilters do
		for _, frame in frames do
			if not frame:IsA'GuiObject' or frame.Visible == false then continue end
			frame.Visible = filterFunc(frame) 
		end
	end
end


function FilterController._Start()
	local UIController = Client.GetController('UIController')
	
	local FilterFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.Filter
	
	--[[UIController.PageToggled:Connect(function(frame: GuiObject, isOpen: boolean)
		if frame == FilterFrame and isOpen == false then
			FilterController.CurrentFilterData.FrameName = nil
			FilterController.CurrentFilterData.Filter = {}
		end
	end)]]
	
	task.defer(setupFilterFrame)
end


function FilterController._Init()
	for _, filterFolder in script:GetChildren() do
		FilterController.Filters[filterFolder.Name] = {}
		for _, module in filterFolder:GetChildren() do
			FilterController.Filters[filterFolder.Name][module.Name] = require(module)
		end
	end
end


return FilterController