local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local AnalyticsService = game:GetService('AnalyticsService')

local Server = require(ServerScriptService.Server)

local Math = require(ReplicatedStorage.SharedModules.Math)
local Packets = require(ReplicatedStorage.Data.Packets)

local CurrencyService = {}


local function logEconomy(...)
	local args = {...}
	task.spawn(function()
		pcall(AnalyticsService.LogEconomyEvent, AnalyticsService, table.unpack(args))
	end)
end


function CurrencyService.AddGems(plr: Player, amount: number, source: string?, sku: string?)
	local DataService = Server.GetService('DataService')
	
	if not Math.IsFinite(amount) then
		amount = 0
	end
	
	if amount == 0 then return end
	
	DataService.Increment(plr, 'Gems', amount)
	
	Packets.HUD.UpdateGems.sendTo(DataService.Get(plr, 'Gems'), plr)
	
	logEconomy(
		plr,
		amount > 0 and Enum.AnalyticsEconomyFlowType.Source or Enum.AnalyticsEconomyFlowType.Sink,
		'Gems',
		math.abs(amount),
		CurrencyService.GetGems(plr),
		source or 'UnknownSource',
		sku
	)
end


function CurrencyService.GetGems(plr: Player)
	local DataService = Server.GetService('DataService')
	return DataService.Get(plr, 'Gems')
end


function CurrencyService.AddCoins(plr: Player, amount: number, source: string?, sku: string?)
	local DataService = Server.GetService('DataService')
	local ChallengeService = Server.GetService('ChallengeService')
	
	if not Math.IsFinite(amount) then
		amount = 0
	end
	
	if ChallengeService.Challenge then
		amount *= 1.3
	end
	
	DataService.Increment(plr, 'Coins', amount)
	
	Packets.HUD.UpdateCoins.sendTo(DataService.Get(plr, 'Coins'), plr)
	
	logEconomy(
		plr,
		amount > 0 and Enum.AnalyticsEconomyFlowType.Source or Enum.AnalyticsEconomyFlowType.Sink,
		'Coins',
		amount,
		CurrencyService.GetCoins(plr),
		source or 'UnknownSource',
		sku
	)
end


function CurrencyService.GetCoins(plr: Player)
	local DataService = Server.GetService('DataService')
	return DataService.Get(plr, 'Coins')
end


function CurrencyService.AddXP(plr: Player, amount: number)
	local DataService = Server.GetService('DataService')
	
	if not Math.IsFinite(amount) then
		amount = 0
	end
	
	DataService.Increment(plr, 'XP', amount)
	
	Packets.HUD.UpdateLevel.sendTo({
		Level = DataService.Get(plr, 'Level'),
		XP = DataService.Get(plr, 'XP')
	}, plr)
end


function CurrencyService._Start()
	local DataService = Server.GetService('DataService')
	
	Packets.HUD.UpdateGems.listen(function(_, plr: Player)
		Packets.HUD.UpdateGems.sendTo(DataService.Get(plr, 'Gems'), plr)
	end)
	
	Packets.HUD.UpdateCoins.listen(function(_, plr: Player)
		Packets.HUD.UpdateCoins.sendTo(DataService.Get(plr, 'Coins'), plr)
	end)
	
	Packets.HUD.UpdateLevel.listen(function(_, plr: Player)
		CurrencyService.AddXP(plr, 0)
	end)
end


return CurrencyService