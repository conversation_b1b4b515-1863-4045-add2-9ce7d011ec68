local Types = require(script.Parent.Parent.Package.Util.Types)
local AnimationIdsUtil = require(script.Parent.Parent.Package.Util.AnimationIdsUtil)

local HasAnimatedObject = AnimationIdsUtil.HasAnimatedObject
local HasProperties = AnimationIdsUtil.HasProperties

--[=[
	@type rigType string
	@within AnimationIds

	The first key in the `AnimationIds` module that indicates the type of rig the paired animation id table belongs to.

	```lua
	local AnimationIds = {
		Player = { -- `rigType` of "Player"
			Dodge = {
				[Enum.KeyCode.W] = 0000000,
				[Enum.KeyCode.S] = 0000000,
				[Enum.KeyCode.A] = 0000000,
				[Enum.KeyCode.D] = 0000000,
			},
			Run = 0000000,
			Walk = 0000000,
			Idle = 0000000
		}
	}
	```

	:::info
	The only preset `rigType` is that of **"Player"** for all player/client animation ids.
	:::
]=]

--[=[
	@type animationId number
	@within AnimationIds
]=]

--[=[
	@interface idTable
	@within AnimationIds
	.[any] idTable | animationId
]=]

--[=[
	@interface propertiesSettings
	@within AnimationIds
	.Priority Enum.AnimationPriority?
	.Looped boolean?
	.StartSpeed number? -- Auto set animation speed through [`Animations:PlayTrack()`](/api/AnimationsServer#PlayTrack) related methods
	.DoUnpack boolean? -- Set the key-value pairs of [`animationId`](#HasProperties) (if it's a table) *in the parent table*
	.MarkerTimes boolean? -- Support [`Animations:GetTimeOfMarker()`](/api/AnimationsServer#GetTimeOfMarker)

	:::caution *changed in version 2.1.0*
	Added `MarkerTimes` property	
	:::
	
	:::caution *changed in version 2.3.0*
	Added `StartSpeed` property
	:::
]=]

--[=[
	@type HasProperties (animationId: idTable, propertiesSettings: propertiesSettings): {}
	@within AnimationIds

	:::tip *added in version 2.0.0*
	:::

	```lua
	local AnimationIds = {
		Player = { -- `rigType` of "Player"
			Dodge = {
				[Enum.KeyCode.W] = 0000000,
				[Enum.KeyCode.S] = 0000000,
				[Enum.KeyCode.A] = 0000000,
				[Enum.KeyCode.D] = 0000000,
			},
			Run = 0000000,
			Walk = 0000000,
			Idle = 0000000,
			Sword = {
				-- Now when the "Sword.Walk" animation plays it will
				-- automatically have `Enum.AnimationPriority.Action` priority
				Walk = HasProperties(0000000, { Priority = Enum.AnimationPriority.Action })

				Idle = 0000000,
				Run = 0000000,

	            -- Now when {"Sword", "AttackCombo", 1 or 2 or 3} animation
	            -- plays it will automatically have `Enum.AnimationPriority.Action` priority and
				-- will support `Animations:GetTimeOfMarker()`
				AttackCombo = HasProperties({
					[1] = 0000000,
					[2] = 0000000,
					[3] = 0000000
				}, { Priority = Enum.AnimationPriority.Action, MarkerTimes = true })
			}
		},
	}
	```
]=]

--[=[
	@interface animatedObjectSettings
	@within AnimationIds
	.AutoAttach boolean?
	.AutoDetach boolean?
	.DoUnpack boolean? -- Set the key-value pairs of [`animationId`](#HasAnimatedObject) (if it's a table) *in the parent table*
]=]

--[=[
	@tag Beta
	@type HasAnimatedObject (animationId: idTable, animatedObjectPath: path, animatedObjectSettings: animatedObjectSettings): {}
	@within AnimationIds

	```lua
	local AnimationIds = {
		Player = { -- `rigType` of "Player"
			Dodge = {
				[Enum.KeyCode.W] = 0000000,
				[Enum.KeyCode.S] = 0000000,
				[Enum.KeyCode.A] = 0000000,
				[Enum.KeyCode.D] = 0000000,
			},
			Run = 0000000,
			Walk = 0000000,
			Idle = 0000000,
			Sword = {
				-- Now when the "Sword.Walk" animation plays "Sword" will
				-- auto attach to the player and get animated
				Walk = HasAnimatedObject(0000000, "Sword", { AutoAttach = true })

				Idle = 0000000,
				Run = 0000000,

				-- Now when {"Sword", "AttackCombo", 1 or 2 or 3} animation
				-- plays "Sword" will auto attach to the player and get
				-- animated
				AttackCombo = HasAnimatedObject({
					[1] = 0000000,
					[2] = 0000000,
					[3] = 0000000
				}, "Sword", { AutoAttach = true })
			}
		},
	}
	```

	:::info
	For more information on setting up animated objects check out [animated objects tutorial](/docs/animated-objects).
	:::
]=]

--[=[
	@interface AnimationIds
	@within AnimationIds
	.[rigType] idTable

	```lua
	local AnimationIds = {
		Player = { -- `rigType` of "Player"
			Dodge = {
				[Enum.KeyCode.W] = 0000000,
				[Enum.KeyCode.S] = 0000000,
				[Enum.KeyCode.A] = 0000000,
				[Enum.KeyCode.D] = 0000000,
			},
			Run = 0000000,
			Walk = 0000000,
			Idle = 0000000
		},

		BigMonster = { -- `rigType` of "BigMonster"
			HardMode = {
				Attack1 = 0000000,
				Attack2 = 0000000
			},
			EasyMode = {
				Attack1 = 0000000,
				Attack2 = 0000000
			}
		},

		SmallMonster = { -- `rigType` of "SmallMonster"
			HardMode = {
				Attack1 = 0000000,
				Attack2 = 0000000
			},
			EasyMode = {
				Attack1 = 0000000,
				Attack2 = 0000000
			}
		}
	}
	```
]=]

--[=[
	@tag Read Only
	@class AnimationIds

	:::note
	Roblox model path: `Animations.Deps.AnimationIds`
	:::
]=]
local AnimationIds = {
	Player = {
		Run = HasProperties(107066168379441, {Looped = true, Priority = Enum.AnimationPriority.Action2}),
		Idle = HasProperties(84707419155609, {Looped = true}),
		
		PlatePickup = HasProperties(94068044274815, {Looped = false}),
		PlateHold = HasProperties(97299526073313, {Looped = true}),
		PlatePutDown = HasProperties(91724914968538, {Looped = false}),
		
		Cut = HasProperties(134362059695685, {Looped = true}),
		
		SinkStart = HasProperties(73970355682703, {Looped = false}),
		SinkWashing = HasProperties(130972891455225, {Looped = true}),
		
		Throw = HasProperties(133476447703696, {Looped = false}),
		Catch = HasProperties(94619398773489, {Looped = false}),
		
		FugaFlameArrow = HasProperties(80123643488392, {Looped = false}),
		SukunaSlash = HasProperties(135204149806087, {Looped = false}),
		
		RulerAuthority = HasProperties(94594001686844, {Looped = false}),
		ShadowExchange = HasProperties(75100060547993, {Looped = true}),
		
		KingGlare = HasProperties(105281291829146, {Looped = false}),
		VampiricAgility = HasProperties(92352473982972, {Looped = false}),

		InfinityBarrier = HasProperties(126665646505594, {Looped = false}),
		SpatialDistortion = HasProperties(83635805126381, {Looped = false}),
		
		YatanoKagamiStart = HasProperties(109977057753432, {Looped = false}),
		YatanoKagamiEnd = HasProperties(86018133756158, {Looped = false}),
		LightFlashIntervention = HasProperties(108548578266250, {Looped = false}),
		
		AlchemicalRush = HasProperties(131236840920676, {Looped = false}),
		
		TogeStop = HasProperties(88614635950600, {Looped = false}),
		
		RengokuFlameSurge = HasProperties(112106114443853, {Looped = false})
	}
}

return AnimationIds :: Types.AnimationIdsType