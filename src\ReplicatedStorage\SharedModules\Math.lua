--!native

--[=[
	Author: <PERSON><PERSON><PERSON> (Syveric)
	Math utility functions
]=]

local GuiService = game:GetService('GuiService')

local Math = {}

-- Constants
Math.phi = (1 + math.sqrt(5)) / 2
Math.e = math.exp(1)

Math.StudToMeter = 0.28 -- 1 stud = 0.28 meters
Math.MeterToStud = 3.571428571 -- 1 meter = ~3.57 studs

Math.G = 6.673 * 10^-11 -- Gravitational constant (in m³/kg/s²)
Math.c = 299792458 -- Speed of light in m/s

Math.inf = 1/0
Math.nan = 0/0

Math.int8 = 2^7 - 1
Math.int16 = 2^15 - 1
Math.int32 = 2^31 - 1
Math.int64 = 2^63 - 1
Math.uint8 = 2^8 - 1
Math.uint16 = 2^16 - 1
Math.uint32 = 2^32 - 1
Math.uint64 = 2^64 - 1

Math.byte = 8
Math.kilobyte = 1024
Math.megabyte = Math.kilobyte * 1024
Math.gigabyte = Math.megabyte * 1024
Math.terabyte = Math.gigabyte * 1024
Math.petabyte = Math.terabyte * 1024

Math.rand = Random.new()


-- Unit Conversions
do
	function Math.StudsToMeters(studs: number): number
		return studs * Math.StudToMeter
	end

	function Math.MetersToStuds(meters: number): number
		return meters * Math.MeterToStud
	end

	function Math.KmhToStudsPerSecond(kmh: number): number
		return Math.MetersToStuds(kmh / 3.6)
	end

	function Math.MphToStudsPerSecond(mph: number): number
		return Math.MetersToStuds(mph * 0.44704)
	end

	function Math.StudsPerSecondToKmh(studsPerSec: number): number
		return Math.StudsToMeters(studsPerSec) * 3.6
	end

	function Math.StudsPerSecondToMph(studsPerSec: number): number
		return Math.StudsToMeters(studsPerSec) * 2.237
	end
end


-- Numeric Utilities
do
	function Math.Isinf(n: number): boolean
		return n == Math.inf
	end

	function Math.Isnan(n: number): boolean
		return n ~= n
	end

	function Math.IsFinite(n: number): boolean
		return not (Math.Isnan(n) or Math.Isinf(n))
	end
	
	function Math.FlipSign(n: number): number
		return n * -1
	end
	
	function Math.CopySign(n: number, copySign: number): number
		return math.abs(n) * math.sign(copySign)
	end

	function Math.IsInRange(n: number, min: number, max: number): boolean
		return n >= min and n <= max
	end

	function Math.TruncateToNDecimals(num: number, decimals: number): number
		local factor = 10 ^ decimals
		return math.floor(num * factor) / factor
	end

	function Math.SnapNumber(n: number, snap: number): number
		return math.round(n / snap) * snap
	end

	-- Retrieves the digit at a specific position from the right (1-based index)
	function Math.GetDigitAtPos(n: number, pos: number): number
		-- starts counting from the end of number
		return math.floor(n%10^pos/(10^pos/10))
	end

	-- Trims a number by replacing a set number of trailing digits with zeroes
	function Math.AddTrailingZeros(n: number, trailing: number): number
		-- 123456, 2 -> 123400
		n = tostring(n)
		return tonumber(n:sub(1, #n - trailing) .. ('0'):rep(trailing))
	end

	function Math.RoundDownToMultiple(n: number, multiple: number): number
		return n - (n % multiple)
	end
	
	function Math.RoundToNearestMultiple(n: number, multiple: number)
		local remainder = n % multiple
		return remainder < multiple/2 and n-remainder or n + (multiple-remainder)
	end
	
	function Math.PositiveMod(a: number, b: number): number
		return (a % b + b) % b
	end

	-- Approximates square root of `x` using Newton-Raphson method
	function Math.NewtonRaphson(x: number, iterations: number?): number
		if x == 0 then return 0 end
		iterations = iterations or 5

		local estimate = x / 2
		for i = 1, iterations do
			estimate = (estimate + x / estimate) / 2
		end

		return estimate
	end

	-- Legendary Quake trick
	function Math.FastInvSqrt(n: number): number
		local packed = string.pack('<f', n)
		local i = string.unpack('<I4', packed)
		i = 0x5f3759df - bit32.rshift(i, 1)
		local y = string.unpack('<f', string.pack('<I4', i))
		y = y * (1.5 - (n*.5 * y * y))
		return y
	end

	function Math.NaturalLog(exp: number): number
		return math.log(exp, Math.e)
	end

	function Math.Derivative(x: number, dx: number, func: (number) -> number): number
		return (func(x + dx) - func(x)) / dx
	end

	-- Returns angle difference (shortest path) between two angles
	function Math.AngleDifference(a: number, b: number): number
		local diff = (b - a + 180) % 360 - 180
		return diff
	end

	function Math.IsPermutation(a: number, b: number): boolean
		local counts = table.create(10, 0)

		while a > 0 do
			local digit = a % 10
			counts[digit + 1] += 1
			a //= 10
		end

		while b > 0 do
			local digit = b % 10
			counts[digit + 1] -= 1
			b //= 10
		end

		for i = 1, 10 do
			if counts[i] ~= 0 then
				return false
			end
		end

		return true
	end

	function Math.ReverseNumber(n: number): number
		local reversed = 0

		while n > 0 do
			reversed = 10 * reversed + n % 10
			n = n // 10
		end

		return reversed
	end

	function Math.Factorial(n: number): number
		if n < 0 then
			return Math.nan
		end
		
		local res = 1
		for i = 2, n do
			res *= i
		end
		return res
	end

	do
		local PRECISION = 1.0000001

		function Math.ConvertOrderedDataToNumber(n: number): number
			return math.floor(n ~= 0 and (PRECISION ^ n) or 0)
		end

		function Math.ConvertNumberToOrderedData(n: number): number
			return n ~= 0 and math.floor(math.log(n) / math.log(PRECISION)) or 0
		end
	end
end

-- Interpolation & Mapping
do
	-- Remaps a value `t` from range [a, b] to [c, d]; clamps to output range if `clamp` is true
	function Math.MapToRange(t: number, a: number, b: number, c: number, d: number, clamp: boolean?): number
		local res = c + ((d - c) / (b - a)) * (t - a)
		return clamp and math.clamp(res, c, d) or res
	end

	function Math.Damp(current: number, target: number, smoothing: number, dt: number): number
		return math.lerp(current, target, 1 - math.exp(-smoothing * dt))
	end

	function Math.AngleLerp(a1: number, a2: number, t: number): number
		local delta = (a2 - a1 + 180) % 360 - 180
		return (a1 + delta * t) % 360
	end
end

-- Geometry & Vector Math
do
	function Math.TriangleArea(a: Vector3, b: Vector3, c: Vector3): number
		return ((b - a):Cross(c - a)).Magnitude / 2
	end

	-- Normalizes a value to a range between 0 and 1 based on min/max
	function Math.Bound(min: number, max: number, value: number): number
		return (-max + value) / (-max + min)
	end
	
	function Math.Reflect(normal: Vector3, dir: Vector3): Vector3
		return dir - 2 * (dir:Dot(normal)) * normal
	end

	function Math.ClosestPointToLine(linePos: Vector3, lineDirection: Vector3, pos: Vector3, distFromStart: number): Vector3
		return linePos + lineDirection * math.clamp((pos - linePos):Dot(lineDirection), 0, distFromStart)
	end

	function Math.AngleBetween(v1: Vector3, v2: Vector3): number
		return math.deg(math.acos(math.clamp(v1.Unit:Dot(v2.Unit), -1, 1)))
	end

	function Math.RotateVectorAroundAxis(vec: Vector3, axis: Vector3, angle: number): Vector3
		return CFrame.fromAxisAngle(axis.Unit, math.rad(angle)):VectorToWorldSpace(vec)
	end

	function Math.GetPointsOnCircle(center: CFrame, radius: number, amount: number, startAngle: number?, endAngle: number?, offset: number?, y: number?): {Vector3}
		local points = table.create(amount)

		offset = offset or -0
		startAngle = (startAngle or 0) + offset
		endAngle = (endAngle or math.pi * 2) + offset

		local angleRange = endAngle - startAngle
		local angleStep = angleRange / amount

		for i = 1, amount do
			local angle = startAngle + (i - 1) * angleStep

			points[i] = Vector3.new(
				center.X + radius * math.cos(angle),
				center.Y + (y or 0),
				center.Z + radius * math.sin(angle)
			)
		end

		return points
	end
	
	function Math.GenerateZigZagPath(start: Vector3, endPos: Vector3, segments: number, amplitude: NumberRange?): {Vector3}
		local path = table.create(segments + 2)
		path[1] = start

		local direction = (endPos - start).Unit
		local totalDistance = (endPos - start).Magnitude
		local segmentLength = totalDistance / segments

		local up = Vector3.new(0, 1, 0)
		local perp = direction:Cross(up)
		if perp.Magnitude == 0 then
			-- If direction is vertical, use another vector for cross
			perp = direction:Cross(Vector3.new(1, 0, 0))
		end
		perp = perp.Unit

		for i = 0, segments do
			local basePoint = start + direction * segmentLength * i
			local thisAplitude = amplitude and Math.rand:NextNumber(amplitude.Min, amplitude.Max) or 1
			local offset = perp * thisAplitude * ((i % 2 == 0) and 1 or -1)
			local zigzagPoint = basePoint + offset
			path[i + 2] = zigzagPoint
		end

		path[#path] = endPos

		return path
	end
	
	-- Get the top down size of a part or model no matter its orientation
	function Math.GetWorldHeightY(instance: Instance): number
		if instance:IsA('BasePart') then
			local right = instance.CFrame.RightVector * instance.Size.X
			local up = instance.CFrame.UpVector * instance.Size.Y
			local back = instance.CFrame.LookVector * instance.Size.Z

			return math.abs(right.Y) + math.abs(up.Y) + math.abs(back.Y)
		elseif instance:IsA('Model') then
			local cf, sz = instance:GetBoundingBox()
			local half = sz / 2

			local corners = {
				Vector3.new(-half.X, -half.Y, -half.Z),
				Vector3.new(-half.X, -half.Y,  half.Z),
				Vector3.new(-half.X,  half.Y, -half.Z),
				Vector3.new(-half.X,  half.Y,  half.Z),
				Vector3.new( half.X, -half.Y, -half.Z),
				Vector3.new( half.X, -half.Y,  half.Z),
				Vector3.new( half.X,  half.Y, -half.Z),
				Vector3.new( half.X,  half.Y,  half.Z),
			}

			local minY = math.huge
			local maxY = -math.huge
			for _, corner in corners do
				local worldPos = cf:PointToWorldSpace(corner)
				minY = math.min(minY, worldPos.Y)
				maxY = math.max(maxY, worldPos.Y)
			end

			return maxY - minY
		end

		return 0
	end

end

-- Physics
do
	-- Checks if `target` is within a field-of-view and range from `obj`
	function Math.IsInFront(obj: BasePart, target: BasePart, angleLimit: number?, rangeLimit: number?): boolean
		angleLimit = angleLimit or 180
		rangeLimit =  rangeLimit or math.huge

		local Position = target.Position - obj.Position
		local look = obj.CFrame.LookVector
		local direction = Position.Unit
		local angle = math.deg(math.acos(look:Dot(direction)))

		if look:Dot(Position.Unit) > 0 and Position.Magnitude <= rangeLimit and angle < angleLimit then
			return true
		end

		return false
	end

	function Math.IsCenterInRegion(pt: CFrame, cf: CFrame, sz: Vector3): boolean
		-- CFrame.fromMatrix constructs a CFrame from column vectors
		local encodedOBB = CFrame.fromMatrix(
			cf.Position,
			cf.XVector/sz.X,
			cf.YVector/sz.Y,
			cf.ZVector/sz.Z
		):inverse() * pt

		return math.abs(encodedOBB.X) <= .5
			and math.abs(encodedOBB.Y) <= .5
			and math.abs(encodedOBB.Z) <= .5
	end

	-- Determines if a point lies within a horizontal circle (ignores Y)
	function Math.IsPointInCircle(point: Vector3, cc: Vector3, radius: number): boolean
		local flatPoint = Vector3.new(point.X, 0, point.Z)
		local flatCenter = Vector3.new(cc.X, 0, cc.Z)

		local distance = (flatPoint - flatCenter).Magnitude
		return distance <= radius
	end
	
	do -- https://devforum.roblox.com/t/using-a-beam-to-model-projectile-motion/1302237
		-- Returns Tuple:  isInRange(boolean), lauchAngle(in Radians)
		function Math.ComputeLaunchAngle(horizontalDistance: number, distanceY: number, intialSpeed: number, g: number) -- gravity must be (+)number
			local distanceTimesG = g*horizontalDistance -- Micro-optimizations
			local initialSpeedSquared = intialSpeed^2

			local inRoot = initialSpeedSquared^2 - (g*((distanceTimesG*horizontalDistance)+(2*distanceY*initialSpeedSquared)))
			if inRoot <= 0 then
				return false, 0.25 * math.pi -- Returns false as it is out of range
				-- Makes launch angle 45˚ for ranges past max Range
			end
			local root = math.sqrt(inRoot)
			local inATan1 = (initialSpeedSquared - root) / distanceTimesG
			local inATan2 = (initialSpeedSquared + root) / distanceTimesG
			local answerAngle1 = math.atan(inATan1) -- When optimal launch angle is lofted
			local answerAngle2 = math.atan(inATan2) -- When optimal launch angle is 'direct'
			-- You might be able to change some things and force the launch angle to be lofted at times for certain circumstance
			-- For example, shooting a basketball might require a more lofted trajectory, although EgoMoose's tutorial might be better for that
			if answerAngle1 < answerAngle2 then -- I've honestly never seen it be answer2, I can't figure out the case when it would be. I might remove it at some point
				return true, answerAngle1 -- Returns true as it is in range, same with below
			else
				return true, answerAngle2
			end
		end
		
		function Math.ComputeLaunchVelocity(distanceVector: Vector3, initialSpeed: number, g: number, allowOutOfRange: boolean?) -- gravity: (+)number
			local horizontalDistanceVector = Vector3.new(distanceVector.X, 0, distanceVector.Z)
			local horizontalDistance = horizontalDistanceVector.Magnitude

			local isInRange, launchAngle = Math.ComputeLaunchAngle(horizontalDistance, distanceVector.Y, initialSpeed, g)
			if not isInRange and not allowOutOfRange then return end

			local horizontaldirectionUnit = horizontalDistanceVector.Unit
			local vy = math.sin(launchAngle)
			local xz = math.cos(launchAngle)
			local vx = horizontaldirectionUnit.X * xz
			local vz = horizontaldirectionUnit.Z * xz

			return Vector3.new(vx*initialSpeed, vy*initialSpeed, vz*initialSpeed)
		end
		
		function Math.ComputeLaunchVelocityBeam(distanceVector: Vector3, initialSpeed: number, g: number, allowOutOfRange: boolean?) -- gravity: (+)number
			local distanceY = distanceVector.Y
			local horizontalDistanceVector = Vector3.new(distanceVector.X, 0, distanceVector.Z)
			local horizontalDistance = horizontalDistanceVector.Magnitude

			local isInRange, launchAngle = Math.ComputeLaunchAngle(horizontalDistance, distanceY, initialSpeed, g)
			if not isInRange and not allowOutOfRange then return end

			local horizontaldirectionUnit = horizontalDistanceVector.Unit
			local vy = math.sin(launchAngle)
			local xz = math.cos(launchAngle)
			local vx = horizontaldirectionUnit.X * xz
			local vz = horizontaldirectionUnit.Z * xz

			-- Just for beaming:
			local v0sin = vy * initialSpeed
			local horizontalRangeHalf = ((initialSpeed^2)/g * (math.sin(2*launchAngle)))/2

			local flightTime
			if horizontalRangeHalf <= horizontalDistance then
				flightTime = ((v0sin+(math.sqrt(v0sin^2+(2*-g*((distanceY))))))/g)
			else          
				flightTime = ((v0sin-(math.sqrt(v0sin^2+(2*-g*((distanceY))))))/g)
			end
			--

			return Vector3.new(vx*initialSpeed, vy*initialSpeed, vz*initialSpeed), flightTime -- flightTime is used to beam
		end
		
		-- v0: initialVelocity(Vec3),   x0: initialPosition(Vec3),   t1: flightTime((+)number),   g: gravity((+)number)
		function Math.BeamProjectile(v0: Vector3, x0: Vector3, t1: number, g: number)
			local g = Vector3.new(0, -g, 0)
			-- calculate the bezier points
			local c = 0.5*0.5*0.5
			local p3 = 0.5*g*t1*t1 + v0*t1 + x0
			local p2 = p3 - (g*t1*t1 + v0*t1)/3
			local p1 = (c*g*t1*t1 + 0.5*v0*t1 + x0 - c*(x0+p3))/(3*c) - p2

			-- the curve sizes
			local curve0 = (p1 - x0).magnitude
			local curve1 = (p2 - p3).magnitude

			-- build the world CFrames for the attachments
			local b = (x0 - p3).unit
			local r1 = (p1 - x0).unit
			local u1 = r1:Cross(b).unit
			local r2 = (p2 - p3).unit
			local u2 = r2:Cross(b).unit
			b = u1:Cross(r1).unit

			local cf0 = CFrame.new(
				(x0.x), (x0.y), (x0.z),
				r1.x, u1.x, b.x,
				r1.y, u1.y, b.y,
				r1.z, u1.z, b.z)

			local cf1 = CFrame.new(
				(p3.x), (p3.y), (p3.z),
				r2.x, u2.x, b.x,
				r2.y, u2.y, b.y,
				r2.z, u2.z, b.z)

			return curve0, -curve1, cf0, cf1
		end
	end
end

-- Statistics
do
	function Math.Mean(t: {number}): number
		local sum = 0
		for _, v in t do
			sum += v
		end
		return sum / #t
	end

	function Math.Median(t: {number}): number
		local sorted = table.clone(t)
		table.sort(sorted)
		local n = #sorted
		if n % 2 == 1 then
			return sorted[math.ceil(n / 2)]
		else
			return (sorted[n/2] + sorted[n/2 + 1]) / 2
		end
	end

	function Math.StdDev(t: {number}): number
		local mean = Math.Mean(t)
		local variance = 0
		for _, v in t do
			variance += (v - mean) ^ 2
		end
		return math.sqrt(variance / #t)
	end

	function Math.Range(values: {number}): number
		local minVal, maxVal = math.huge, -math.huge
		for _, v in values do
			if v < minVal then minVal = v end
			if v > maxVal then maxVal = v end
		end
		return maxVal - minVal
	end


	function Math.Mode(values: {number}): number?
		local counts = {}
		local maxCount = 0
		local mode = nil

		for _, v in values do
			counts[v] = (counts[v] or 0) + 1
			if counts[v] > maxCount then
				maxCount = counts[v]
				mode = v
			end
		end

		return mode
	end

	function Math.Variance(values: {number}): number
		local mean = Math.Mean(values)
		local variance = 0
		for _, v in values do
			variance += (v - mean) ^ 2
		end
		return variance / #values
	end

	function Math.ZScore(value: number, values: {number}): number
		local mean = Math.Mean(values)
		local stdDev = math.sqrt(Math.Variance(values))
		return (value - mean) / stdDev
	end
	
	local RANDOMIZER = Random.new()
	local SCALE = 10_000_000_000
	function Math.RNG(input: {[any]: number}, luck: number?, default: any?)
		local Portions = {}
		local total = 0
		local luckPercent = (luck or 0) / 100

		local maxWeight = 0
		for _, v in input do
			if v > maxWeight then maxWeight = v end
		end

		local adjustedWeights = {}
		for k, v in input do
			local rarityFactor = (maxWeight / v)
			local multiplier = 1 + (rarityFactor - 1) * luckPercent
			local adjusted = v * multiplier
			adjustedWeights[k] = adjusted
			total += adjusted
		end

		local cumulativeNormalized = 0
		for k, adjusted in adjustedWeights do
			local normalized = (adjusted / total) * SCALE
			table.insert(Portions, {
				Lower = cumulativeNormalized,
				Upper = cumulativeNormalized + normalized,
				Result = k
			})
			cumulativeNormalized += normalized
		end

		local rand = RANDOMIZER:NextNumber(0, SCALE)
		for _, v in Portions do
			if v.Lower <= rand and rand <= v.Upper then
				return v.Result
			end
		end

		return default or Portions[1].Result
	end
end

-- Functions
do
	function Math.Sigmoid(z: number): number
		return 1 / (1 + Math.e ^ -z)
	end

	function Math.Quadratic(a: number, b: number, c: number, t: number): number
		return a * (t ^ 2) + (b * t) + c
	end
end

--  Algorithms
do
	function Math.BinaryExponentiation(a: number, b: number, mod: number?)
		mod = mod or math.huge
		
		local res = 1
		a = a % mod
		while b > 0 do
			if bit32.band(b, 1) ~= 0 then
				res = (res * a) % mod
			end
			a = (a * a) % mod
			b = bit32.rshift(b, 1)
		end
		return res
	end
	
	function Math.FindNearestLowerAndHigherNumbers(x: number, t: {number}): (number?, number?)
		local lower, higher

		for _, num in t do
			if num < x and (lower == nil or num > lower) then
				lower = num
			elseif num > x and (higher == nil or num < higher) then
				higher = num
			end
		end

		return lower, higher
	end
end

-- Number theory
do
	--[[function Math.gcd(a: number, b: number)
		while b ~= 0 do
			a %= b
			
			a = a + b
			b = a - b
			a = a - b
		end
		return a
	end]]

	-- Binary gcd
	function Math.gcd(a: number, b: number): number
		if a == 0 or b == 0 then return bit32.bor(a, b) end

		local shift = Math.CountTrailingZeros(bit32.bor(a, b))
		a = bit32.rshift(a, Math.CountTrailingZeros(a))

		repeat
			b = bit32.rshift(b, Math.CountTrailingZeros(b))
			if a > b then
				a, b = b, a
			end
			b -= a
		until b == 0

		return bit32.lshift(a, shift)
	end

	function Math.lcm(a: number, b: number): number
		return a / Math.gcd(a, b) * b
	end

	function Math.SumOfConsecutiveNumbers(step: number, upTo: number): number
		local terms = upTo // step
		local lastTerm = terms * step
		return terms * (step + lastTerm)/2
	end

	function Math.SumOfConsecutiveSquares(step: number, upTo: number): number
		local terms = upTo // step
		return step * step * terms * (terms + 1) * (2 * terms + 1) // 6
	end

	function Math.GetExponentInPrimeFactorization(n: number, prime: number): number
		local exp = 0
		while n % prime == 0 do
			exp += 1
			n //= prime
		end
		return exp
	end
	
	function Math.PrimeFactors(n: number): { [number]: number }
		local factors = {}
		local i = 2
		while i * i <= n do
			while n % i == 0 do
				factors[i] = (factors[i] or 0) + 1
				n //= i
			end
			
			i += 1
		end
		
		if n > 1 then
			factors[n] = (factors[n] or 0) + 1
		end
		
		return factors
	end

	do
		function Math.Totient(n: number): number
			local result = n

			local i = 2
			while i * i <= n do
				if n % i == 0 then
					while n % i == 0 do
						n /= i
					end

					result -= result / i
				end

				i += 1
			end

			if n > 1 then
				result -= result / n
			end

			return result
		end

		function Math.TotientsToN(n: number): {number}
			local phi = table.create(n)

			for i = 1, n do
				phi[i] = i
			end

			for i = 2, n do
				if phi[i] == i then
					for j = i + i, n, i do
						phi[j] -= phi[j] // i
					end
				end
			end

			return phi
		end

		-- https://projecteuler.net/overview=0351
		-- O(n^(2/3) * (log log n)^(1/3))
		function Math.TotientSum(n: number): number
			local L = math.floor((n / math.log(math.log(n))) ^ (2 / 3))

			-- Modified Sieve of Eratosthenes for Euler's Totient function
			local sieve = table.create(L)
			for i = 1, L do
				sieve[i] = i
			end

			for p = 2, L do
				if sieve[p] == p then
					for k = p, L, p do
						sieve[k] -= sieve[k] // p
					end
				end

				if p > 1 then
					sieve[p] += sieve[p - 1]
				end
			end

			-- Main loop
			local maxX = n // L
			local bigV = table.create(maxX, 0)

			for x = maxX, 1, -1 do
				local k = n // x
				local res = k * (k + 1) // 2

				-- First inner loop
				local sqrtk = math.floor(math.sqrt(k))
				for g = 2, sqrtk do
					local kg = k // g
					if kg <= L then
						res -= sieve[kg]
					else
						res -= bigV[x * g]
					end
				end

				-- Second inner loop
				for z = 1, sqrtk do
					local q = k // z
					if z ~= q then
						local diff = q - (k // (z + 1))
						res -= diff * sieve[z]
					end
				end

				bigV[x] = res
			end

			return bigV[1]
		end
	end

	do
		function Math.NumberOfDivisors(num: number): number
			local total = 1

			local i = 2
			while i * i <= num do
				if num % i == 0 then
					local e = 0
					repeat
						e += 1
						num /= i
					until num % i ~= 0

					total *= e + 1
				end

				i += 1
			end

			if num > 1 then
				total *= 2
			end

			return total
		end

		function Math.SumOfDivisors(num): number
			local total = 1

			local i = 2
			while i * i <= num do
				if num % i == 0 then
					local exp = 0
					repeat
						exp += 1
						num //= i
					until num % i ~= 0

					local sum, pow = 0, 1
					repeat
						sum += pow
						pow *= i
						exp -= 1
					until exp < 0

					total *= sum
				end

				i += 1
			end

			if num > 1 then
				total *= (1 + num)
			end

			return total
		end

		function Math.UniquePrimeDivisorsCountSieve(n: number): {number}
			local sieve = table.create(n, 0)

			for p = 2, n do
				if sieve[p] ~= 0 then continue end

				for mult = p, n, p do
					sieve[mult] += Math.GetExponentInPrimeFactorization(mult, p)
				end
			end

			return sieve
		end
	end

	do
		function Math.IsPrime(n: number): boolean
			if n == 2 or n == 3 then return true end
			if n % 2 == 0 or n % 3 == 0 then return false end

			local i = 5
			while i * i <= n do
				if n % i == 0 or n % (i + 2) == 0 then return false end
				i += 6
			end

			return true
		end

		function Math.SegmentedPrimeSieve(L: number, R: number): {boolean}
			local lim = math.floor(math.sqrt(R))

			local mark = table.create(lim + 1, false)
			local primes = {}

			for i = 2, lim do
				if not mark[i] then
					table.insert(primes, i)
					for j = i * i, lim, i do
						mark[j] = true
					end
				end
			end

			--

			local isPrime = table.create(R - L + 1, true)
			for _, i in primes do
				for j = math.max(i * i, (L + i - 1) / i * i), R, i do
					isPrime[j - L + 1] = false
				end
			end

			if L == 1 then
				isPrime[1] = false
			end

			return isPrime
		end
	end

	-- Derived from: P(n) = n(3n - 1)/2
	function Math.IsPentagonal(n: number): boolean
		local k = (1 + math.sqrt(1 + 24 * n)) / 6
		return math.floor(k + .5) == k
	end
	
	-- Derived from: T(n) = n(n + 1)/2
	function Math.IsTriangular(n: number): boolean
		local x = (math.sqrt(8 * n + 1) - 1) // 2
		return x * (x + 1) // 2 == n
	end

	function Math.GenerateSquareNumbers(n: number): {number}
		local sieve = table.create(n, 0)

		local currentOdd = 1
		local currentSum = 1
		for i = 1, n do
			sieve[i] = currentSum

			currentOdd += 2
			currentSum += currentOdd
		end

		return sieve
	end
	
	function Math.IsPerfectSquare(n: number): boolean
		if n < 0 then return false end
		local root = math.floor(math.sqrt(n) + .5)
		return root * root == n
	end
end

-- Bit manipulation
do
	function Math.IsBitSet(n: number, x: number): boolean
		return bit32.btest(bit32.band(n, bit32.lshift(1, x)))
	end

	function Math.SetBit(n: number, x: number): number
		return bit32.bor(n, bit32.lshift(1, x))
	end

	function Math.FlipBit(n: number, x: number): number
		return bit32.bxor(n, bit32.lshift(1, x))
	end

	function Math.ClearBit(n: number, x: number): number
		return bit32.band(n, bit32.bnot(bit32.lshift(1, x)))
	end

	function Math.IsPowerOfTwo(n: number): boolean
		return n > 0 and bit32.band(n, n - 1) == 0
	end

	function Math.CountSetBits(n: number): number
		local count = 0
		while n ~= 0 do
			n = bit32.band(n, n - 1)
			count += 1
		end
		return count
	end

	function Math.CountTrailingZeros(x: number): number
		if x == 0 then return 0 end

		local n = 0
		while bit32.band(x, 1) == 0 do
			x = bit32.rshift(x, 1)
			n += 1
		end
		return n
	end

	-- Returns the number of bits needed to represent n
	function Math.BitLength(n: number): number
		if n <= 0 then return 0 end
		return math.floor(math.log(n, 2)) + 1
	end
end

-- Gui
do
	function Math.ScaleToOffset(x: number, y: number, parentFrame: GuiObject?): (number, number)
		local size = parentFrame and parentFrame.AbsoluteSize or workspace.Camera.ViewportSize
		return x * size.X, y * size.Y
	end

	function Math.OffsetToScale(x: number, y: number, parentFrame: GuiObject?): (number, number)
		local size = parentFrame and parentFrame.AbsoluteSize or workspace.Camera.ViewportSize
		return x / size.X, y / size.Y
	end

	function Math.GetGuiCorners(gui: GuiObject): { topleft: Vector2, bottomleft: Vector2, topright: Vector2, bottomright: Vector2,  }
		local pos = gui.AbsolutePosition
		local size = gui.AbsoluteSize
		local rotation = math.rad(gui.Rotation)

		local cornerAngle = math.atan2(size.Y, size.X)
		
		local a = pos + size/2-math.sqrt((size.X/2)^2 + (size.Y/2)^2) * Vector2.new(math.cos(rotation + cornerAngle), math.sin(rotation + cornerAngle))
		local b = pos + size/2-math.sqrt((size.X/2)^2 + (size.Y/2)^2) * Vector2.new(math.cos(rotation - cornerAngle), math.sin(rotation - cornerAngle))
		local c = pos + size/2+math.sqrt((size.X/2)^2 + (size.Y/2)^2) * Vector2.new(math.cos(rotation + cornerAngle), math.sin(rotation + cornerAngle))
		local d = pos + size/2+math.sqrt((size.X/2)^2 + (size.Y/2)^2) * Vector2.new(math.cos(rotation - cornerAngle), math.sin(rotation - cornerAngle))

		return { 
			topleft = a,
			bottomleft = b,
			topright = d,
			bottomright = c
		}
	end

	function Math.IsGui0InsideGui1(gui0: GuiObject, gui1: GuiObject): boolean
		if gui0.AbsoluteSize.X > gui1.AbsoluteSize.X or gui0.AbsoluteSizeY > gui1.AbsoluteSize.Y then return false end

		local corners0, corners1 = Math.GetGuiCorners(gui0), Math.GetGuiCorners(gui1)

		local cornersInside = 0

		for _, corner in corners0 do
			if Math.IsInRange(corner.x, corners1[1].X, corners1[4].X) and Math.IsInRange(corner.y, corners1[1].Y, corners1[2].Y) then
				cornersInside += 1
			end
		end

		return cornersInside == 4
	end
	
	-- Checks if a ray from a point intersects a GUI edge (for polygon hit tests)
	function Math.DoesRayFromPointIntersectGuiEdge(p: Vector2, edgeStartPos: Vector2, edgeEndPos: Vector2): boolean
		local x1, y1 = edgeStartPos.X, edgeStartPos.Y
		local x2, y2 = edgeEndPos.X, edgeEndPos.Y

		local x3, y3 = p.x, p.y
		local x4, y4 = p.x + Math.int32, p.y

		local den = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)

		if den == 0 then return false end

		local t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / den
		local u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / den

		if t and u and t > 0 and t < 1 and u > 0 then
			return true
		end

		return false
	end

	function Math.AreGuisColliding(guiObject0: GuiObject, guiObject1: GuiObject): boolean
		local ap1 = guiObject0.AbsolutePosition
		local as1 = guiObject0.AbsoluteSize
		local sum = ap1 + as1

		local ap2 = guiObject1.AbsolutePosition
		local as2 = guiObject1.AbsoluteSize
		local sum2 = ap2 + as2

		local corners0 = Math.GetGuiCorners(guiObject0)
		local corners1 = Math.GetGuiCorners(guiObject1)

		local edges = {
			{
				a = corners1.topleft,
				b = corners1.bottomleft
			},
			{
				a = corners1.topleft,
				b = corners1.topright
			},
			{
				a = corners1.bottomleft,
				b = corners1.bottomright
			},
			{
				a = corners1.topright,
				b = corners1.bottomright
			}
		}

		local collisions = 0

		for _, corner in corners0 do
			for _, edge in edges do
				if Math.DoesRayFromPointIntersectGuiEdge(corner, edge.a, edge.b) then
					collisions += 1
				end
			end
		end

		if collisions % 2 ~= 0 then
			return true
		end

		if (ap1.x < sum2.x and sum.x > ap2.x) and (ap1.y < sum2.y and sum.y > ap2.y) then
			return true
		end

		return false
	end
	
	function Math.DrawLine(origin: Vector2, endpoint: Vector2, parent: Instance, thickness: number, frameToUse: Frame?, image: string?): Frame | ImageLabel
		local line do
			local hyp = (endpoint - origin).Magnitude
			
			line = frameToUse or (image and Instance.new('ImageLabel') or Instance.new('Frame'))
			line.Name = 'ConstraintLine'
			line.AnchorPoint = Vector2.new(.5, .5)
			line.Size = UDim2.new(0, hyp, 0, (thickness or 5) + (image and 15 or 0))
			line.BackgroundTransparency = image and 1 or 0
			line.BorderSizePixel = 0

			if image then
				line.Image = image
			end

			line.Parent = parent
		end
		
		local mid = (origin + endpoint)/2
		local theta = math.atan2((endpoint-mid).Y, (endpoint-mid).X)

		-- Apply rotation and update position
		line.Position = UDim2.fromOffset(mid.X, mid.Y + GuiService.TopbarInset.Height)
		line.Rotation = math.deg(theta)

		return line
	end
end

-- Time
do
	function Math.GetDHMS(s: number, floor: boolean?)
		local d, h, m, s = s/86400, (s/(60*60))%24, s/60%60, s%60
		
		local f = function(n: number)return floor and math.floor(n) or n end
		return f(d), f(h), f(m), f(s)
	end
end


-- Number formatting
do
	function Math.Comma(n: number): string
		local left, num, decimal = string.match(n, '^([^%d]*%d)(%d*)(.-)$')
		return left .. (num:reverse():gsub('(%d%d%d)', '%1,'):reverse()) .. decimal
	end
end


return Math