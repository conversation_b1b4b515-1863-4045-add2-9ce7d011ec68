local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local ServerScriptService = game:GetService('ServerScriptService')

local Server = require(ServerScriptService.Server)

local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Signal = require(ReplicatedStorage.SharedModules.Signal)
local Packets = require(ReplicatedStorage.Data.Packets)
local DailyRewardsData = require(ReplicatedStorage.Data.DailyRewards)
local Math = require(ReplicatedStorage.SharedModules.Math)

local TimeService = {
	ResetDaily = Signal.new(),
	ResetWeekly = Signal.new(),
	ResetHourly = Signal.new(),
	ResetMonthly = Signal.new()
}


local function dailyRewardsHandler()
	local DataService = Server.GetService('DataService')
	
	Packets.Time.ClaimDailyReward.listen(function(dayNum: number, plr: Player)
		local profile = DataService.GetProfile(plr)
		
		local normalizedStreak = (profile.DailyStreak - 1) % 7 + 1
		dayNum = (dayNum - 1) % 7 + 1
		
		if profile.DailyRewardsClaimed[dayNum] or normalizedStreak < dayNum then return end
		
		local thisRewardInfo = DailyRewardsData[dayNum]
		
		profile.DailyRewardsClaimed[dayNum] = true
		
		Server.GetService(thisRewardInfo.Reward.Service)[thisRewardInfo.Reward.Function](plr, table.unpack(thisRewardInfo.Reward.Args))
	end)
	
	Packets.Time.GetDailyRewardInfo.listen(function(_, plr: Player)
		local profile = DataService.GetProfile(plr)
		
		Packets.Time.GetDailyRewardInfo.sendTo({
			Streak = (profile.DailyStreak - 1) % 7 + 1,
			Claimed = profile.DailyRewardsClaimed,
			TimeUntilNextDay = TimeService.GetTimeLeftUntilNextDay(plr)
		}, plr)
	end)
end


local function resetDaily(plr: Player)
	local DataService = Server.GetService('DataService')
	
	local profile = DataService.GetProfile(plr)

	local t = os.time() + profile.UTCOffset
	if ClassExtension.time.CreateDailyHash(t) ~= ClassExtension.time.CreateDailyHash(profile.LastDailyReset) then
		if ClassExtension.time.CreateDailyHash(profile.LastDailyReset) == ClassExtension.time.CreateDailyHash(t - (60*60*24)) then
			profile.DailyStreak += 1
			
			local normalizedStreak = (profile.DailyStreak - 1) % 7 + 1
			if normalizedStreak == 1 then
				table.clear(profile.DailyRewardsClaimed)
			end
		else
			profile.DailyStreak = 1
		end
		
		profile.LastDailyReset = t
		TimeService.ResetDaily:Fire(plr)
	end
end


local function resetWeekly(plr: Player)
	local DataService = Server.GetService('DataService')
	
	local profile = DataService.GetProfile(plr)

	local t = os.time() + profile.UTCOffset
	if ClassExtension.time.CreateWeeklyHash(t) ~= ClassExtension.time.CreateWeeklyHash(profile.LastWeeklyReset) then
		profile.LastWeeklyReset = t
		TimeService.ResetWeekly:Fire(plr)
	end
end


local function resetHourly()
	if os.date('%M', os.time()) == '00' then
		TimeService.ResetHourly:Fire()
	end
end


local function resetMonthly()
	if os.date('%d%H%M', os.time()) == '010000' then
		TimeService.ResetMonthly:Fire()
	end
end


function TimeService.GetTimeLeftUntilNextDay(plr: Player)
	local DataService = Server.GetService('DataService')
	
	local profile = DataService.GetProfile(plr)
	
	local now = os.time() + profile.UTCOffset
	
	local targetDate = os.date('!*t', now)
	targetDate.hour = 0
	targetDate.min = 0
	targetDate.sec = 0
	targetDate.day += 1
	
	return os.time(targetDate) - now
end


function TimeService.GetTimeLeftUntilNextHour(plr: Player)
	local DataService = Server.GetService('DataService')
	
	local profile = DataService.GetProfile(plr)

	local now = os.time() + profile.UTCOffset

	local targetDate = os.date('!*t', now)
	targetDate.hour += 1
	targetDate.min = math.floor(profile.UTCOffset/60%60)
	targetDate.sec = 0

	return os.time(targetDate) - now
end


function TimeService.GetTimeLeftUntilNextWeek(plr: Player)
	local DataService = Server.GetService('DataService')
	
	local profile = DataService.GetProfile(plr)

	local now = os.time() + profile.UTCOffset

	local targetDate = os.date('!*t', now)
	targetDate.hour = 0
	targetDate.min = 0
	targetDate.sec = 0
	
	local daysToMonday = (9 - targetDate.wday) % 7
	if daysToMonday == 0 then
		daysToMonday = 7
	end
	
	targetDate.day = targetDate.day + daysToMonday

	return os.time(targetDate) - now
end


function TimeService.GetTimeLeftUntilNextMonth()
	local now = os.time()

	local targetDate = os.date('!*t', now)

	targetDate.day = 1
	targetDate.hour = 0
	targetDate.min = 0
	targetDate.sec = 0

	if targetDate.month == 12 then
		targetDate.month = 1
		targetDate.year += 1
	else
		targetDate.month += 1
	end

	return os.time(targetDate) - now
end



function TimeService._PlayerAdded(plr: Player)
	local DataService = Server.GetService('DataService')
	
	local OtherData = DataService.Get(plr, 'OtherData')

	if OtherData.FirstJoin == 0 then
		OtherData.FirstJoin = os.time()
	end

	OtherData.LastJoin = os.time()
	
	DataService.Increment(plr, 'GameVisits', 1)

	task.defer(resetDaily, plr)
	task.defer(resetWeekly, plr)

	while plr:IsDescendantOf(Players) do
		DataService.Increment(plr, 'TimePlayed', 1)
		task.wait(1)
	end
end


function TimeService._Start()
	local DataService = Server.GetService('DataService')
	
	task.spawn(function()
		-- tries reset every 15 minutes
		while true do
			local now = os.time()
			local jitter = math.random(5, 25)/10
			task.wait((math.floor(now/(60*15) + 1) * (60*15) - now) + jitter) -- time remaining until the next 15-minute interval (based on EST)

			for _, v in Players:GetPlayers() do
				task.spawn(resetDaily, v)
				task.spawn(resetWeekly, v)
			end
			
			task.spawn(resetHourly)
			task.spawn(resetMonthly)
		end
	end)
	
	task.spawn(function()
		while true do
			workspace:SetAttribute('time', os.time())
			task.wait(1)
		end
	end)
	
	Packets.Time.GetPlayerTime.listen(function(data: { t: number, timeSent: number }, plr: Player)
		local latency = (workspace:GetServerTimeNow() - data.timeSent)/1000
		local TimeOffset = -Math.RoundToNearestMultiple(os.time(os.date('!*t')) - (data.t + latency), 60*15)

		DataService.Set(plr, 'UTCOffset', TimeOffset)
	end)
	
	TimeService.ResetDaily:Connect(function(plr: Player)
		print(plr)
	end)
	
	task.defer(dailyRewardsHandler)
end


return TimeService