--[=[
	Author: <PERSON><PERSON><PERSON> (Syveric)
	Daily rewards data
]=]

return table.freeze{
	[1] = {
		Name = 'Test',
		ImageFooter = '50x',
		Image = 'rbxassetid://122220601991100',
		
		Reward = {
			Service = 'CurrencyService',
			Function = 'AddGems',
			Args = {50, 'DailyReward'}
		}
	},
	
	[2] = {
		Name = 'Test',
		ImageFooter = '50x',
		Image = 'rbxassetid://122220601991100',

		Reward = {
			Service = 'CurrencyService',
			Function = 'AddGems',
			Args = {50, 'DailyReward'}
		}
	},
	
	[3] = {
		Name = 'Test',
		ImageFooter = '50x',
		Image = 'rbxassetid://122220601991100',

		Reward = {
			Service = 'CurrencyService',
			Function = 'AddGems',
			Args = {50, 'DailyReward'}
		}
	},
	
	[4] = {
		Name = 'Test',
		ImageFooter = '50x',
		Image = 'rbxassetid://122220601991100',

		Reward = {
			Service = 'CurrencyService',
			Function = 'AddGems',
			Args = {50, 'DailyReward'}
		}
	},
	
	[5] = {
		Name = 'Test',
		ImageFooter = '50x',
		Image = 'rbxassetid://122220601991100',

		Reward = {
			Service = 'CurrencyService',
			Function = 'AddGems',
			Args = {50, 'DailyReward'}
		}
	},
	
	[6] = {
		Name = 'Test',
		ImageFooter = '60x',
		Image = 'rbxassetid://122220601991100',

		Reward = {
			Service = 'CurrencyService',
			Function = 'AddGems',
			Args = {50, 'DailyReward'}
		}
	},
	
	[7] = {
		Name = 'Test',
		ImageFooter = '70x',
		Image = 'rbxassetid://122220601991100',

		Reward = {
			Service = 'CurrencyService',
			Function = 'AddGems',
			Args = {70, 'DailyReward'}
		}
	},
}