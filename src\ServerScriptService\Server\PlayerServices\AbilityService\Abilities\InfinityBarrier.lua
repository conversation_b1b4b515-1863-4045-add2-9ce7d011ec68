local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local TweenService = game:GetService('TweenService')
local SoundService = game:GetService('SoundService')
local Debris = game:GetService('Debris')
local CollectionService = game:GetService('CollectionService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local CookingService: typeof(require(ServerScriptService.Server.GameServices.CookingService)) = Server.GetService('CookingService')

local InfinityBarrier = {
	COOLDOWN = 5,
	EFFECT_TIME = 6,
	
	Cooldowns = {} :: { [Player]: number },
	AbilityJanitors = {} :: { [Player]: Janitor.Janitor }
}


function InfinityBarrier.GetCooldownTimeLeft(plr: Player): number
	if InfinityBarrier.Cooldowns[plr] then
		return InfinityBarrier.COOLDOWN - (os.clock() - InfinityBarrier.Cooldowns[plr])
	end
	return 0
end


function InfinityBarrier.CanUse(plr: Player): boolean
	if InfinityBarrier.Cooldowns[plr] then
		return os.clock() - InfinityBarrier.Cooldowns[plr] > InfinityBarrier.COOLDOWN
	end
	
	return true
end


function InfinityBarrier.IsInUse(plr: Player): boolean
	if InfinityBarrier.AbilityJanitors[plr] and InfinityBarrier.AbilityJanitors[plr]:Get('Active') then
		return true
	end

	return false
end


function InfinityBarrier.UseAbility(plr: Player): boolean
	if not InfinityBarrier.CanUse(plr) or InfinityBarrier.IsInUse(plr) then return false end

	InfinityBarrier.Cooldowns[plr] = os.clock()

	task.delay(InfinityBarrier.COOLDOWN, function()
		InfinityBarrier.Cooldowns[plr] = nil
	end)

	if not InfinityBarrier.AbilityJanitors[plr] then
		InfinityBarrier.AbilityJanitors[plr] = Janitor.new()
	end
	
	InfinityBarrier.AbilityJanitors[plr]:Add(math.random, nil, 'Active')
	
	ClassExtension.Player.DisableMovement(plr, true)
	plr:SetAttribute(script.Name, true)
	
	local InfBarrierTrack: AnimationTrack = Animations:PlayTrack(plr, 'InfinityBarrier')

	SoundService.SFX.Abilities.InfinityBarrier:Play()

	do
		local CharFX = {} :: { ParticleEmitter }
		for _, bodyPart in script.CharFX:GetChildren() do
			for _, v in bodyPart:GetChildren() do
				local ThisFX = v:Clone()
				ThisFX.Parent = plr.Character[bodyPart.Name]
				table.insert(CharFX, ThisFX)
			end
		end

		InfinityBarrier.AbilityJanitors[plr]:Add(function()
			for _, v in CharFX do
				VFXFunctions.DisableDescendants(v)
				Debris:AddItem(v, VFXFunctions.GetHighestWaitTime(v))
			end
		end)
	end
	
	InfinityBarrier.AbilityJanitors[plr]:Add(task.delay(InfinityBarrier.EFFECT_TIME, function()
		InfinityBarrier.AbilityJanitors[plr]:Cleanup()
	end))
	
	InfinityBarrier.AbilityJanitors[plr]:Add(InfBarrierTrack.Ended:Connect(function()
		ClassExtension.Player.DisableMovement(plr, false)
		InfinityBarrier.AbilityJanitors[plr]:Remove('Active')
	end))

	InfinityBarrier.AbilityJanitors[plr]:Add(function()
		plr:SetAttribute(script.Name, nil)
		InfBarrierTrack:Stop()
		ClassExtension.Player.DisableMovement(plr, false)

		Server.GetService('AbilityService').UpdatePassives(plr)
	end)
	
	return true
end


function InfinityBarrier.CancelAbility(plr: Player)
	if not InfinityBarrier.IsInUse(plr) then return end

	if InfinityBarrier.AbilityJanitors[plr] then
		InfinityBarrier.AbilityJanitors[plr]:Cleanup()
	end
end


return InfinityBarrier