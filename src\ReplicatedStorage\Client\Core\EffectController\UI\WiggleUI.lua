local RunService = game:GetService('RunService')

local doublePi = math.pi * 2

return function(gui: GuiObject, intensity: number?, len: number?, finalRotation: number?, cancelOld: boolean?)
	if gui:GetAttribute('Wiggling') then
		if cancelOld then
			gui:SetAttribute('CancelWiggle', true)
			gui:GetAttributeChangedSignal'CancelWiggle':Wait()
		else return end
	end
	
	gui:SetAttribute('Wiggling', true)
	
	intensity = intensity or 15
	len = len or 1

	local defaultRotation = finalRotation or gui:GetAttribute'DefaultRotation' or gui.Rotation

	gui:SetAttribute('DefaultRotation', defaultRotation)

	local start = os.clock()
	local c c = RunService.PreRender:Connect(function(dt: number)
		if gui:GetAttribute'CancelWiggle' then
			gui:SetAttribute('CancelWiggle', nil)
			return c:Disconnect()
		end
		
		local elapse = (os.clock() - start)/len
		gui.Rotation = intensity * math.sin(doublePi * (1 + elapse) * 6) / 4 ^ (5 * elapse) + defaultRotation
	end)
	
	task.delay(len, function()
		if c.Connected then
			c:Disconnect()
			gui.Rotation = defaultRotation
			gui:SetAttribute('Wiggling', nil)
			gui:SetAttribute('CancelWiggle', nil)
		end
	end)
end