local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local RunService = game:GetService('RunService')
local GamepadService = game:GetService('GamepadService')
local UserInputService = game:GetService('UserInputService')

local Client = require(ReplicatedStorage.Client)
local Janitor = require(ReplicatedStorage.SharedModules.Janitor)

local Packets = require(ReplicatedStorage.Data.Packets)
local InputController: typeof(require(script.Parent.Parent.Parent)) = Client.GetController('InputController')

local Fuga = {
	Key = Enum.KeyCode.V,

	GamepadKey = Enum.KeyCode.ButtonR2
}


function Fuga.KeyDown(second: true?)
	if InputController.Preferred == 'Gamepad' and second == nil then
		Fuga.Click()
		return
	end

	Fuga.Cleanup()
	
	if Fuga.IsOnCooldown() or not Fuga.CanUseAbilityAsCharacter() then return end

	local hitRp = RaycastParams.new()
	hitRp.FilterType = Enum.RaycastFilterType.Exclude
	hitRp.FilterDescendantsInstances = {workspace.Map.Collision_Parts}
	
	local hitRes = InputController.Inputs.Mouse.Hit(hitRp)
	if hitRes then
		Packets.Ability.UseAbility.send({
			AbilityName = script.Name,
			Args = {hitRes.Position}
		})
	end
end


function Fuga.KeyUp()
	
end


local ClickJanitor = Janitor.new()
function Fuga.Click()
	if ClickJanitor:Get('Click') then
		return Fuga.Cleanup()
	end
	
	if Fuga.IsOnCooldown() or not Fuga.CanUseAbilityAsCharacter() then return end
	
	ClickJanitor:Add(Players.LocalPlayer:GetMouse().Button1Down:Connect(function()
		task.wait()
		Fuga.KeyDown()
		Fuga.Cleanup()
	end), nil, 'Click')
	
	GamepadService:EnableGamepadCursor(nil)
	ClickJanitor:Add(UserInputService.InputBegan:Connect(function(input: InputObject, processed: boolean)
		if input.KeyCode == Enum.KeyCode.ButtonA then
			Fuga.KeyDown(true)
			Fuga.Cleanup()
		elseif input.KeyCode == Enum.KeyCode.ButtonB then
			Fuga.Cleanup()
		end
	end))
	
	ClickJanitor:Add(function()
		GamepadService:DisableGamepadCursor()
	end)
end


function Fuga.Cleanup()
	ClickJanitor:Cleanup()
end


function Fuga.IsOnCooldown()
	return Client.GetController'AbilityController'.IsAbilityOnCooldown(script.Name)
end


function Fuga.CanUseAbilityAsCharacter()
	return Client.GetController'AbilityController'.CanUseAbilityAsCharacter(script.Name)
end


function Fuga._Init()
	local follow = false
	Players.LocalPlayer:GetAttributeChangedSignal'FugaFollowMouse':Connect(function()
		follow = Players.LocalPlayer:GetAttribute'FugaFollowMouse'
	end)
	
	RunService.RenderStepped:Connect(function()
		if not follow then return end
		
		local hitRp = RaycastParams.new()
		hitRp.FilterType = Enum.RaycastFilterType.Exclude
		hitRp.FilterDescendantsInstances = {workspace.Map.Collision_Parts}
		local hitRes = InputController.Inputs.Mouse.Hit(hitRp)
		
		if hitRes then
			local lookAt = Vector3.new(hitRes.Position.X, Players.LocalPlayer.Character.PrimaryPart.CFrame.Y, hitRes.Position.Z)
			Players.LocalPlayer.Character.PrimaryPart.CFrame = CFrame.lookAt(Players.LocalPlayer.Character.PrimaryPart.Position, lookAt)
		end
	end)
end


return Fuga
