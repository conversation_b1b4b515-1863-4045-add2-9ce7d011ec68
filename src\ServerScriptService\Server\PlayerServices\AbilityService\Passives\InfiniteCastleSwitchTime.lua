local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local PassiveModule = {}


function PassiveModule.Get()
	if script:GetAttribute'Override' then
		return script:GetAttribute'Override'
	end
	
	local default = 30
	
	local act = Server.GetService('RoundService').Act
	if act and Client.Immutable.ACT_CONFIG[act] then
		default = Client.Immutable.ACT_CONFIG[act].InfiniteCastleSwitchTime
	end
	
	return default
end


function PassiveModule.Init()
	
end


return PassiveModule