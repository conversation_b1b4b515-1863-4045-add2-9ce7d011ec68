local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local RunService = game:GetService('RunService')

local Packets = require(ReplicatedStorage.Data.Packets)
local ItemsData = require(ReplicatedStorage.Data.Items)
local Math = require(ReplicatedStorage.SharedModules.Math)

local BoostController = {
	BoostsInfo = nil
}


local function refreshBoosts(data: {{ Name: string, TimeLeft: number }})
	local BoostsContainer = Players.LocalPlayer.PlayerGui.LobbyUI.BoostsContainer
	
	BoostController.BoostsInfo = data

	for _, v in data do
		local ThisBoostFrame = BoostsContainer:FindFirstChild(v.Name)
		if not ThisBoostFrame then
			ThisBoostFrame = ReplicatedStorage.Assets.Templates.BoostTemplate:Clone()
			ThisBoostFrame.Name = v.Name
			ThisBoostFrame.BoostImage.Image = ItemsData[v.Name].Image
			ThisBoostFrame.Parent = BoostsContainer
		end
	end
	
	for _, v in BoostsContainer:GetChildren() do
		if v:IsA'GuiObject' then
			local exists = false
			for _, bInfo in BoostController.BoostsInfo do
				if bInfo.Name == v.Name then
					exists = true
					break
				end
			end
			
			if exists == false then
				v:Destroy()
			end
		end
	end
end


local function setupBoostsContainer()
	local BoostsContainer = Players.LocalPlayer.PlayerGui.LobbyUI.BoostsContainer
	
	local MIN_FORMAT_STR = '%i:%02i'
	local HOUR_FORMAT_STR = '%i:%02i:%02i'
	RunService.RenderStepped:Connect(function(dt: number)
		for _, v in BoostsContainer:GetChildren() do
			if not v:IsA'GuiObject' then continue end
			
			local boostData
			for _, bInfo in BoostController.BoostsInfo do
				if bInfo.Name == v.Name then
					boostData = bInfo
					break
				end
			end
			
			if boostData then
				local _, h, m, s = Math.GetDHMS(boostData.TimeLeft, true)
				v.TextLabel.Text = h >= 1 and HOUR_FORMAT_STR:format(h, m, s) or MIN_FORMAT_STR:format(m, s)
				
				boostData.TimeLeft -= dt
			end
		end
	end)
end


function BoostController._Start()
	Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'
	
	local BoostsContainer = Players.LocalPlayer.PlayerGui.LobbyUI.BoostsContainer
	
	for _, v in BoostsContainer:GetChildren() do
		if v:IsA'GuiObject' then v:Destroy() end
	end
	
	Packets.Boosts.UpdateBoosts.listen(refreshBoosts)
	Packets.Boosts.UpdateBoosts.send()
	
	task.defer(setupBoostsContainer)
end


return BoostController