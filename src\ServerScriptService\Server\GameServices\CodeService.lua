local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')

local Server = require(ServerScriptService.Server)

local Packets = require(ReplicatedStorage.Data.Packets)

local CodeService = {}


function CodeService.IsValidCode(code: string): boolean
	local CodesList = Server.Immutable.Codes
	return CodesList[code] ~= nil
end


function CodeService.IsCodeExpired(code: string): boolean
	if CodeService.IsValidCode(code) then
		local CodesList = Server.Immutable.Codes
		return CodesList[code].Expired
	end

	return false
end


function CodeService.HasRedeemedCode(plr: Player, code: string): boolean
	local DataService = Server.GetService('DataService')
	local profile = DataService.GetProfile(plr)

	if table.find(profile.CodesRedeemed, code) then
		return true
	end
	
	return false
end


function CodeService.RedeemCode(plr: Player, code: string)
	local DataService = Server.GetService('DataService')
	
	if not CodeService.IsValidCode(code) then
		Packets.Codes.CodeSubmitted.sendTo({
			Color = Color3.fromRGB(255, 0, 4):ToHex(),
			Text = 'Invalid Code'
		}, plr)
		return
	elseif CodeService.HasRedeemedCode(plr, code) then
		Packets.Codes.CodeSubmitted.sendTo({
			Color = Color3.fromRGB(255, 162, 0):ToHex(),
			Text = 'Code already redeemed'
		}, plr)
	elseif CodeService.IsCodeExpired(code) then
		Packets.Codes.CodeSubmitted.sendTo({
			Color = Color3.fromRGB(255, 0, 4):ToHex(),
			Text = 'Code expired'
		}, plr)
	else
		local profile = DataService.GetProfile(plr)
		
		local Rewards = Server.Immutable.Codes[code].Rewards
		
		for _, v in Rewards do
			Server.GetService(v.Service)[v.Function](plr, table.unpack(v.Args))
		end
		
		table.insert(profile.CodesRedeemed, code)
		
		Packets.Codes.CodeSubmitted.sendTo({
			Color = Color3.fromRGB(0, 255, 17):ToHex(),
			Text = 'Code Redeemed'
		}, plr)
	end
end


function CodeService._Start()
	Packets.Codes.RedeemCode.listen(function(data: string, plr: Player)
		CodeService.RedeemCode(plr, data)
	end)
end


return CodeService