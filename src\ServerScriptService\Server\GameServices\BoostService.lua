local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')

local Server = require(ServerScriptService.Server)

local Packets = require(ReplicatedStorage.Data.Packets)
local ItemsData = require(ReplicatedStorage.Data.Items)

local BoostService = {}


function BoostService.UpdateBoostInfoForClient(plr: Player)
	local DataService = Server.GetService('DataService')

	local profile = DataService.GetProfile(plr)
	
	local res = {}
	
	for boostName, boostInfo in profile.Boosts do
		table.insert(res, {
			Name = boostName,
			TimeLeft = boostInfo.TimeLeft
		})
	end
	
	Packets.Boosts.UpdateBoosts.sendTo(res, plr)
end


function BoostService.UseBoost(plr: Player, boost: string)
	local DataService = Server.GetService('DataService')
	local InventoryService = Server.GetService('InventoryService')
	
	if ItemsData[boost].Type ~= 'Boost' then return end
	
	local profile = DataService.GetProfile(plr)
	
	if not profile.Boosts[boost] then
		profile.Boosts[boost] = {
			TimeStart = os.time(),
			TimeLeft = 0
		}
	end
	
	profile.Boosts[boost].TimeLeft += ItemsData[boost].Duration
	
	BoostService.UpdateBoostInfoForClient(plr)
end


function BoostService.GetLuckBoost(plr: Player)
	local DataService = Server.GetService('DataService')

	local profile = DataService.GetProfile(plr)
	
	local BoostTotal = 0
	for boostName, boostInfo in profile.Boosts do
		if ItemsData[boostName].Type == 'Boost' then
			BoostTotal += ItemsData[boostName].Boost
		end
	end
	return BoostTotal
end


function BoostService._PlayerAdded(plr: Player)
	local DataService = Server.GetService('DataService')
	
	local profile = DataService.GetProfile(plr)
	
	while true do
		task.wait(1)
		
		for boostName, boostInfo in profile.Boosts do
			profile.Boosts[boostName].TimeLeft -= 1
			
			if profile.Boosts[boostName].TimeLeft == 0 then
				profile.Boosts[boostName] = nil
				BoostService.UpdateBoostInfoForClient(plr)
			end
		end
	end
end


function BoostService._Start()
	Packets.Boosts.UpdateBoosts.listen(function(_, plr: Player)
		BoostService.UpdateBoostInfoForClient(plr)
	end)
end


return BoostService