local ReplicatedStorage = game:GetService('ReplicatedStorage')

local CookingService = require(script.Parent)
local Ingredients = require(ReplicatedStorage.Data.Ingredients)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)

local EXTRA_SPACE = .05

local SOUP_OFFSET = CFrame.new(-0.00189971924, 0.358625889, 0.0188140869, 1, 0, 0, 0, 1, 0, 0, 0, 1)
local RICE_ONIGIRI_OFFSET = CFrame.new(0.0656471252, 0.0671433657, -0.0672111511, 0.994731963, 0.102042295, -0.00978859793, 0.00234164065, 0.0728449076, 0.99734056, 0.102483965, -0.992109418, 0.072222203)
local COFFEE_OFFSET = CFrame.new(-0.125854492, 0.256013513, -0.00012588501, 1, 0, 0, 0, 1, 0, 0, 0, 1)


return function(thisIngredient: BasePart, interactable: BasePart)
	local bun = interactable:FindFirstChild('Bun')
	local plate = interactable:FindFirstChild('Plate')
	local bowl = interactable:FindFirstChild('Bowl')
	local mug = interactable:FindFirstChild('Mug')
	local bread = interactable:FindFirstChild('Bread')

	if bun then
		local foundIngredients = {}

		for _, ingredient in interactable:GetChildren() do
			if ingredient ~= bun and CookingService.IsIngredient(ingredient) then
				table.insert(foundIngredients, ingredient)
			end
		end

		table.sort(foundIngredients, function(a: BasePart, b: BasePart): boolean
			return (Ingredients[a.Name].VisualOrder or 1) < (Ingredients[b.Name].VisualOrder or 1)
		end)

		local offset = Vector3.new(0, bun.Size.Y/2 + EXTRA_SPACE, 0)
		for _, ingredient in foundIngredients do
			local expectedIngredientMesh = ReplicatedStorage.Assets.Ingredients:FindFirstChild('Bun' .. ingredient.Name)
			if expectedIngredientMesh and ingredient.MeshId ~= expectedIngredientMesh.MeshId then
				ingredient = CookingService.ReplaceIngredientModel(ingredient, expectedIngredientMesh:Clone())
			end
			
			ingredient.Position = bun.Position + offset

			offset += Vector3.new(0, ingredient.Size.Y + EXTRA_SPACE, 0)
		end

		bun.Top.Anchored = false
		bun.Top.Top.C1 = CFrame.new(offset - Vector3.new(0, bun.Size.Y/2, 0)):Inverse()
	elseif bread then
		local ham = interactable:FindFirstChild'Ham'
		if ham then
			ham.Position = bread.Position + Vector3.new(0, bread.Size.Y, 0)
			bread.Top.Top.C1 = CFrame.new(0, .1, 0)
		end
	elseif bowl and interactable:FindFirstChild'Carrot' and interactable:FindFirstChild'Potato' then
		if bowl:FindFirstChild('Soup') then return end
		
		local Soup = script.Soup:Clone()
		Soup.CFrame = bowl.CFrame * SOUP_OFFSET
		Soup.Parent = bowl
		
		interactable.Carrot.Transparency = 1
		interactable.Potato.Transparency = 1
		
		ClassExtension.Instance.Weld(Soup, bowl)
	elseif plate then
		if interactable:FindFirstChild'Rice' and interactable:FindFirstChild'Nori' and not plate:FindFirstChild'RiceOnigiri' then
			local RiceOnigiri = script.RiceOnigiri:Clone()
			RiceOnigiri.CFrame = plate.CFrame * RICE_ONIGIRI_OFFSET
			RiceOnigiri.Parent = plate
			
			interactable.Rice.Transparency = 1
			interactable.Nori.Transparency = 1
			
			ClassExtension.Instance.Weld(RiceOnigiri, plate)
		end
	elseif mug then
		if interactable:FindFirstChild'Coffee Bean' then
			local coffee = script.Coffee:Clone()
			coffee.CFrame = mug.CFrame * COFFEE_OFFSET
			coffee.Parent = mug
			
			interactable['Coffee Bean'].Transparency = 1
			
			ClassExtension.Instance.Weld(coffee, mug)
		end
	end
end