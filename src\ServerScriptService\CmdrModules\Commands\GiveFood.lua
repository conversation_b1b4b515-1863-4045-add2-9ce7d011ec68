return {
	Name = 'GiveFood',
	Aliases = {},
	Description = 'Give food to a player',
	Group = 225,
	Args = {
		{
			Type = 'players';
			Name = 'players';
			Description = 'The players to give to';
		},
		{
			Type = 'foods',
			Name = 'Foods',
			Description = 'Foods to give',
			Optional = false
		},
		{
			Type = 'integer',
			Name = 'amount',
			Description = 'The amount of food to give',
			Optional = true,
			Default = 1
		}
	}
}