local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Characters = require(ReplicatedStorage.Data.Characters)
local Packets = require(ReplicatedStorage.Data.Packets)

export type Ability = {
	GetCooldownTimeLeft: (Player) -> number,
	IsInUse: (Player) -> boolean,
	CanUse: (Player) -> boolean,
	UseAbility: (Player, any...) -> boolean,
	CancelAbility: (Player) -> (),
	
	COOLDOWN: number
}

export type Passive = {
	Init: (() -> ())?,
	Get: ((Player) -> (number))?,
	Update: ((Player) -> ())?
}

local AbilityService = {
	AbilityModules = {} :: { [string]: Ability },
	PassiveModules = {} :: { [string]: Passive }
}


function AbilityService.CancelAbilities(plr: Player)
	for k, v in AbilityService.AbilityModules do
		task.spawn(function()
			v.CancelAbility(plr)
			AbilityService.UpdatePassives()
		end)
	end
end


function AbilityService.IsUsingAbility(plr: Player): boolean
	for k, v in AbilityService.AbilityModules do
		if v.IsInUse and v.IsInUse(plr) then
			return true
		end
	end

	return false
end


function AbilityService.CanUseAnAbility(plr: Player): boolean
	local CookingService = Server.GetService('CookingService')
	
	if CookingService.KitchenwareModules.CuttingBoard.IsPlayerCutting(plr) then
		return false
	end
	
	return true
end


function AbilityService.UseAbility(AbilityData: { Args: {unknown}, AbilityName: string }, plr: Player)
	if not AbilityService.CanUseAnAbility(plr) or not AbilityService.CanUseAbilityAsCharacter(plr, AbilityData.AbilityName) or AbilityService.IsUsingAbility(plr) then return end
	
	local abilityModule = AbilityService.AbilityModules[AbilityData.AbilityName]

	if abilityModule and abilityModule.CanUse(plr) then
		local successfullyCasted = abilityModule.UseAbility(plr, table.unpack(AbilityData.Args))
		if successfullyCasted then
			Packets.Ability.SetCooldown.sendTo({
				Ability = AbilityData.AbilityName,
				StartTime = workspace:GetServerTimeNow(),
				EndTime = workspace:GetServerTimeNow() + abilityModule.GetCooldownTimeLeft(plr)
			}, plr)
			
			AbilityService.UpdatePassives()
			
			AbilityService.ReduceCooldowns(plr, AbilityService.GetPassiveValue(nil, 'CooldownAdjust'))
		end
	end
end


function AbilityService.CanUseAbilityAsCharacter(plr: Player, ability: string)
	local EquippedCharacter = AbilityService.GetEquippedCharacter(plr, true)
	return table.find(Characters[EquippedCharacter].Abilities, ability) ~= nil
end


function AbilityService.GetEquippedCharacter(plr: Player, waitForCharacter: true?): string?
	if not plr:GetAttribute'Character' then
		plr:GetAttributeChangedSignal'Character':Wait()
	end

	return plr:GetAttribute'Character'
end


function AbilityService.UpdatePassives(p: Player?)
	if Client.Immutable.SERVER_TYPE == 'Lobby' then return end
	
	for _, plr in (p and {p} or Players:GetPlayers()) do
		for k, v in AbilityService.PassiveModules do
			if v.Update then
				v.Update(plr)
			end
		end
	end
end


function AbilityService.ReduceCooldowns(plr: Player, amount: number)
	for k, v in AbilityService.AbilityModules do
		if v.Cooldowns[plr] then
			v.Cooldowns[plr] -= amount
			
			Packets.Ability.SetCooldown.sendTo({
				Ability = k,
				StartTime = workspace:GetServerTimeNow(),
				EndTime = workspace:GetServerTimeNow() + v.GetCooldownTimeLeft(plr)
			}, plr)
		end
	end
end


function AbilityService.GetPassiveValue(arg: any, passive: string): number
	return AbilityService.PassiveModules[passive].Get(arg)
end


function AbilityService._PlayerRemoving(plr: Player)
	AbilityService.CancelAbilities(plr)
end


function AbilityService._Start()
	local RoundService = Server.GetService('RoundService')
	
	if Client.Immutable.SERVER_TYPE == 'Round' then
		RoundService.RoundEndedSignal:Connect(function()
			for _, v in Players:GetPlayers() do
				AbilityService.CancelAbilities(v)
			end
		end)
		
		Packets.Ability.UseAbility.listen(AbilityService.UseAbility)
	end
end


function AbilityService._Init()
	for _, v in script.Abilities:GetChildren() do
		AbilityService.AbilityModules[v.Name] = require(v)
	end
	
	for _, v in script.Passives:GetChildren() do
		AbilityService.PassiveModules[v.Name] = require(v)
		if AbilityService.PassiveModules[v.Name].Init then
			AbilityService.PassiveModules[v.Name].Init()
		end
	end
end


return AbilityService