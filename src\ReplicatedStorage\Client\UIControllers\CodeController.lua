local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Packets = require(ReplicatedStorage.Data.Packets)

local CodeController = {}


local function setupCodeUI()
	local CodesFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Codes
	
	local TextBox = CodesFrame.CodeContainer.Type
	local ClaimButton = CodesFrame.Claim
	
	ClaimButton.MouseButton1Click:Connect(function()
		local submittedCode = TextBox.Text:lower():gsub('^%s*(.-)%s*$', '%1'):gsub('%s+', ' ')
		Packets.Codes.RedeemCode.send(submittedCode)
	end)
end


local function codeSubmitted(data: { Color: string, Text: string })
	local CodesFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Codes
	
	local TextBox = CodesFrame.CodeContainer.Type
	
	TextBox.Text = data.Text
	TextBox.TextColor3 = Color3.fromHex(data.Color)

	local c
	
	local resetted = false
	local function resetTextbox()
		if resetted then return end
		resetted = true
		
		c:Disconnect()
		TextBox.Text = ''
		TextBox.TextColor3 = Color3.fromRGB(252, 198, 132)
	end
	
	c = TextBox:GetPropertyChangedSignal'Text':Connect(function()
		resetTextbox()
	end)
	
	task.wait(2)
	
	resetTextbox()
end


function CodeController._Start()
	local CodesFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.Codes
	
	Packets.Codes.CodeSubmitted.listen(codeSubmitted)
	
	task.defer(setupCodeUI)
end


return CodeController