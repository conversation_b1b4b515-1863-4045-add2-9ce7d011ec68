local effectFrame = script.Parent
local isAnimating = false  -- Flag para controlar a animação

-- Função para animar os frames
local function animateFrames()
	-- Evita iniciar múltiplas animações
	if isAnimating then return end

	isAnimating = true

	while effectFrame.Visible do
		for i = 1, 35 do
			local frameName = "Frame" .. i
			local currentFrame = effectFrame:FindFirstChild(frameName)

			if currentFrame then
				currentFrame.Visible = true
				task.wait(0.05)  -- Tempo entre frames
				currentFrame.Visible = false
			else
				warn("Frame não encontrado: " .. frameName)
			end
		end
	end

	isAnimating = false
end

-- Conexão para verificar visibilidade
effectFrame:GetPropertyChangedSignal("Visible"):Connect(function()
	if effectFrame.Visible then
		animateFrames()
	end
end)

animateFrames()