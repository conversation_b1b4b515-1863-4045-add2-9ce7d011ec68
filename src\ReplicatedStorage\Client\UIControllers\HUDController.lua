local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local TweenService = game:GetService('TweenService')

local Client = require(ReplicatedStorage.Client)

local Table = require(ReplicatedStorage.SharedModules.TableUtil)
local Packets = require(ReplicatedStorage.Data.Packets)
local CharactersData = require(ReplicatedStorage.Data.Characters)
local spr = require(ReplicatedStorage.SharedModules.spr)
local Math = require(ReplicatedStorage.SharedModules.Math)

local HUDController = {}


local function setupHUDButtons()
	local UIController = Client.GetController('UIController')
	local AreaController = Client.GetController('AreaController')

	local HUDFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Hud
	local Pages = Players.LocalPlayer.PlayerGui.LobbyUI.Pages

	for _, v in Table.Extend(HUDFrame.Left:GetDescendants(), HUDFrame.Right:GetDescendants()) do
		if v:IsA'ImageButton' then
			UIController.SetButtonAnim(v)
			
			v.MouseButton1Click:Connect(function()
				if AreaController.IsArea(v.Name) then
					AreaController.TeleportToArea(v.Name)
				elseif Pages:FindFirstChild(v.Name) then
					UIController.Open(Pages[v.Name], true)
				end
			end)
		end
	end
end


local function setupBottomButtons()
	local ItemController = Client.GetController('ItemController')

	local HUDFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Hud

	local function updateCoins(coins: number)
		-- TODO: Animate
		HUDFrame.Bottom.Coins.Amount.Text = Math.Comma(coins)
	end

	local function updateGems(gems: number)
		-- TODO: Animate
		HUDFrame.Bottom.Gems.Amount.Text = Math.Comma(gems)
	end

	local function updateLevel(data: { Level: number, XP: number })
		-- TODO: Animate, setup bar, set xp reqs

		HUDFrame.Bottom.Level.Txt.Text = `LVL: {data.Level}`
		HUDFrame.Bottom.Level.Counter.Text = `{data.XP}/xxx XP`
	end

	local function setEquippedCharacter()
		local EquippedCharacter = Players.LocalPlayer:GetAttribute('Character')

		if HUDFrame.Bottom.EquippedUnit:FindFirstChild('EquippedCharacter') then
			HUDFrame.Bottom.EquippedUnit['EquippedCharacter']:Destroy()
		end

		if EquippedCharacter then
			HUDFrame.Bottom.EquippedUnit.X.Visible = false

			local ThisCharFrame = ItemController.CreateItem({
				ItemName = EquippedCharacter,
				Rarity = CharactersData[EquippedCharacter].Rarity,
				Parent = HUDFrame.Bottom.EquippedUnit,
				Position = UDim2.new(),
				Size = UDim2.fromScale(1, 1)
			})

			ThisCharFrame.Name = 'EquippedCharacter'
		else
			HUDFrame.Bottom.EquippedUnit.X.Visible = true
		end
	end

	Packets.HUD.UpdateGems.listen(updateGems)
	Packets.HUD.UpdateGems.send()

	Packets.HUD.UpdateCoins.listen(updateCoins)
	Packets.HUD.UpdateCoins.send()

	Packets.HUD.UpdateLevel.listen(updateLevel)
	Packets.HUD.UpdateLevel.send()

	if Players.LocalPlayer:GetAttribute'Character' then
		task.spawn(setEquippedCharacter)
	end
	Players.LocalPlayer:GetAttributeChangedSignal'Character':Connect(setEquippedCharacter)
end


function HUDController.HideHUDPortion(portion: 'Left' | 'Right' | 'Bottom')
	local HUDFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Hud
	
	if portion == 'Left' then
		spr.target(HUDFrame.Left, 1, 2, { Position = UDim2.fromScale(-.2, .5) })
	elseif portion == 'Right' then
		spr.target(HUDFrame.Right, 1, 2, { Position = UDim2.fromScale(1.2, .5) })
	elseif portion == 'Bottom' then
		spr.target(HUDFrame.Bottom, 1, 2, { Position = UDim2.fromScale(.5, 1.2) })
	end
end


function HUDController.ShowHUDPortion(portion: 'Left' | 'Right' | 'Bottom')
	local HUDFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Hud
	
	if portion == 'Left' then
		spr.target(HUDFrame.Left, .65, 2, { Position = UDim2.fromScale(.008, .5) })
	elseif portion == 'Right' then
		spr.target(HUDFrame.Right, .65, 2, { Position = UDim2.fromScale(.992, .5) })
	elseif portion == 'Bottom' then
		spr.target(HUDFrame.Bottom, .65, 2, { Position = UDim2.fromScale(.5, .985) })
	end
end


function HUDController._Start()
	local HUDFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Hud

	Packets.HUD.ToggleHUD.listen(function(data: { Enabled: boolean, Portions: {string} })
		for _, v in data.Portions do
			if data.Enabled then
				HUDController.ShowHUDPortion(v)
			else
				HUDController.HideHUDPortion(v)
			end
		end
	end)
	
	local ActionInfoFrame = Players.LocalPlayer.PlayerGui:WaitForChild'GameplayUI'.ActionInfo
	ActionInfoFrame:SetAttribute('Priority', 0)
	Packets.HUD.SetActionInfo.listen(function(data: { Priority: number, Text: string })
		if ActionInfoFrame.ActionInfo.Text ~= '' then
			if data.Priority < ActionInfoFrame:GetAttribute('Priority') then return end
		end
		
		ActionInfoFrame:SetAttribute('Priority', data.Priority)
		ActionInfoFrame.ActionInfo.Text = data.Text
		
		ActionInfoFrame.Visible = true
	end)

	task.defer(setupHUDButtons)
	task.defer(setupBottomButtons)
end


return HUDController