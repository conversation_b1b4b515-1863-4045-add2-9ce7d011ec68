--[=[
	Author: <PERSON><PERSON><PERSON> (Syveric)
	Gamepasses and dev products data
]=]

local ServerScriptService = game:GetService('ServerScriptService')
local MarketplaceService = game:GetService('MarketplaceService')
local RunService = game:GetService('RunService')
local Players = game:GetService('Players')

local Server = RunService:IsServer() and require(ServerScriptService.Server)

local MarketplaceModule = {
	Gamepasses = {
		['Premium Pass'] = {
			ID = 1350323061,
			Description = 'premium pass wooo!!',
			GiftID = 3364588018
		},
		
		['VIP'] = {
			ID = 1371939123,
			Description = 'vip yesyes',
			GiftID = 3364588393
		}
	},

	DevProducts = {
		['Skip1BattlepassLevel'] = {
			ID = 3349538118
		},
		
		['Skip10BattlepassLevels'] = {
			ID = 3349551885
		},
		
		['Buy1Crate'] = {
			ID = 3363649432
		},
		
		['Buy10Crates'] = {
			ID = 3363649569
		},
		
		['Pack 1'] = {
			ID = 3364542997
		},
		
		['Coins Tier 1'] = {
			ID = 3364639556,
			Currency = 'Coins',
			Order = 1
		},
		
		['Coins Tier 2'] = {
			ID = 3364643278,
			Currency = 'Coins',
			Order = 2
		},
		
		['Coins Tier 3'] = {
			ID = 3364643567,
			Currency = 'Coins',
			Order = 3
		},
		
		['Coins Tier 4'] = {
			ID = 3364643815,
			Currency = 'Coins',
			Order = 4
		},
		
		['Gems Tier 1'] = {
			ID = 3364658946,
			Currency = 'Gems',
			Order = 5
		},
		
		['Gems Tier 2'] = {
			ID = 3364659178,
			Currency = 'Gems',
			Order = 5
		},
		
		['Gems Tier 3'] = {
			ID = 3364659782,
			Currency = 'Gems',
			Order = 5
		},
		
		['Gems Tier 4'] = {
			ID = 3364659933,
			Currency = 'Gems',
			Order = 5
		}
	}
}

------------------------------------------------------

local GAMEPASS_RETRY_COUNT = 3
local PRODUCT_INFO_RETRY_COUNT = 3


local ProductInfoCache = {}
function MarketplaceModule.GetProductInfo(id, assetType, yield: number?): {}
	if ProductInfoCache[id] then return ProductInfoCache[id] end
	
	for i = 1, PRODUCT_INFO_RETRY_COUNT do
		local success, ret = pcall(function()
			return MarketplaceService:GetProductInfo(id, assetType)
		end)

		if success then
			ProductInfoCache[id] = ret
			return ret
		else
			if ret:find('Bad Request') then
				break
			end
			warn(ret .. 'SimpleMarketplace:GetProductInfo', id, assetType)
			task.wait(yield or 3)
		end
	end
end


function MarketplaceModule.GetNameFromID(id: number): string?
	for k, v in MarketplaceModule.Gamepasses do
		if v.ID == id then return k end
	end
	
	for k, v in MarketplaceModule.DevProducts do
		if v.ID == id then return k end
	end
end


function MarketplaceModule.UserOwnsGamePassAsync(userId: number, id: number | { number }): boolean
	local plr: Player = Players:GetPlayerByUserId(userId)
	
	if plr:GetAttribute(id) then return true end
	
	for _, id in (type(id) == 'table' and id or {id}) do
		for i = 1, GAMEPASS_RETRY_COUNT do
			local success, ret = pcall(function()
				if RunService:IsServer() and Server.GetService('MonetizationService').DoesOwnGamepassInData(plr, id) then
					return true
				end
				return MarketplaceService:UserOwnsGamePassAsync(userId, id)
			end)

			if success then
				if RunService:IsServer() and ret == true then
					plr:SetAttribute(id, true)
				end
				
				return ret
			else
				warn(ret .. ' SimpleMarketplace:UserOwnsGamePassAsync > Gamepass check')
				task.wait(3)
			end
		end
	end

	return false
end


return MarketplaceModule