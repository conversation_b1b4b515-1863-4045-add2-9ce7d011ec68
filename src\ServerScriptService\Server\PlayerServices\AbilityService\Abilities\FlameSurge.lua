local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local TweenService = game:GetService('TweenService')
local Debris = game:GetService('Debris')
local CollectionService = game:GetService('CollectionService')
local SoundService = game:GetService('SoundService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local CookingService: typeof(require(ServerScriptService.Server.GameServices.CookingService)) = Server.GetService('CookingService')

local FlameSurge = {
	COOLDOWN = 60,
	EFFECT_TIME = 6,
	
	Cooldowns = {} :: { [Player]: number },
	AbilityJanitors = {} :: { [Player]: Janitor.Janitor }
}


function FlameSurge.GetCooldownTimeLeft(plr: Player): number
	if FlameSurge.Cooldowns[plr] then
		return FlameSurge.COOLDOWN - (os.clock() - FlameSurge.Cooldowns[plr])
	end
	return 0
end


function FlameSurge.CanUse(plr: Player): boolean
	if FlameSurge.Cooldowns[plr] then
		return os.clock() - FlameSurge.Cooldowns[plr] > FlameSurge.COOLDOWN
	end
	
	return true
end


function FlameSurge.IsInUse(plr: Player): boolean
	if FlameSurge.AbilityJanitors[plr] and FlameSurge.AbilityJanitors[plr]:Get('Active') then
		return true
	end

	return false
end


function FlameSurge.UseAbility(plr: Player): boolean
	if not FlameSurge.CanUse(plr) or FlameSurge.IsInUse(plr) then return false end

	FlameSurge.Cooldowns[plr] = os.clock()

	task.delay(FlameSurge.COOLDOWN, function()
		FlameSurge.Cooldowns[plr] = nil
	end)

	if not FlameSurge.AbilityJanitors[plr] then
		FlameSurge.AbilityJanitors[plr] = Janitor.new()
	end
	
	FlameSurge.AbilityJanitors[plr]:Add(math.random, nil, 'Active')
	
	ClassExtension.Player.DisableMovement(plr, true)
	plr:SetAttribute(script.Name, true)
	
	local FlameSurgeTrack: AnimationTrack = Animations:PlayTrack(plr, 'RengokuFlameSurge')
	FlameSurgeTrack:AdjustSpeed(1.75)
	
	SoundService.SFX.Abilities.FlameSurge:Play()
	
	do
		local CharFX = {} :: { ParticleEmitter }
		for _, v in script.HumanoidRootPart:GetChildren() do
			local ThisFX = v:Clone()
			ThisFX.Parent = plr.Character.HumanoidRootPart
			table.insert(CharFX, ThisFX)
		end
		
		FlameSurge.AbilityJanitors[plr]:Add(function()
			for _, v in CharFX do
				VFXFunctions.DisableDescendants(v)
				Debris:AddItem(v, VFXFunctions.GetHighestWaitTime(v))
			end
		end, nil, 'FX')
	end
	
	FlameSurge.AbilityJanitors[plr]:Add(task.delay(FlameSurge.EFFECT_TIME, function()
		FlameSurge.AbilityJanitors[plr]:Cleanup()
	end))
	
	FlameSurge.AbilityJanitors[plr]:Add(FlameSurgeTrack.Ended:Connect(function()
		ClassExtension.Player.DisableMovement(plr, false)
		FlameSurge.AbilityJanitors[plr]:Remove('Active')
		FlameSurge.AbilityJanitors[plr]:Remove('FX')
	end))

	FlameSurge.AbilityJanitors[plr]:Add(function()
		plr:SetAttribute(script.Name, nil)
		FlameSurgeTrack:Stop()
		ClassExtension.Player.DisableMovement(plr, false)

		Server.GetService('AbilityService').UpdatePassives()
	end)
	
	return true
end


function FlameSurge.CancelAbility(plr: Player)
	if not FlameSurge.IsInUse(plr) then return end

	if FlameSurge.AbilityJanitors[plr] then
		FlameSurge.AbilityJanitors[plr]:Cleanup()
	end
end


return FlameSurge