local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)
local Packets = require(ReplicatedStorage.Data.Packets)

local SukunaSlash = {
	Key = Enum.KeyCode.C,

	GamepadKey = Enum.KeyCode.ButtonL2
}


function SukunaSlash.KeyDown()
	Packets.Ability.UseAbility.send({
		AbilityName = script.Name,
		Args = {}
	})
end


function SukunaSlash.KeyUp()

end


function SukunaSlash.Click()
	SukunaSlash.KeyDown()
end


function SukunaSlash.IsOnCooldown()
	return Client.GetController'AbilityController'.IsAbilityOnCooldown(script.Name)
end


function SukunaSlash.CanUseAbilityAsCharacter()
	return Client.GetController'AbilityController'.CanUseAbilityAsCharacter(script.Name)
end


function SukunaSlash._Init()

end


return SukunaSlash
