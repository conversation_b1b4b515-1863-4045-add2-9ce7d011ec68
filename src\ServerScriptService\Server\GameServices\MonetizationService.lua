local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local ServerScriptService = game:GetService('ServerScriptService')
local AnalyticsService = game:GetService('AnalyticsService')
local MarketplaceService = game:GetService('MarketplaceService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local SimpleMarketplace = require(ReplicatedStorage.Data.SimpleMarketplace)
local Packets =  require(ReplicatedStorage.Data.Packets)

local MonetizationService = {
	DevproductFunctions = {} :: { [string]: (Player) -> () },
	GamepassFunctions = {} :: { [string]: {OnJoin: (Player) -> (), OnPurchase: (Player) -> ()} },
	
	Gifting = {}
}


local function addGamepassToData(plr: Player, passName: string)
	local DataService = Server.GetService('DataService')
	
	local profile = DataService.GetProfile(plr)
	
	profile.OtherData.PurchasedPasses[passName] = true
	
	SimpleMarketplace.UserOwnsGamePassAsync(plr.UserId, SimpleMarketplace.Gamepasses[passName].ID)
	
	local OwnedPasses = {}
	for passName in profile.OtherData.PurchasedPasses do
		table.insert(OwnedPasses, passName)
	end
	Packets.Store.OwnedGamepasses.sendTo(OwnedPasses, plr)
	
	--[[if MonetizationService.GamepassFunctions[passName] and MonetizationService.GamepassFunctions[passName].OnPurchase then
		MonetizationService.GamepassFunctions[passName].OnPurchase(plr)
	end]]
end


function MonetizationService.DoesOwnGamepassInData(plr: Player, passId: number)
	local DataService = Server.GetService('DataService')
	
	local profile = DataService.GetProfile(plr)
	
	local passName = SimpleMarketplace.GetNameFromID(passId)
	return profile.OtherData.PurchasedPasses[passName]
end


do -- product callbacks
	MonetizationService.DevproductFunctions['Skip1BattlepassLevel'] = function(plr: Player)
		local DataService = Server.GetService('DataService')
		local BattlepassService = Server.GetService('BattlepassService')
		
		local profile = DataService.GetProfile(plr)
		
		profile.Battlepass.Level += 1
		BattlepassService.AddXP(plr, 0)
	end
	
	MonetizationService.DevproductFunctions['Skip10BattlepassLevels'] = function(plr: Player)
		local DataService = Server.GetService('DataService')
		local BattlepassService = Server.GetService('BattlepassService')

		local profile = DataService.GetProfile(plr)

		profile.Battlepass.Level += 10
		BattlepassService.AddXP(plr, 0)
	end
	
	MonetizationService.DevproductFunctions['Buy1Crate'] = function(plr: Player)
		local CrateService = Server.GetService('CrateService')
		CrateService.BuyCrate(plr, 'Robux Crate')
	end
	
	MonetizationService.DevproductFunctions['Buy10Crates'] = function(plr: Player)
		local CrateService = Server.GetService('CrateService')
		for i = 1, 10 do
			CrateService.BuyCrate(plr, 'Robux Crate')
		end
	end
	
	MonetizationService.DevproductFunctions['Pack 1'] = function(plr: Player)
		for _, v in Client.Immutable.Packs['Pack 1'].Items do
			Server.GetService(v.Reward.Service)[v.Reward.Function](plr, table.unpack(v.Reward.Args))
		end
	end
	
	MonetizationService.DevproductFunctions[tostring(SimpleMarketplace.Gamepasses['VIP'].GiftID)] = function(plr: Player)
		Server.GetService'InventoryService'.AddItem(plr, 'VIP', 1)
	end
	
	MonetizationService.DevproductFunctions[tostring(SimpleMarketplace.Gamepasses['Premium Pass'].GiftID)] = function(plr: Player)
		Server.GetService'InventoryService'.AddItem(plr, 'Premium Pass', 1)
	end
	
	MonetizationService.DevproductFunctions['Coins Tier 1'] = function(plr: Player)
		Server.GetService'CurrencyService'.AddCoins(plr, 100, Enum.AnalyticsEconomyTransactionType.IAP, 'Coins Tier 1')
	end
	
	MonetizationService.DevproductFunctions['Coins Tier 2'] = function(plr: Player)
		Server.GetService'CurrencyService'.AddCoins(plr, 200, Enum.AnalyticsEconomyTransactionType.IAP, 'Coins Tier 2')
	end
	
	MonetizationService.DevproductFunctions['Coins Tier 3'] = function(plr: Player)
		Server.GetService'CurrencyService'.AddCoins(plr, 300, Enum.AnalyticsEconomyTransactionType.IAP, 'Coins Tier 2')
	end
	
	MonetizationService.DevproductFunctions['Coins Tier 4'] = function(plr: Player)
		Server.GetService'CurrencyService'.AddCoins(plr, 400, Enum.AnalyticsEconomyTransactionType.IAP, 'Coins Tier 4')
	end
	
	MonetizationService.DevproductFunctions['Gems Tier 1'] = function(plr: Player)
		Server.GetService'CurrencyService'.AddGems(plr, 10, Enum.AnalyticsEconomyTransactionType.IAP, 'Gems Tier 1')
	end

	MonetizationService.DevproductFunctions['Gems Tier 2'] = function(plr: Player)
		Server.GetService'CurrencyService'.AddGems(plr, 20, Enum.AnalyticsEconomyTransactionType.IAP, 'Gems Tier 2')
	end

	MonetizationService.DevproductFunctions['Gems Tier 3'] = function(plr: Player)
		Server.GetService'CurrencyService'.AddGems(plr, 30, Enum.AnalyticsEconomyTransactionType.IAP, 'Gems Tier 3')
	end

	MonetizationService.DevproductFunctions['Gems Tier 4'] = function(plr: Player)
		Server.GetService'CurrencyService'.AddGems(plr, 40, Enum.AnalyticsEconomyTransactionType.IAP, 'Gems Tier 4')
	end
end

do -- gamepass callbacks
	MonetizationService.GamepassFunctions['Premium Pass'] = {
		OnPurchase = function(plr: Player, claim: boolean?)
			if not claim then
				Server.GetService'InventoryService'.AddItem(plr, 'Premium Pass', 1)
				return
			end
			
			addGamepassToData(plr, 'Premium Pass')
			
			local BattlepassService = Server.GetService('BattlepassService')
			BattlepassService.AddXP(plr, 0)
		end
	}
	
	MonetizationService.GamepassFunctions['VIP'] = {
		OnPurchase = function(plr: Player, claim: boolean?)
			if not claim then
				Server.GetService'InventoryService'.AddItem(plr, 'VIP', 1)
				return
			end
			--
			
			addGamepassToData(plr, 'VIP')
		end
	}
end


local function newPurchase(plr: Player, robuxAmount: number)
	local UserData = Server.GetService'DataService'.GetProfile(plr)

	UserData.OtherData.RobuxSpent += robuxAmount
	UserData.OtherData.RobuxPurchases += 1

	AnalyticsService:LogCustomEvent(plr, 'Robux Spent in-game', robuxAmount, {
		[Enum.AnalyticsCustomFieldKeys.CustomField01.Name] = robuxAmount
	})
end


local function processReceipt(receiptInfo)
	local player = Players:GetPlayerByUserId(receiptInfo.PlayerId)

	if not player then return Enum.ProductPurchaseDecision.NotProcessedYet end
	
	task.spawn(function()
		local robuxAmount = tonumber(SimpleMarketplace.GetProductInfo(receiptInfo.ProductId, Enum.InfoType.Product, 3).PriceInRobux or 0)
		newPurchase(player, robuxAmount)
	end)

	local productName = SimpleMarketplace.GetNameFromID(receiptInfo.ProductId)

	local devProductFunction = MonetizationService.DevproductFunctions[productName]
	if not devProductFunction then
		for k, passInfo in SimpleMarketplace.Gamepasses do
			if passInfo.GiftID == receiptInfo.ProductId then
				devProductFunction = MonetizationService.DevproductFunctions[tostring(passInfo.GiftID)]
				break
			end
		end
	end
	
	if not devProductFunction then
		warn('No devproduct/gamepass function for product', productName)
	end
	
	if MonetizationService.Gifting[player] and Players:FindFirstChild(MonetizationService.Gifting[player]) then
		Packets.Store.GiftReceived.sendTo({
			FromUsername = player.Name,
			ID = receiptInfo.ProductId
		}, Players[MonetizationService.Gifting[player]])
		
		player = Players[MonetizationService.Gifting[player]]
	end

	local success = true
	if devProductFunction then
		local err
		success, err = pcall(devProductFunction, player)
		if not success then
			warn(err)
		end
	end

	if success then
		local UserData = Server.GetService'DataService'.GetProfile(player)
		local prodID = tostring(receiptInfo.ProductId)
		if not UserData.OtherData.PurchasedProducts[prodID] then
			UserData.OtherData.PurchasedProducts[prodID] = 1
		else
			UserData.OtherData.PurchasedProducts[prodID] += 1
		end

		return Enum.ProductPurchaseDecision.PurchaseGranted
	else
		-- TODO: Add error handling
		return Enum.ProductPurchaseDecision.NotProcessedYet
	end
end


function MonetizationService._PlayerRemoving(plr: Player)
	MonetizationService.Gifting[plr] = nil
end


function MonetizationService._PlayerAdded(plr: Player)
	for k, v in SimpleMarketplace.Gamepasses do
		if not SimpleMarketplace.UserOwnsGamePassAsync(plr.UserId, v.ID) then continue end

		local passName = SimpleMarketplace.GetNameFromID(v.ID)

		if not passName then
			warn('COULD NOT FIND GAMEPASS INFO FOR:', k)
		end

		if passName and SimpleMarketplace.Gamepasses[passName] and MonetizationService.GamepassFunctions[passName] and MonetizationService.GamepassFunctions[passName].OnJoin then
			MonetizationService.GamepassFunctions[passName].OnJoin(plr)
		end
	end
end


function MonetizationService._Start()
	MarketplaceService.ProcessReceipt = processReceipt
	
	MarketplaceService.PromptGamePassPurchaseFinished:Connect(function(plr, passId, purchased)
		if not purchased then return end

		local passName = SimpleMarketplace.GetNameFromID(passId)

		if not passName then
			warn('COULD NOT FIND GAMEPASS INFO FOR:', passId)
		end

		task.spawn(function()
			local robuxAmount = tonumber(SimpleMarketplace.GetProductInfo(passId, Enum.InfoType.GamePass, 3).PriceInRobux or 0)
			newPurchase(plr, robuxAmount)
		end)

		if passName and SimpleMarketplace.Gamepasses[passName] and MonetizationService.GamepassFunctions[passName] and MonetizationService.GamepassFunctions[passName].OnPurchase then
			MonetizationService.GamepassFunctions[passName].OnPurchase(plr)
			return true
		end
	end)
	
	MarketplaceService.PromptPurchaseFinished:Connect(function(plr, id, purchased)
		if not purchased then return end


	end)
	
	Packets.Store.SetGiftingUser.listen(function(username: string, plr: Player)
		MonetizationService.Gifting[plr] = username
	end)
end


return MonetizationService