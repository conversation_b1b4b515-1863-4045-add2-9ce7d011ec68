local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')

local Server = require(ServerScriptService.Server)

local PassiveModule = {}


function PassiveModule.Init()
	local CookingService = Server.GetService('CookingService')
	local AbilityService = Server.GetService('AbilityService')
	
	CookingService.FireExtinguished:Connect(function(_, plr: Player?)
		if plr and plr:GetAttribute('Character') == 'Satoru Gojo' then
			print('AAAAAAAAAAAAAAAA')
			AbilityService.ReduceCooldowns(plr, 10)
		end
	end)
end


return PassiveModule