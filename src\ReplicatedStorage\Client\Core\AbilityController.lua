local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local TweenService = game:GetService('TweenService')
local RunService = game:GetService('RunService')
local UserInputService = game:GetService('UserInputService')

local Characters = require(ReplicatedStorage.Data.Characters)
local Packets = require(ReplicatedStorage.Data.Packets)
local Client = require(ReplicatedStorage.Client)
local Janitor = require(ReplicatedStorage.SharedModules.Janitor)

local AbilityController = {
	CooldownJanitors = {} :: { Janitor.Janitor }
}


function AbilityController.SetAbilityOnCooldown(data: { Ability: string, EndTime: number, StartTime: number })
	local EffectController = Client.GetController('EffectController')
	
	local AbilitiesContainer = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'GameplayUI'.Ability

	if AbilitiesContainer:FindFirstChild(data.Ability) then
		if AbilityController.CooldownJanitors[data.Ability] then
			AbilityController.CooldownJanitors[data.Ability]:Destroy()
		end

		AbilityController.CooldownJanitors[data.Ability] = Janitor.new()

		local ThisAbilityFrame: GuiObject = AbilitiesContainer[data.Ability]

		AbilityController.CooldownJanitors[data.Ability]:Add(function()
			if ThisAbilityFrame:IsDescendantOf(AbilitiesContainer) then
				ThisAbilityFrame.Cooldown.Visible = false
			end
		end)

		AbilityController.CooldownJanitors[data.Ability]:Add(RunService.RenderStepped:Connect(function(dt: number)
			if not ThisAbilityFrame:IsDescendantOf(AbilitiesContainer) then
				AbilityController.CooldownJanitors[data.Ability]:Cleanup()
				return
			end
			
			local cooldownTimeLeft = data.EndTime - workspace:GetServerTimeNow()
			local elapsed = workspace:GetServerTimeNow() - data.StartTime

			ThisAbilityFrame.Cooldown.Cooldown.Text = ('%.1fs'):format(cooldownTimeLeft)
			
			local progress = 1 - (elapsed / (data.EndTime - data.StartTime))
			ThisAbilityFrame.Cooldown.UIGradient.Transparency = NumberSequence.new{
				NumberSequenceKeypoint.new(0, 0),
				NumberSequenceKeypoint.new(math.clamp(progress, .001, .998), 0),
				NumberSequenceKeypoint.new(math.clamp(progress + .001, .002, .999), 1),
				NumberSequenceKeypoint.new(1, 1)
			}

			if cooldownTimeLeft <= 0 then
				AbilityController.CooldownJanitors[data.Ability]:Cleanup()
			end
		end))

		ThisAbilityFrame.Cooldown.Visible = true
	end
end


function AbilityController.IsAbilityOnCooldown(ability: string): boolean
	local AbilitiesContainer = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'GameplayUI'.Ability
	
	if AbilitiesContainer:FindFirstChild(ability) then
		return AbilitiesContainer[ability].Cooldown.Visible
	end
	
	return false
end


function AbilityController.LoadAbilities()
	local CharacterController = Client.GetController('CharacterController')
	local InputController = Client.GetController('InputController')

	local AbilitiesContainer = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'GameplayUI'.Ability

	local ThisCharacter = CharacterController.GetEquippedCharacter(true)
	local Abilities = Characters[ThisCharacter].Abilities

	--table.insert(Abilities, 'Dash') -- TODO: Move this somewhere elsee

	for _, v in AbilitiesContainer:GetChildren() do
		if v:IsA'GuiObject' and not table.find(Abilities, v.Name) then
			local AbilityBinder = InputController.Binders[v.Name]
			if AbilityBinder and AbilityBinder.Cleanup then
				AbilityBinder.Cleanup()
			end
			
			v:Destroy()
		end
	end

	for idx, v in Abilities do
		if AbilitiesContainer:FindFirstChild(v) then continue end

		local AbilityBinder = InputController.Binders[v]

		local AbilityTemplate = ReplicatedStorage.Assets.Templates.Ability:Clone()

		AbilityTemplate.Name = v
		AbilityTemplate.AbilityName.Text = v
		AbilityTemplate.Cooldown.Visible = false

		if AbilityBinder and AbilityBinder.Click then
			AbilityTemplate.MouseButton1Click:Connect(AbilityBinder.Click)
		end

		AbilityTemplate.Parent = AbilitiesContainer
	end
	
	for _, v in AbilitiesContainer:GetChildren() do
		if v:IsA'GuiObject' then
			local AbilityBinder = InputController.Binders[v.Name]
			
			if InputController.Preferred == 'Touch' then
				v.Key.Visible = false
			else
				v.Key.Visible = true
				
				local keyImage = UserInputService:GetImageForKeyCode(AbilityBinder.GamepadKey)
				if InputController.Preferred == 'Gamepad' and keyImage ~= '' then
					v.Key.TextLabel.Text = ''
					v.Key.ImageLabel.Visible = true
					v.Key.ImageLabel.Image = keyImage
				else
					v.Key.TextLabel.Text = AbilityBinder.Key.Name
					v.Key.ImageLabel.Visible = false
				end
			end
		end
	end
end


function AbilityController.CleanupHangingClicks()
	local InputController = Client.GetController('InputController')
	
	for k, v in Characters do
		for _, ability in v.Abilities do
			local AbilityBinder = InputController.Binders[v]
			if AbilityBinder and AbilityBinder.Cleanup then
				AbilityBinder.Cleanup()
			end
		end
	end
end


function AbilityController.CanUseAbilityAsCharacter(ability: string)
	local EquippedCharacter = Client.GetController('CharacterController').GetEquippedCharacter(true)
	return table.find(Characters[EquippedCharacter].Abilities, ability) ~= nil
end


function AbilityController._Start()
	local InputController = Client.GetController('InputController')
	
	local AbilitiesContainer = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'GameplayUI'.Ability
	
	for _, v in AbilitiesContainer:GetChildren() do
		if v:IsA'GuiObject' then
			v:Destroy()
		end
	end
	
	task.defer(AbilityController.LoadAbilities)

	Packets.Ability.SetCooldown.listen(AbilityController.SetAbilityOnCooldown)
	Packets.Round.RoundEnded.listen(AbilityController.CleanupHangingClicks)
	
	InputController.PreferredChanged:Connect(AbilityController.LoadAbilities)
	Players.LocalPlayer:GetAttributeChangedSignal'Character':Connect(AbilityController.LoadAbilities)
end


return AbilityController