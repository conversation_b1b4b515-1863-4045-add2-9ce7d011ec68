local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local TweenService = game:GetService('TweenService')
local Debris = game:GetService('Debris')
local CollectionService = game:GetService('CollectionService')
local SoundService = game:GetService('SoundService')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local Table = require(ReplicatedStorage.SharedModules.TableUtil)
local CookingService: typeof(require(ServerScriptService.Server.GameServices.CookingService)) = Server.GetService('CookingService')

local Stop = {
	COOLDOWN = 5,
	EFFECT_TIME = 6,
	
	Cooldowns = {} :: { [Player]: number },
	AbilityJanitors = {} :: { [Player]: Janitor.Janitor }
}


function Stop.GetCooldownTimeLeft(plr: Player): number
	if Stop.Cooldowns[plr] then
		return Stop.COOLDOWN - (os.clock() - Stop.Cooldowns[plr])
	end
	return 0
end


function Stop.CanUse(plr: Player): boolean
	if Stop.Cooldowns[plr] then
		return os.clock() - Stop.Cooldowns[plr] > Stop.COOLDOWN
	end
	
	return true
end


function Stop.IsInUse(plr: Player): boolean
	if Stop.AbilityJanitors[plr] and Stop.AbilityJanitors[plr]:Get('Active') then
		return true
	end

	return false
end


function Stop.UseAbility(plr: Player): boolean
	if not Stop.CanUse(plr) or Stop.IsInUse(plr) then return false end

	Stop.Cooldowns[plr] = os.clock()

	task.delay(Stop.COOLDOWN, function()
		Stop.Cooldowns[plr] = nil
	end)

	if not Stop.AbilityJanitors[plr] then
		Stop.AbilityJanitors[plr] = Janitor.new()
	end
	
	Stop.AbilityJanitors[plr]:Add(math.random, nil, 'Active')
	
	ClassExtension.Player.DisableMovement(plr, true)
	plr:SetAttribute(script.Name, true)
	
	local TogeStopTrack: AnimationTrack = Animations:PlayTrack(plr, 'TogeStop')
	
	SoundService.SFX.Abilities.TogeStop:Play()
	
	Stop.AbilityJanitors[plr]:Add(TogeStopTrack:GetMarkerReachedSignal'Hit':Once(function()
		for _, v in script.HumanoidRootPart:GetChildren() do
			local ThisFX = v:Clone()
			ThisFX.Parent = plr.Character.HumanoidRootPart
			
			VFXFunctions.EmitDescendants(ThisFX)
			
			task.delay(VFXFunctions.GetHighestWaitTime(ThisFX) + 2, function()
				ThisFX:Destroy()
			end)
		end
	end))
	
	Stop.AbilityJanitors[plr]:Add(task.delay(Stop.EFFECT_TIME, function()
		Stop.AbilityJanitors[plr]:Cleanup()
	end))
	
	Stop.AbilityJanitors[plr]:Add(TogeStopTrack.Ended:Connect(function()
		ClassExtension.Player.DisableMovement(plr, false)
		Stop.AbilityJanitors[plr]:Remove('Active')
	end))
	
	Stop.AbilityJanitors[plr]:Add(function()
		plr:SetAttribute(script.Name, nil)
		Animations:StopTrack(plr, 'Stop')
		ClassExtension.Player.DisableMovement(plr, false)
		
		Server.GetService('AbilityService').UpdatePassives(plr)
	end)
	
	return true
end


function Stop.CancelAbility(plr: Player)
	if not Stop.IsInUse(plr) then return end

	if Stop.AbilityJanitors[plr] then
		Stop.AbilityJanitors[plr]:Cleanup()
	end
end


return Stop