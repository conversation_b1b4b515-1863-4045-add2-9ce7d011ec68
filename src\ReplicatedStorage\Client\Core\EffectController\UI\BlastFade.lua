local TweenService = game:GetService('TweenService')

local EXTRA_PIXELS = 8

local MainInfo = TweenInfo.new(.3, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut)
return function(gui: GuiObject, TI: TweenInfo?, scale: number?)
	local Container = Instance.new('CanvasGroup')
	Container.Position = UDim2.fromScale(.5, .5)
	Container.Size = UDim2.fromOffset(gui.AbsoluteSize.X + EXTRA_PIXELS, gui.AbsoluteSize.Y + EXTRA_PIXELS)
	Container.AnchorPoint = Vector2.new(.5, .5)
	Container.BackgroundTransparency = 1
	Container.Parent = gui

	local clone = gui:Clone()

	clone.Position = UDim2.fromScale(.5, .5)
	clone.Size = UDim2.new(1, -EXTRA_PIXELS, 1, -EXTRA_PIXELS)
	clone.AnchorPoint = Vector2.new(.5, .5)
	clone.Parent = Container

	local foundUIScale = clone:FindFirstChildOfClass('UIScale')
	if foundUIScale then
		foundUIScale:Destroy()
	end

	local UIScale = Instance.new('UIScale')
	UIScale.Parent = Container

	TweenService:Create(
		UIScale,
		TI or MainInfo,
		{Scale = scale or 1.4}
	):Play()

	local t = TweenService:Create(
		Container,
		TI or MainInfo,
		{GroupTransparency = 1}
	);t:Play();

	t.Completed:Once(function()
		Container:Destroy()
	end)
end