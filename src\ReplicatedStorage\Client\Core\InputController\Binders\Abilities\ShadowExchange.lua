local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)
local Packets = require(ReplicatedStorage.Data.Packets)

local ShadowExchange = {
	Key = Enum.KeyCode.C,
	
	GamepadKey = Enum.KeyCode.ButtonL2
}


function ShadowExchange.KeyDown()
	Packets.Ability.UseAbility.send({
		AbilityName = script.Name,
		Args = {}
	})
end


function ShadowExchange.KeyUp()

end


function ShadowExchange.IsOnCooldown()
	return Client.GetController'AbilityController'.IsAbilityOnCooldown(script.Name)
end


function ShadowExchange.CanUseAbilityAsCharacter()
	return Client.GetController'AbilityController'.CanUseAbilityAsCharacter(script.Name)
end


function ShadowExchange._Init()

end


return ShadowExchange
