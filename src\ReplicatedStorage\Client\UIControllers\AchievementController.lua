local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local AchievementsData = require(ReplicatedStorage.Data.Achievements)

local AchievementController = {
	CurrentSelection = ''
}


local function refreshAchievement(data: { Name: string, Achievements: {{ Name: string, Claimed: boolean, Progress: number }} })
	local ItemController = Client.GetController('ItemController')
	
	local AchievementsFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Achievements
	
	local PrimaryAchievementFrame = AchievementsFrame.Content.Achivements.Slots.Scroller:FindFirstChild(data.Name)
	if not PrimaryAchievementFrame then
		local ThisPrimaryAchievementInfo = AchievementsData[data.Name]
		
		PrimaryAchievementFrame = ReplicatedStorage.Assets.Templates.PrimaryAchivementTemplate:Clone()
		PrimaryAchievementFrame.Name = data.Name
		PrimaryAchievementFrame.Details.Txt.Text = data.Name
		PrimaryAchievementFrame.Background.Image = ThisPrimaryAchievementInfo.BgImage
		PrimaryAchievementFrame.Parent = AchievementsFrame.Content.Achivements.Slots.Scroller
		
		PrimaryAchievementFrame.MouseButton1Click:Connect(function()
			AchievementController.SetPrimaryAchievementDisplay(data.Name)
		end)
	end
	
	local Completed = 0
	for _, secondaryAchievementData in data.Achievements do
		local ThisAchievementInfo = AchievementsData[data.Name].Secondary[secondaryAchievementData.Name]
		
		local SecondaryAchievementFrame = AchievementsFrame.Content.Tab.Slots.Scroller:FindFirstChild(secondaryAchievementData.Name)
		if not SecondaryAchievementFrame then
			SecondaryAchievementFrame = ReplicatedStorage.Assets.Templates.SecondaryAchievementTemplate:Clone()
			SecondaryAchievementFrame.Details.Description.Text = `{ThisAchievementInfo.Description} ({secondaryAchievementData.Progress}/{ThisAchievementInfo.Requirement})`
			SecondaryAchievementFrame.LayoutOrder = ThisAchievementInfo.Order
			SecondaryAchievementFrame.Name = secondaryAchievementData.Name
			SecondaryAchievementFrame.Details.ItemName.Text = secondaryAchievementData.Name
			SecondaryAchievementFrame.Visible = false
			SecondaryAchievementFrame.Parent = AchievementsFrame.Content.Tab.Slots.Scroller
			
			ItemController.CreateItem({
				ItemName = ThisAchievementInfo.Reward.Name,
				Count = ThisAchievementInfo.Reward.Count,
				Size = SecondaryAchievementFrame.Prize.Prize.Size,
				Position = SecondaryAchievementFrame.Prize.Prize.Position,
				AnchorPoint = SecondaryAchievementFrame.Prize.Prize.AnchorPoint,
				Parent = SecondaryAchievementFrame.Prize
			})
			
			SecondaryAchievementFrame.Prize.Prize:Destroy()
		end
		
		local CanClaim = secondaryAchievementData.Progress >= ThisAchievementInfo.Requirement
		
		SecondaryAchievementFrame:SetAttribute('CanClaim', CanClaim)
		SecondaryAchievementFrame:SetAttribute('Claimed', secondaryAchievementData.Claimed)
		SecondaryAchievementFrame:SetAttribute('Primary', data.Name)
		
		if CanClaim then
			Completed += 1
			
			if not secondaryAchievementData.Claimed then
				SecondaryAchievementFrame.Prize.Locked.Visible = true
			end
		end
	end
	
	PrimaryAchievementFrame:SetAttribute('Completed', Completed)
	PrimaryAchievementFrame:SetAttribute('Count', #data.Achievements)
	
	PrimaryAchievementFrame.Details.Description.Text = `Completion: {math.ceil(Completed/#data.Achievements*100)}%`
	
	if AchievementController.CurrentSelection == '' then
		AchievementController.SetPrimaryAchievementDisplay(data.Name)
	end
end


function AchievementController.SetPrimaryAchievementDisplay(primaryAchievementName: string)
	local AchievementsFrame = Players.LocalPlayer.PlayerGui.LobbyUI.Pages.Achievements
	local MainTab = AchievementsFrame.Content.Tab
	
	local PrimaryAchievementFrame = AchievementsFrame.Content.Achivements.Slots.Scroller[primaryAchievementName]
	local LastPrimaryAchievementFrame = AchievementsFrame.Content.Achivements.Slots.Scroller:FindFirstChild(AchievementController.CurrentSelection)
	
	AchievementController.CurrentSelection = primaryAchievementName
	
	if LastPrimaryAchievementFrame then
		LastPrimaryAchievementFrame.Stroke.IsSelected.Enabled = false
		LastPrimaryAchievementFrame.Stroke.NotSelected.Enabled = true
	end
	
	PrimaryAchievementFrame.Stroke.IsSelected.Enabled = true
	PrimaryAchievementFrame.Stroke.NotSelected.Enabled = false

	-- TODO: Update main tab
	for _, v in MainTab.Slots.Scroller:GetChildren() do
		if v:IsA'ImageLabel' then
			v.Visible = v:GetAttribute'Primary' == primaryAchievementName
		end
	end
	
	MainTab.Task.ProgressBar.Txt.Text = `Progress: {PrimaryAchievementFrame:GetAttribute'Completed'}/{PrimaryAchievementFrame:GetAttribute'Count'}`
	
	local progressRatio = PrimaryAchievementFrame:GetAttribute'Completed' / PrimaryAchievementFrame:GetAttribute'Count'
	MainTab.Task.ProgressBar.Holder.Loader.UIGradient.Transparency = NumberSequence.new{
		NumberSequenceKeypoint.new(0, 0),
		NumberSequenceKeypoint.new(math.clamp(progressRatio, .001, .998), 0),
		NumberSequenceKeypoint.new(math.clamp(progressRatio + .001, .002, .999), 1),
		NumberSequenceKeypoint.new(1, 1),
	}
end


function AchievementController._Start()
	local AchievementsFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'LobbyUI'.Pages.Achievements

	for _, v in AchievementsFrame.Content.Tab.Slots.Scroller:GetChildren() do
		if v:IsA'ImageLabel' then v:Destroy() end
	end
	
	for _, v in AchievementsFrame.Content.Achivements.Slots.Scroller:GetChildren() do
		if v:IsA'ImageButton' then v:Destroy() end
	end
	
	Packets.Achievements.RequestAchievementData.listen(refreshAchievement)
	Packets.Achievements.RequestAchievementData.send()
end


return AchievementController