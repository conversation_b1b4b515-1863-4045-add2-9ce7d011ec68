local ReplicatedStorage = game:GetService('ReplicatedStorage')
local Players = game:GetService('Players')
local StarterGui = game:GetService('StarterGui')
local TweenService = game:GetService('TweenService')
local ContentProvider = game:GetService('ContentProvider')
local RunService = game:GetService('RunService')
local TextChatService = game:GetService('TextChatService')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local Signal = require(ReplicatedStorage.SharedModules.Signal)

local LoadingController = {
	LoadingFrameFadedAway = Signal.new(),
	IsReady = false
}

local PlayersLoaded = Signal.new()


function LoadingController.YieldUntilLoaded()
	if not workspace:GetAttribute('PlayersLoaded') then
		PlayersLoaded:Wait()
	end
	
	if not workspace:GetAttribute('ClientReady') then
		workspace:GetAttributeChangedSignal'ClientReady':Wait()
	end
end


function LoadingController._Start()
	LoadingController.YieldUntilLoaded()
	LoadingController.IsReady = true
end


function LoadingController._Init()
	if not RunService:IsStudio() then
		StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.Chat, false)
		
		task.spawn(function()
			local LoadingFrame = script:WaitForChild'Loading':Clone()
			LoadingFrame.LoadingText.Visible = false
			LoadingFrame.Parent = Players.LocalPlayer:WaitForChild'PlayerGui'
			
			task.spawn(ContentProvider.PreloadAsync, ContentProvider, {LoadingFrame.VideoFrame, LoadingFrame.VideoFrame.Video})
			--ContentProvider:PreloadAsync{LoadingFrame.VideoFrame, LoadingFrame.VideoFrame.Video}
			
			LoadingFrame.VideoFrame.TimePosition = 0
			LoadingFrame.VideoFrame:Play()
			
			local c c = RunService.RenderStepped:Connect(function(dt: number)
				if LoadingFrame.VideoFrame.TimePosition >= 7.5 then
					if LoadingController.IsReady == false then
						LoadingFrame.LoadingText.Visible = true
						LoadingFrame.VideoFrame:Pause()
					end
					c:Disconnect()
				end
			end)
			
			LoadingFrame.VideoFrame.Ended:Once(function()
				c:Disconnect()
				
				LoadingFrame.LoadingText:Destroy()
				LoadingFrame.VideoFrame:Destroy()

				local t = TweenService:Create(
					LoadingFrame.Frame,
					TweenInfo.new(.7, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut),
					{ BackgroundTransparency = 1 }
				);t:Play();

				t.Completed:Once(function()
					LoadingController.LoadingFrameFadedAway:Fire()
					StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.Chat, true)
					LoadingFrame:Destroy()
				end)
			end)
			
			LoadingController.YieldUntilLoaded()

			if LoadingFrame.VideoFrame.TimePosition >= 7.5 then
				LoadingFrame.VideoFrame.Volume = 0
				LoadingFrame.VideoFrame:Play()
			end
		end)
	end
	
	local ServerType = Client.Immutable.SERVER_TYPE
	
	Packets.Loading.PlayersLoaded.listen(function()PlayersLoaded:Fire()end)
	Packets.Loading.PlayersLoaded.send()
	
	task.spawn(function() -- gui stuff
		local PlayerGui = Players.LocalPlayer:WaitForChild('PlayerGui')
		
		PlayerGui:WaitForChild('GameplayUI')
		PlayerGui:WaitForChild('LobbyUI')
		PlayerGui:WaitForChild('AfkChamber')
		
		if ServerType == 'Round' then
			PlayerGui.GameplayUI.Enabled = true
			PlayerGui.LobbyUI.Enabled = false
			PlayerGui.AfkChamber.Enabled = false
		elseif ServerType == 'Lobby' then
			PlayerGui.GameplayUI.Enabled = false
			PlayerGui.LobbyUI.Enabled = true
			PlayerGui.AfkChamber.Enabled = false
		elseif ServerType == 'AFK' then
			PlayerGui.GameplayUI.Enabled = false
			PlayerGui.LobbyUI.Enabled = false
			PlayerGui.AfkChamber.Enabled = true
		end
		
		PlayerGui.ChildAdded:Connect(function(c: Instance)
			if c.Name == 'Freecam' then c:Destroy() end
		end)
	end)
	
	if ServerType == 'Lobby' then
		TextChatService.ChatWindowConfiguration.HorizontalAlignment = Enum.HorizontalAlignment.Left
	end
end


return LoadingController
