local ReplicatedStorage = game:GetService('ReplicatedStorage')
local ServerScriptService = game:GetService('ServerScriptService')
local TweenService = game:GetService('TweenService')
local SoundService = game:GetService('SoundService')
local Debris = game:GetService('Debris')

local Server = require(ServerScriptService.Server)

local Janitor = require(ReplicatedStorage.SharedModules.Janitor)
local Animations = require(ReplicatedStorage.SharedModules.Animations.Package.AnimationsServer)
local VFXFunctions = require(ReplicatedStorage.SharedModules.VFXFunctions)
local Packets = require(ReplicatedStorage.Data.Packets)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)
local CookingService = Server.GetService('CookingService')

local ARROW_SPEED = 110
local BURN_CHANCE = 1/35
local FLAME_TIME = 3

local Fuga = {
	COOLDOWN = 10,
	
	Cooldowns = {} :: { [Player]: number },
	AbilityJanitors = {} :: { [Player]: Janitor.Janitor }
}


function Fuga.GetCooldownTimeLeft(plr: Player): number
	if Fuga.Cooldowns[plr] then
		return Fuga.COOLDOWN - (os.clock() - Fuga.Cooldowns[plr])
	end
	return 0
end


function Fuga.CanUse(plr: Player): boolean
	if Fuga.Cooldowns[plr] then
		return os.clock() - Fuga.Cooldowns[plr] > Fuga.COOLDOWN
	end
	
	return true
end


function Fuga.IsInUse(plr: Player): boolean
	if Fuga.AbilityJanitors[plr] and Fuga.AbilityJanitors[plr]:Get('Active') then
		return true
	end
	
	return false
end


function Fuga.UseAbility(plr: Player, mouseHit: Vector3): boolean
	if not Fuga.CanUse(plr) or Fuga.IsInUse(plr) then return false end

	Fuga.Cooldowns[plr] = os.clock()
	
	task.delay(Fuga.COOLDOWN, function()
		Fuga.Cooldowns[plr] = nil
	end)
	
	if Fuga.AbilityJanitors[plr] then
		Fuga.AbilityJanitors[plr]:Destroy()
	end
	
	Fuga.AbilityJanitors[plr] = Janitor.new()
	
	Fuga.AbilityJanitors[plr]:Add(math.random, nil, 'Active')
	
	ClassExtension.Player.DisableMovement(plr, true)
	plr:SetAttribute('FugaFollowMouse', true)
	
	mouseHit += Vector3.new(0, 3, 0)
	
	local lookAt = Vector3.new(mouseHit.X, plr.Character.PrimaryPart.CFrame.Y, mouseHit.Z)
	plr.Character.PrimaryPart.CFrame = CFrame.lookAt(plr.Character.PrimaryPart.Position, lookAt)

	local FugaArrowTrack: AnimationTrack = Animations:PlayTrack(plr, 'FugaFlameArrow')
	FugaArrowTrack:AdjustSpeed(1.35)
	
	SoundService.SFX.Abilities.FugaStart:Play()
	
	local ground = Fuga.AbilityJanitors[plr]:Add(script.Ground.Attachment:Clone())
	ground.Parent = plr.Character.PrimaryPart
	VFXFunctions.EnableDescendants(ground)
	
	Fuga.AbilityJanitors[plr]:Add(FugaArrowTrack:GetMarkerReachedSignal'Shoot':Once(function()
		local firing = Fuga.AbilityJanitors[plr]:Add(script.Firing.Attachment:Clone())
		firing.Parent = plr.Character.PrimaryPart
		VFXFunctions.EmitDescendants(firing)

		local arrow = Fuga.AbilityJanitors[plr]:Add(script.Arrow:Clone())
		arrow.CFrame = CFrame.lookAt(plr.Character:GetPivot().Position, mouseHit) * CFrame.Angles(math.pi, 0, 0) * CFrame.new(0, 0, -1.5)
		arrow.Parent = workspace

		local dist = (arrow.Position - mouseHit).Magnitude
		local t = TweenService:Create(
			arrow,
			TweenInfo.new(dist/ARROW_SPEED, Enum.EasingStyle.Linear, Enum.EasingDirection.In),
			{Position = mouseHit}
		)

		Fuga.AbilityJanitors[plr]:Add(t.Completed:Once(function(playbackState: Enum.PlaybackState)
			if playbackState == Enum.PlaybackState.Completed then
				Packets.EffectController.Do.sendToAll({
					Effect = 'SmallHit',
					Args = {}
				})

				plr:SetAttribute('FugaFollowMouse', false)

				SoundService.SFX.Abilities.FugaBlast:Play()

				ClassExtension.Player.DisableMovement(plr, false)
				arrow:Destroy()

				local interactablesInRegion = CookingService.GetInteractablesInRegion(plr.Character.PrimaryPart.Position, 20)
				for _, interactable in interactablesInRegion do
					local ingredient = CookingService.GetIngredientOnInteractable(interactable)
					if ingredient then
						local burnt = math.random() <= BURN_CHANCE
						CookingService.KitchenwareModules.Stove[burnt and 'SetIngredientAsBurnt' or 'SetIngredientAsCooked'](ingredient)
					end
				end

				local explosion = Fuga.AbilityJanitors[plr]:Add(script.Explosion:Clone())
				explosion.Position = mouseHit
				explosion.Parent = workspace
				VFXFunctions.EmitDescendants(explosion)

				Fuga.AbilityJanitors[plr]:Remove('Active')

				task.wait(VFXFunctions.GetHighestWaitTime(explosion))
			end

			Fuga.AbilityJanitors[plr]:Cleanup()
		end))

		Fuga.AbilityJanitors[plr]:Add(function()
			t:Cancel()
		end)

		t:Play()

		VFXFunctions.DisableDescendants(ground)
	end))
	
	Fuga.AbilityJanitors[plr]:Add(function()
		plr:SetAttribute('FugaFollowMouse', false)
		ClassExtension.Player.DisableMovement(plr, false)
		Animations:StopTrack(plr, 'FugaFlameArrow')
	end)
	
	return true
end


function Fuga.CancelAbility(plr: Player)
	if not Fuga.IsInUse(plr) then return end

	if Fuga.AbilityJanitors[plr] then
		Fuga.AbilityJanitors[plr]:Cleanup()
	end
end


return Fuga