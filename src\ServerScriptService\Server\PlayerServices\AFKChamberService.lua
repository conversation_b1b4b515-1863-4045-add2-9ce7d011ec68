local ServerScriptService = game:GetService('ServerScriptService')
local TeleportService = game:GetService('TeleportService')
local ReplicatedStorage = game:GetService('ReplicatedStorage')
local HttpService = game:GetService('HttpService')
local Players = game:GetService('Players')

local Server = require(ServerScriptService.Server)
local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local SimpleMarketplace = require(ReplicatedStorage.Data.SimpleMarketplace)
local ClassExtension = require(ReplicatedStorage.SharedModules.ClassExtension)

local AFKChamberService = {
	PlayersInChambers = {}
}


function AFKChamberService.IsPlayerInAFKChamber(plr: Player)
	return AFKChamberService.PlayersInChambers[plr] ~= nil
end


function AFKChamberService.ChamberEntered(plr: Player)
	local CurrencyService = Server.GetService('CurrencyService')
	
	if AFKChamberService.IsPlayerInAFKChamber(plr) then return end
	
	local ThisChamberID = HttpService:GenerateGUID(false)
	AFKChamberService.PlayersInChambers[plr] = {
		ID = ThisChamberID,
		CoinsGained = 0,
		GemsGained = 0
	}
	
	ClassExtension.Player.DisableMovement(plr, true)
	
	task.spawn(function()
		while AFKChamberService.PlayersInChambers[plr] and AFKChamberService.PlayersInChambers[plr].ID == ThisChamberID do
			for i = 120, 0, -.1 do
				if not AFKChamberService.PlayersInChambers[plr] or AFKChamberService.PlayersInChambers[plr].ID ~= ThisChamberID then break end
				
				pcall(function()
					Packets.AFK.UpdateAFKChamberInfo.sendTo({
						NextPayout = i,
						CoinsGained = AFKChamberService.PlayersInChambers[plr].CoinsGained,
						GemsGained = AFKChamberService.PlayersInChambers[plr].GemsGained
					}, plr)
				end)
				
				task.wait(.1)
			end
			
			if AFKChamberService.PlayersInChambers[plr] and AFKChamberService.PlayersInChambers[plr].ID == ThisChamberID then
				local mult = 1
					+ (plr.MembershipType == Enum.MembershipType.Premium and 1.5 or 0)
					+ (SimpleMarketplace.UserOwnsGamePassAsync(plr.UserId, SimpleMarketplace.Gamepasses['VIP'].ID) and 2 or 0)
				CurrencyService.AddGems(plr, Client.Immutable.AFK.GEMS_PER_30_SEC * mult, 'AFKChamber')
				CurrencyService.AddCoins(plr, Client.Immutable.AFK.COINS_PER_30_SEC * mult, 'AFKChamber')
				
				AFKChamberService.PlayersInChambers[plr].CoinsGained += Client.Immutable.AFK.COINS_PER_30_SEC * mult
				AFKChamberService.PlayersInChambers[plr].GemsGained += Client.Immutable.AFK.GEMS_PER_30_SEC * mult
			end
			
			task.wait()
		end
	end)
end


function AFKChamberService._PlayerRemoving(plr: Player)
	AFKChamberService.PlayersInChambers[plr] = nil
end


function AFKChamberService._PlayerAdded(plr: Player)
	local joinData = plr:GetJoinData()
	
	if Client.Immutable.SERVER_TYPE == 'AFK' then
		AFKChamberService.ChamberEntered(plr)
		
		if joinData and joinData.TeleportData and joinData.TeleportData.AFK then
			AFKChamberService.PlayersInChambers[plr].CoinsGained = joinData.TeleportData.CoinsGained or 0
			AFKChamberService.PlayersInChambers[plr].GemsGained = joinData.TeleportData.GemsGained or 0
		end
	elseif Client.Immutable.SERVER_TYPE == 'Lobby' then
		local joinData = plr:GetJoinData()
		if joinData and joinData.TeleportData and joinData.TeleportData.AFK then
			local afkPlaceId
			for placeId, placeName in Client.Immutable.Servers.PLACES[game.GameId] do
				if placeName == 'AFK' then
					afkPlaceId = placeId
					break
				end
			end
			
			TeleportService:Teleport(afkPlaceId, plr, joinData.TeleportData)
		end
	end
end


function AFKChamberService._Start()
	local DataService = Server.GetService('DataService')
	
	if Client.Immutable.SERVER_TYPE == 'AFK' then
		Packets.AFK.LeaveAFKChamber.listen(function(reconnect: boolean, plr: Player)
			local lobbyPlaceId
			for placeId, placeName in Client.Immutable.Servers.PLACES[game.GameId] do
				if placeName == 'Lobby' then
					lobbyPlaceId = placeId
					break
				end
			end

			local tpData = reconnect and {
				AFK = true,
				CoinsGained = AFKChamberService.PlayersInChambers[plr].CoinsGained,
				GemsGained = AFKChamberService.PlayersInChambers[plr].GemsGained
			}

			TeleportService:Teleport(lobbyPlaceId, plr, tpData)
		end)
	end
end


return AFKChamberService