local ReplicatedStorage = game:GetService('ReplicatedStorage')
local UserInputService = game:GetService('UserInputService')
local TeleportService = game:GetService('TeleportService')
local Players = game:GetService('Players')
local MarketplaceService = game:GetService('MarketplaceService')

local Client = require(ReplicatedStorage.Client)

local Packets = require(ReplicatedStorage.Data.Packets)
local SimpleMarketplace = require(ReplicatedStorage.Data.SimpleMarketplace)
local Math = require(ReplicatedStorage.SharedModules.Math)

local AFKChamberController = {}


local function updateChamberInfo(data: { CoinsGained: number, GemsGained: number, NextPayout: number })
	local AFKFrame = Players.LocalPlayer.PlayerGui.AfkChamber
	
	data.NextPayout = string.format('%.1f', data.NextPayout)
	
	AFKFrame.Contents.Payout.Text = `NEXT PAYOUT: <font color="#ffde00">{data.NextPayout}s</font>`
	AFKFrame.Contents.Gems.Container.Contents.Amount.Text = Math.Comma(data.GemsGained)
	AFKFrame.Contents.Coins.Container.Contents.Amount.Text = Math.Comma(data.CoinsGained)
end


local function updateDescriptions()
	local AFKFrame = Players.LocalPlayer.PlayerGui.AfkChamber
	
	local isPremium = Players.LocalPlayer.MembershipType == Enum.MembershipType.Premium
	local isVIP = SimpleMarketplace.UserOwnsGamePassAsync(Players.LocalPlayer.UserId, SimpleMarketplace.Gamepasses['VIP'].ID)
	
	local mult = 1
		+ (isPremium and 1.5 or 0)
		+ (isVIP and 2 or 0)
	AFKFrame.Contents.Gems.Description.Text = `YOU GET {math.floor(Client.Immutable.AFK.GEMS_PER_30_SEC * mult)}x GEMS EVERY INTERVAL`
	AFKFrame.Contents.Coins.Description.Text = `YOU GET {math.floor(Client.Immutable.AFK.COINS_PER_30_SEC * mult)}x COINS EVERY INTERVAL`
	
	AFKFrame.Contents.PremiumBtn.Visible = not isPremium
	AFKFrame.Contents.VIPBtn.Visible = not isVIP
end


function AFKChamberController._Start()
	UserInputService.WindowFocused:Connect(function()
		Packets.AFK.AFK.send(true)
	end)
	
	UserInputService.WindowFocusReleased:Connect(function()
		Packets.AFK.AFK.send(false)
	end)
	
	if Client.Immutable.SERVER_TYPE == 'AFK' then
		local AFKFrame = Players.LocalPlayer:WaitForChild'PlayerGui':WaitForChild'AfkChamber'
		
		Packets.AFK.UpdateAFKChamberInfo.listen(updateChamberInfo)
		
		AFKFrame.Contents.Return.MouseButton1Click:Connect(function()
			Packets.AFK.LeaveAFKChamber.send(false)
		end)
		
		Players.LocalPlayer.Idled:Connect(function(idleTime: number)
			if idleTime > 60 * 18 then
				Packets.AFK.LeaveAFKChamber.send(true)
			end
		end)
		
		task.defer(updateDescriptions)
		
		Players.PlayerMembershipChanged:Connect(function(player: Player)
			if player ~= Players.LocalPlayer then return end
			updateDescriptions()
		end)
		
		Packets.Store.OwnedGamepasses.listen(updateDescriptions)
		
		AFKFrame.Contents.PremiumBtn.MouseButton1Click:Connect(function()
			MarketplaceService:PromptPremiumPurchase(Players.LocalPlayer)
		end)
		
		AFKFrame.Contents.VIPBtn.MouseButton1Click:Connect(function()
			MarketplaceService:PromptGamePassPurchase(Players.LocalPlayer, SimpleMarketplace.Gamepasses['VIP'].ID)
		end)
	end
end


return AFKChamberController